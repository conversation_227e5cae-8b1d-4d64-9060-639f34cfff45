# -*- coding: utf-8 -*-
import numpy as np
import pandas as pd
from datetime import datetime
import os
import re
from module import TradeDate, PathManager


def fund_net_file(x, y, filepath):
    col_name = ['JJJC', 'JJDM', 'SCJZ', 'JYSC', 'JYRQ', 'SBDM', 'ZQLB']
    values = [['', 'SAZK78', x[1], '', '', '', ''], ['', 'SZW641', y[1], '', '', '', '']]  # 导入模板
    fund_detail = pd.DataFrame(values, columns=col_name)
    fund_detail.to_excel(os.path.join(filepath, 'FundPost.xlsx'), index=False)
    fundclname = ['资产净值', '基金净值', '市值']
    fundvalue = [y, x]
    fundfile = pd.DataFrame(fundvalue, columns=fundclname)
    fundfile.to_excel(os.path.join(filepath, 'Fund.xlsx'), index=False)


def fund_detail_file(x, y, filepath, z):
    today = datetime.today().strftime('%Y%m%d')
    if 'CTA' in y:
        fund_detail_dict = {
            '持仓明细序号': 0,
            '账户名称': '场外权益产品',
            '组合名称': y,
            '市场': x.loc[:, '市场'].to_list(),
            '证券代码': x.loc[:, '证券代码'].to_list(),
            '股东代码': '',
            '托管席位': '',
            '多空标志': x.loc[:, '多空'].to_list(),
            '投资类型': '',
            '目标组合': '',
            '目标投资类型': '',
            '业务类型': '新增开仓',
            '发生数量': x.loc[:, '数量'].to_list(),
            '价格': x.loc[:, '单位成本'].to_list(),
            '操作生效日期': today,
            '调整成本': '',
            '调整类型': '',
            '开仓日期': today,
            '开仓时间': '9:15:00',
            '成交编号': '',
            '备注': ''

        }
        fund_detail = pd.DataFrame(fund_detail_dict)
        fund_detail.fillna(method='ffill', inplace=True)
        file_path = os.path.join(filepath, ('FUTURE_' + today + '_01.xlsx'))
        if os.path.exists(file_path):
            data_src = pd.read_excel(file_path)  # 读取已有的excel文件
            fund_detail = pd.concat([data_src, fund_detail], ignore_index=True)  # 拼接新的数据
            fund_detail.to_excel(file_path, index=False)  # 保存到excel文件
        else:
            fund_detail.to_excel(file_path, index=False)
    else:
        fund_detail_dict = {
            '账户名称': '场外权益产品',
            '投资组合名称': y,
            '市场': x.loc[:, '市场'].to_list(),
            '证券代码': x.loc[:, '证券代码'].to_list(),
            '股东代码': '',
            '托管席位': '',
            '投资类型': '',
            '目标组合名称': '',
            '目标组合类型': '',
            '业务类型': '证券增加',
            '发生数量': x.loc[:, '数量'].to_list(),
            '成本价格': x.loc[:, '单位成本'].to_list(),
            '操作生效日期': today,
            '操作失效日期': '',
            '备注': '',
            '调整类型': 1,
            '变动成本': '',
            '变动收益': '',
            '多空标志': x.loc[:, '多空'].to_list(),
            '是否影响标准券': '',
            '组合编号': '',
            '目标组合编号': '',
            '成本调整类型': '',
            '人民币变动成本': '',
            '收益调整类型': '',
            '人民币变动收益': ''
        }
        fund_detail = pd.DataFrame(fund_detail_dict)
        fund_detail.fillna(method='ffill', inplace=True)
        fund_detail.to_excel(os.path.join(filepath, ('STOCK_' + today + '_' + z + '.xlsx')), index=False)


def CTA_file(x, y, z):
    try:
        fund = pd.read_excel(x, skiprows=3)
        value = fund.loc[fund['科目代码'].str.contains('基金资产净值|基金单位净值', na=False), '市值'].to_list()
        value.append(0)
        ctaPortf(fund, y, z)
        return value
    except:
        return ['', '', '']


def T0_file(x, y, z):
    try:
        fund = pd.read_excel(x, skiprows=2)
        value = fund.loc[fund['科目代码'].str.contains('基金单位净值', na=False), '科目名称'].values[0]
        value1 = fund.loc[fund['科目代码'].str.contains('基金资产净值', na=False), '市值'].values[0]
        # value2 = fund.loc[fund['科目名称'].str.contains('交易性股票投资', na=False), '市值'].values[0]
        # stockPortf(fund, y, z)
        return [value1, value, 0]
    except:
        return ['', '', '']


def filepath():
    srcpath = "D:\\OneDrive\\工作\\财信证券\\1.风控报表\\1.日报\\"
    files_path = list()
    date = TradeDate()
    filepath = PathManager()
    filename = [
        date.get_trade_date(1, '%Y%m%d') + '_(SAZK78)财信期货多策略CTA3号单一资产管理计划_证券投资基金估值表.xls',
        date.get_trade_date(1, '%Y%m%d') + '_(SZW641)财信期货多策略CTA2号单一资产管理计划_证券投资基金估值表.xls',
        date.get_trade_date(1, '%Y-%m-%d') + '_SACD35_翰荣双子量化专享五号私募证券投资基金估值表.xlsx',
        'funddetail.xlsx']
    files_path.append(filepath.get_full_path(srcpath, date.get_trade_date(1, '%m%d'), filename[0]))
    files_path.append(filepath.get_full_path(srcpath, date.get_trade_date(1, '%m%d'), filename[1]))
    files_path.append(filepath.get_full_path(srcpath, date.get_trade_date(1, '%m%d'), filename[2]))
    files_path.append(filepath.get_full_path(srcpath, date.get_trade_date(0, '%m%d')))
    files_path.append(filepath.get_full_path(srcpath, date.get_trade_date(1, '%m%d')))
    files_path.append(filepath.get_full_path(srcpath, date.get_trade_date(2, '%m%d'), filename[3]))
    return files_path


# T0估值表穿透持仓提取
def stockPortf(df, y, z):
    df.loc[df['科目名称'].str.contains('上交所', na=False), '市场'] = '上交所A'
    df.loc[df['科目名称'].str.contains('深交所', na=False), '市场'] = '深交所A'
    df['市场'].fillna(method='ffill', inplace=True)
    df['多空'] = ''
    df = df.loc[df['停牌信息'].notnull(), :]
    df['证券代码'] = df['科目代码'].apply(lambda x: x[-6:])
    df = df.loc[:, ('证券代码', '数量', '单位成本', '市场', '多空')]
    # print(df)
    fund_detail_file(df, '翰荣双子量化专享五号私募证券投资基金', z, y)
    return 0


# 期权正则[A-Za-z]{1,2}\d{3,4}-?[CcPp]-?\d{3,}
# 期货正则[A-Za-z]{1,2}\d{3,4}
def symbolGet(x):
    if re.findall(r'[A-Za-z]{1,2}\d{3,4}-?[CcPp]-?\d{3,}', str(x)):  # 提取期权合约代码
        return re.findall(r'[A-Za-z]{1,2}\d{3,4}-?[CcPp]-?\d{3,}', str(x))[-1]
    elif re.findall(r'[A-Za-z]{1,2}\d{3,4}', str(x)):  # 提取期货合约代码
        return re.findall(r'[A-Za-z]{1,2}\d{3,4}', str(x))[-1]
    else:
        return 0


def ctaPortf(df, y, z):
    # 期权多空匹配
    df.loc[df['科目名称'].str.contains('权利方', na=False), '多空'] = '权利仓'
    df.loc[df['科目名称'].str.contains('义务方', na=False), '多空'] = '义务仓'
    # 期货多空匹配
    df.loc[df['科目名称'].str.contains('多头', na=False), '多空'] = '多仓'
    df.loc[df['科目名称'].str.contains('空头', na=False), '多空'] = '空仓'
    df['证券代码'] = df['科目代码'].apply(lambda x: symbolGet(x))  # 正则提取合约代码
    b = 0
    for i in ['中金所', '上海', '郑州', '大连', '广州']:
        a = ['中金所', '上期所', '郑商所', '大商所', '广期所']
        df.loc[df['科目名称'].str.contains(i, na=False), '市场'] = a[b]
        b += 1
    df.loc[df['科目名称'].str.contains('上海能源', na=False), '市场'] = '能源中心'
    df['市场'].fillna(method='ffill', inplace=True)
    df['多空'].fillna(method='ffill', inplace=True)
    df = df.loc[df['停牌信息'].notnull(), :]
    df = df.loc[:, ('证券代码', '数量', '单位成本', '市场', '多空')]
    # print(df)
    fund_detail_file(df, '财信期货多策略CTA' + y + '号单一资产管理计划', z, '0' + y)
    return 0


def main():
    files_path = filepath()
    pathmk = PathManager()
    if os.name != 'posix':
        # print('Not Linux')
        pathmk.ensure_dir(files_path[3])
    fund1 = CTA_file(files_path[0], '1', files_path[4])
    fund2 = CTA_file(files_path[1], '2', files_path[4])
    # fund3 = T0_file(files_path[2], '05', files_path[4])

    fund_net_file(fund1, fund2, files_path[4])
    # os.system('pause')


if __name__ == '__main__':
    main()
