# -*- coding: utf-8 -*-

import pandas as pd


def bond1():
    df = pd.read_excel('固收部持券2507.xlsx')
    df.dropna(axis=0, subset='市场类型', inplace=True)
    # 按利率债、信用债拆分
    llz = df[df['所属证券池'].str.contains('利率债', na=False)]
    xyz = df[df['所属证券池'].str.contains('信用债|禁止', na=False)]
    # 上交所、深交所、银行间利率债/信用债持仓市值
    a1 = llz.groupby(by='市场类型').sum()['持仓(万元)'].to_list()
    a2 = xyz.groupby(by='市场类型').sum()['持仓(万元)'].to_list()
    # 上交所、深交所、银行间利率债/信用债借入面额
    b1 = llz.groupby(by='市场类型').sum()['借入面额(万元)'].to_list()
    b2 = xyz.groupby(by='市场类型').sum()['借入面额(万元)'].to_list()
    # 银行间回购面额
    c1 = llz.groupby(by='市场类型').sum()['回购面额(万元)'].to_list()
    c2 = xyz.groupby(by='市场类型').sum()['回购面额(万元)'].to_list()
    print(a1, a2, b1, b2, c1, c2)


def bond2():
    df = pd.read_excel('正回购.xlsx')
    df.dropna(axis=0, subset='市场', inplace=True)
    df = df[~(df['账户'] == "深交所报价回购专户")]
    result = df.groupby(by='市场').sum()['融入金额']
    result = [int(x) / 100000000 for x in result]
    print(result)
    # 上交所、深交所、银行间正回购金额
    return result


def bond3():
    df = pd.read_excel('综合信息查询_质押券.xls')
    df.dropna(axis=0, subset='持仓日期', inplace=True)
    result = df.groupby(by='交易市场').sum()['已质押数量'].to_list()
    # result=result['已质押数量'].to_list()
    result = [int(x) / 1000000 for x in result]
    print(result)
    # 上交所、深交所质押券数据
    return result


def bond4():
    df = pd.read_excel('2.数据提供母公司202507-TO资管及固收.xlsx')
    df = df[df.iloc[:, 0].str.contains('总资产|净资产|总负债', na=False)]
    result = df.iloc[:, 1].to_list()
    result = [int(x) / 100000000 for x in result]
    a = df[df.iloc[:, 0] == '总资产']
    b = df[df.iloc[:, 0] == '净资产']
    pct = a.iloc[:, 1].values[0] / b.iloc[:, 1].values[0]
    result.append(pct)
    print(result)
    # 总资产、净资产、总负债、母公司杠杆比率
    return result


def pctCalc(x, y):
    # x,自由债券量；y,正回购面额
    return x / (x - y)


if __name__ == '__main__':
    bond1()
    bond2()
    bond3()
    bond4()
