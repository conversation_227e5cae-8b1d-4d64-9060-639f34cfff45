# -*- coding: utf-8 -*-
"""
基础统计指标模块
包括收益率计算、波动性指标和回撤分析等功能
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
from typing import Union, Optional, Tuple, List


def calculate_actual_returns(nav_series: pd.Series) -> float:
    """
    计算实际收益率（总收益率）
    
    参数:
        nav_series: 净值序列，索引为日期
        
    返回:
        实际收益率（总收益率）
    """
    # 去除NaN值
    nav_series_clean = nav_series.dropna()
    if len(nav_series_clean) < 2:
        return np.nan
    
    # 计算总收益率
    total_return = nav_series_clean.iloc[-1] / nav_series_clean.iloc[0] - 1
    
    return total_return


def calculate_returns(nav_series: pd.Series,
                     period: str = 'daily',
                     annualized: bool = False) -> float:
    """
    计算收益率
    
    参数:
        nav_series: 净值序列，索引为日期
        period: 数据频率，'daily'或'monthly'
        annualized: 是否年化
        
    返回:
        收益率
    """
    # 去除NaN值
    nav_series_clean = nav_series.dropna()
    if len(nav_series_clean) < 2:
        return np.nan
    
    # 计算总收益率
    total_return = nav_series_clean.iloc[-1] / nav_series_clean.iloc[0] - 1
    
    # 如果不需要年化，直接返回总收益率
    if not annualized:
        return total_return
    
    # 计算实际的时间跨度（天数）
    if hasattr(nav_series_clean.index, 'to_pydatetime'):
        start_date = nav_series_clean.index[0]
        end_date = nav_series_clean.index[-1]
        days = (end_date - start_date).days
        years = days / 365.25  # 考虑闰年
    else:
        years = len(nav_series_clean) / (252 if period == 'daily' else 12)
    
    if years <= 0:
        return np.nan
    
    # 对于时间跨度过短的数据，年化收益率可能会产生误导
    # 如果时间跨度少于3个月（0.25年），给出警告但仍计算
    if years < 0.25:
        import warnings
        warnings.warn(f"时间跨度过短({years:.3f}年)，年化收益率可能不具有参考意义", UserWarning)
        
    annual_return = (1 + total_return) ** (1 / years) - 1
    
    return annual_return


def calculate_rolling_returns(nav_series: pd.Series,
                              window: int = 20) -> pd.Series:
    """
    计算滚动收益率
    
    参数:
        nav_series: 净值序列，索引为日期
        window: 滚动窗口大小
        
    返回:
        滚动收益率序列
    """
    # 计算滚动收益率
    rolling_returns = nav_series.rolling(window=window).apply(
        lambda x: x[-1] / x[0] - 1
    )
    
    return rolling_returns


def calculate_volatility(returns_series: pd.Series,
                        period: str = 'daily',
                        window: Optional[int] = None,
                        annualized: bool = False) -> Union[float, pd.Series]:
    """
    计算波动率
    
    参数:
        returns_series: 收益率序列，索引为日期
        period: 数据频率，'daily'或'monthly'
        window: 滚动窗口大小，如果提供则计算滚动波动率
        annualized: 是否年化
        
    返回:
        如果window为None，返回总体波动率
        否则返回滚动波动率序列
    """
    # 年化因子
    annualization_factor = np.sqrt(252 if period == 'daily' else 12)
    
    # 计算滚动波动率
    if window is not None:
        volatility = returns_series.rolling(window=window).std()
        if annualized:
            volatility = volatility * annualization_factor
        return volatility
    
    # 计算总体波动率
    volatility = returns_series.std()
    if annualized:
        volatility = volatility * annualization_factor
    
    return volatility


def calculate_downside_volatility(returns_series: pd.Series,
                                period: str = 'daily',
                                min_acceptable_return: float = 0,
                                window: Optional[int] = None,
                                annualized: bool = False) -> Union[float, pd.Series]:
    """
    计算下行波动率
    
    参数:
        returns_series: 收益率序列，索引为日期
        period: 数据频率，'daily'或'monthly'
        min_acceptable_return: 最小可接受收益率
        window: 滚动窗口大小，如果提供则计算滚动下行波动率
        annualized: 是否年化
        
    返回:
        如果window为None，返回总体下行波动率
        否则返回滚动下行波动率序列
    """
    # 年化因子
    annualization_factor = np.sqrt(252 if period == 'daily' else 12)
    
    # 计算下行收益率
    downside_returns = returns_series.copy()
    downside_returns[downside_returns > min_acceptable_return] = 0
    
    # 计算滚动下行波动率
    if window is not None:
        downside_volatility = downside_returns.rolling(window=window).apply(
            lambda x: np.sqrt(np.mean(x**2))
        )
        if annualized:
            downside_volatility = downside_volatility * annualization_factor
        return downside_volatility
    
    # 计算总体下行波动率
    downside_volatility = np.sqrt(np.mean(downside_returns**2))
    if annualized:
        downside_volatility = downside_volatility * annualization_factor
    
    return downside_volatility


def calculate_max_drawdown(nav_series: pd.Series) -> float:
    """
    计算最大回撤率
    
    参数:
        nav_series: 净值序列，索引为日期
        
    返回:
        最大回撤率（负值）
    """
    # 计算累计最大值
    running_max = nav_series.cummax()
    
    # 计算回撤率
    drawdown = nav_series / running_max - 1
    
    # 计算最大回撤率
    max_drawdown = drawdown.min()
    
    return max_drawdown


def calculate_rolling_drawdown(nav_series: pd.Series) -> pd.Series:
    """
    计算滚动回撤率
    
    参数:
        nav_series: 净值序列，索引为日期
        
    返回:
        滚动回撤率序列（负值）
    """
    # 计算累计最大值
    running_max = nav_series.cummax()
    
    # 计算滚动回撤率
    rolling_drawdown = nav_series / running_max - 1
    
    return rolling_drawdown


# 绘图函数已移至visualization模块


def calculate_historical_var(returns_series: pd.Series,
                           confidence_level: float = 0.95,
                           period: str = 'daily',
                           annualized: bool = False) -> float:
    """
    使用历史模拟法计算VaR (Value at Risk)
    
    参数:
        returns_series: 收益率序列，索引为日期
        confidence_level: 置信水平，默认为0.95 (95%)
        period: 数据频率，'daily'或'monthly'
        annualized: 是否年化
        
    返回:
        VaR值（正值表示损失）
    """
    # 对收益率取负值，使得正的VaR表示损失
    negative_returns = -returns_series
    
    # 计算历史VaR
    var = negative_returns.quantile(confidence_level)
    
    # 如果需要年化
    if annualized:
        annualization_factor = np.sqrt(252 if period == 'daily' else 12)
        var = var * annualization_factor
    
    return var


def calculate_parametric_var(returns_series: pd.Series,
                           confidence_level: float = 0.95,
                           period: str = 'daily',
                           annualized: bool = False) -> float:
    """
    使用参数法（正态分布假设）计算VaR (Value at Risk)
    
    参数:
        returns_series: 收益率序列，索引为日期
        confidence_level: 置信水平，默认为0.95 (95%)
        period: 数据频率，'daily'或'monthly'
        annualized: 是否年化
        
    返回:
        VaR值（正值表示损失）
    """
    # 计算均值和标准差
    mean = returns_series.mean()
    std = returns_series.std()
    
    # 计算Z值（标准正态分布的分位数）
    z_score = stats.norm.ppf(confidence_level)
    
    # 计算参数法VaR（负的均值加上Z值乘以标准差）
    var = -mean + z_score * std
    
    # 如果需要年化
    if annualized:
        annualization_factor = np.sqrt(252 if period == 'daily' else 12)
        var = var * annualization_factor
    
    return var


def calculate_expected_shortfall(returns_series: pd.Series,
                               confidence_level: float = 0.95,
                               period: str = 'daily',
                               annualized: bool = False) -> float:
    """
    计算Expected Shortfall (ES)，也称为条件VaR (CVaR)
    
    参数:
        returns_series: 收益率序列，索引为日期
        confidence_level: 置信水平，默认为0.95 (95%)
        period: 数据频率，'daily'或'monthly'
        annualized: 是否年化
        
    返回:
        Expected Shortfall值（正值表示损失）
    """
    # 对收益率取负值，使得正的ES表示损失
    negative_returns = -returns_series
    
    # 计算VaR
    var = negative_returns.quantile(confidence_level)
    
    # 计算Expected Shortfall（超过VaR的损失的平均值）
    expected_shortfall = negative_returns[negative_returns >= var].mean()
    
    # 如果需要年化
    if annualized:
        annualization_factor = np.sqrt(252 if period == 'daily' else 12)
        expected_shortfall = expected_shortfall * annualization_factor
    
    return expected_shortfall