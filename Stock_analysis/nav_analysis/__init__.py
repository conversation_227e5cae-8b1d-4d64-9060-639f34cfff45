# -*- coding: utf-8 -*-
"""
时序净值数据分析包
包含基础统计指标、风险调整收益指标和动态监控与相关性分析等模块
"""

# 导入基础统计指标模块中的函数
from .basic_stats import (
    calculate_actual_returns,
    calculate_returns,
    calculate_rolling_returns,
    calculate_volatility,
    calculate_downside_volatility,
    calculate_max_drawdown,
    calculate_rolling_drawdown,
    calculate_historical_var,
    calculate_parametric_var,
    calculate_expected_shortfall
)

# 导入风险调整收益指标模块中的函数
from .risk_adjusted_returns import (
    calculate_sharpe_ratio,
    calculate_calmar_ratio,
    calculate_information_ratio,
    calculate_sortino_ratio,
    calculate_treynor_ratio
)

# 导入动态监控与相关性分析模块中的函数
from .correlation_analysis import (
    calculate_correlation,
    calculate_autocorrelation,
    calculate_volatility_channel
)

# 导入可视化模块中的函数
from .visualization import (
    plot_nav_with_drawdown,
    plot_correlation_heatmap,
    plot_rolling_correlation,
    plot_autocorrelation,
    plot_volatility_channel
)

__all__ = [
    # 基础统计指标
    'calculate_actual_returns',
    'calculate_returns',
    'calculate_rolling_returns',
    'calculate_volatility',
    'calculate_downside_volatility',
    'calculate_max_drawdown',
    'calculate_rolling_drawdown',
    'plot_nav_with_drawdown',
    'calculate_historical_var',
    'calculate_parametric_var',
    'calculate_expected_shortfall',
    
    # 风险调整收益指标
    'calculate_sharpe_ratio',
    'calculate_calmar_ratio',
    'calculate_information_ratio',
    'calculate_sortino_ratio',
    'calculate_treynor_ratio',
    
    # 动态监控与相关性分析
    'calculate_correlation',
    'calculate_autocorrelation',
    'calculate_volatility_channel',
    'plot_correlation_heatmap',
    'plot_rolling_correlation',
    'plot_autocorrelation',
    'plot_volatility_channel'
]