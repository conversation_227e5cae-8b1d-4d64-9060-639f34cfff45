# -*- coding: utf-8 -*-
"""
股票策略分析系统配置文件
包含所有配置参数、策略分类和路径设置
"""

import os
from datetime import datetime

# =============================================================================
# 基础配置
# =============================================================================

# 数据文件配置
DATA_CONFIG = {
    'csv_file_path': 'Stock_analysis/stock_ret_indicator_summaries_export_20250617.csv',
    'date_column': 'trade_date',
    'strategy_column': 'strategy_name',
    'index_column': 'index_name',
    'strategy_earnings_column': 'strategy_earnings_ratio',
    'index_change_column': 'index_change_pct',
    'index_close_price_column': 'index_close_price',
    'index_close_price_pre_column': 'index_close_price_pre'
}

# =============================================================================
# 策略分类配置
# =============================================================================

# 策略类型定义
STRATEGY_TYPES = {
    '指数增强策略': {
        'description': '直接使用策略盈亏比例计算净值',
        'calculation_method': 'direct_return',
        'strategies': []  # 动态识别，无基准指数价格数据的策略
    },
    '中性策略': {
        'description': '计算超额净值（策略收益率 - 基准收益率）',
        'calculation_method': 'excess_return',
        'strategies': []  # 动态识别，有基准指数价格数据的策略
    }
}

# 预定义策略分类（可选，用于强制指定某些策略的类型）
PREDEFINED_STRATEGY_TYPES = {
    # 示例：
    # '1000': '中性策略',
    # '500': '中性策略',
    # 'mlp500': '中性策略'
}

# 策略显示名称映射
STRATEGY_DISPLAY_NAMES = {
    '1000': '中证1000策略',
    '500': '中证500策略',
    '500barra': '中证500Barra策略',
    'mlp500': 'MLP500策略',
    '1000untiny': '中证1000Untiny策略',
    '500untiny': '中证500Untiny策略',
    '1000value': '中证1000价值策略',
    'air': 'Air策略',
    'A500': 'A500策略'
}

# =============================================================================
# 分析参数配置
# =============================================================================

# 时间相关配置
TIME_CONFIG = {
    'date_format': '%Y-%m-%d',
    'short_period_threshold': 0.25,  # 少于3个月认为是短期数据
    'trading_days_per_year': 252,
    'calendar_days_per_year': 365.25
}

# 预设时间段
PRESET_TIME_PERIODS = {
    '2023年全年': ('2023-01-01', '2023-12-31'),
    '2024年全年': ('2024-01-01', '2024-12-31'),
    '2023年上半年': ('2023-01-01', '2023-06-30'),
    '2023年下半年': ('2023-07-01', '2023-12-31'),
    '2024年上半年': ('2024-01-01', '2024-06-30'),
    '2024年下半年': ('2024-07-01', '2024-12-31'),
    '最近6个月': ('2024-07-01', None),
    '最近1年': ('2024-01-01', None)
}

# 统计分析配置
ANALYSIS_CONFIG = {
    'confidence_levels': [0.95, 0.99],  # VaR置信水平
    'risk_free_rate': 0.02,  # 无风险利率
    'rolling_window': 20,  # 滚动窗口大小
    'min_data_points': 2,  # 最少数据点数
    'volatility_annualization_factor': {
        'daily': 252**0.5,
        'monthly': 12**0.5
    }
}

# =============================================================================
# 输出配置
# =============================================================================

# 输出目录配置
OUTPUT_CONFIG = {
    'base_dir': 'Stock_analysis/output',
    'subdirs': {
        'data': 'data',
        'charts': 'charts',
        'reports': 'reports'
    }
}

# 文件命名配置
FILE_NAMING = {
    'nav_data': '策略净值数据',
    'returns_data': '策略收益率数据',
    'basic_stats': '策略统计指标',
    'risk_adjusted_stats': '风险调整收益指标',
    'risk_analysis': '风险分析结果',
    'correlation_matrix': '策略相关性矩阵',
    'nav_chart': '策略净值走势图',
    'correlation_heatmap': '策略相关性热力图',
    'returns_distribution': '策略收益率分布图',
    'drawdown_chart': '净值回撤图'
}

# 图表配置
CHART_CONFIG = {
    'figure_size': (12, 8),
    'dpi': 300,
    'font_family': ['SimHei', 'Arial Unicode MS'],
    'line_width': 2,
    'alpha': 0.7,
    'grid_alpha': 0.3,
    'colors': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
               '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
    'save_format': 'png',
    'bbox_inches': 'tight'
}

# =============================================================================
# 显示配置
# =============================================================================

# 控制台输出配置
DISPLAY_CONFIG = {
    'decimal_places': 2,
    'percentage_format': '.2%',
    'float_format': '.4f',
    'show_warnings': True,
    'max_strategies_display': 20
}

# 表格显示配置
TABLE_CONFIG = {
    'index_name': '策略',
    'columns': [
        '数据天数',
        '数据年数', 
        '实际收益率',
        '年化收益率',
        '年化波动率',
        '最大回撤',
        '收益风险比'
    ],
    'risk_columns': [
        '夏普比率',
        '卡玛比率',
        '索提诺比率',
        '95% VaR',
        '99% VaR',
        '95% ES'
    ]
}

# =============================================================================
# 系统配置
# =============================================================================

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_path': 'Stock_analysis/output/logs/analysis.log'
}

# 性能配置
PERFORMANCE_CONFIG = {
    'max_memory_usage': '2GB',
    'parallel_processing': False,
    'chunk_size': 1000
}

# =============================================================================
# 辅助函数
# =============================================================================

def get_output_path(file_type: str, subdir: str = None, time_suffix: str = '') -> str:
    """
    获取输出文件路径
    
    参数:
        file_type: 文件类型，对应FILE_NAMING中的键
        subdir: 子目录，对应OUTPUT_CONFIG['subdirs']中的键
        time_suffix: 时间后缀
    
    返回:
        完整的文件路径
    """
    base_dir = OUTPUT_CONFIG['base_dir']
    
    # 创建基础目录
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    
    # 确定子目录
    if subdir and subdir in OUTPUT_CONFIG['subdirs']:
        full_dir = os.path.join(base_dir, OUTPUT_CONFIG['subdirs'][subdir])
    else:
        full_dir = base_dir
    
    # 创建子目录
    if not os.path.exists(full_dir):
        os.makedirs(full_dir)
    
    # 生成文件名
    if file_type in FILE_NAMING:
        filename = FILE_NAMING[file_type]
        if time_suffix:
            filename += time_suffix
        
        # 根据文件类型添加扩展名
        if subdir == 'charts':
            filename += f".{CHART_CONFIG['save_format']}"
        else:
            filename += '.csv'
        
        return os.path.join(full_dir, filename)
    else:
        raise ValueError(f"未知的文件类型: {file_type}")

def get_strategy_display_name(strategy_code: str) -> str:
    """
    获取策略显示名称
    
    参数:
        strategy_code: 策略代码
    
    返回:
        策略显示名称
    """
    return STRATEGY_DISPLAY_NAMES.get(strategy_code, strategy_code)

def get_time_suffix(start_date: str = None, end_date: str = None) -> str:
    """
    生成时间后缀
    
    参数:
        start_date: 开始日期
        end_date: 结束日期
    
    返回:
        时间后缀字符串
    """
    if not start_date and not end_date:
        return ""
    
    time_parts = []
    if start_date:
        time_parts.append(f"from_{start_date}")
    if end_date:
        time_parts.append(f"to_{end_date}")
    
    return "_" + "_".join(time_parts)

def create_output_directories():
    """创建所有输出目录"""
    base_dir = OUTPUT_CONFIG['base_dir']
    
    # 创建基础目录
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    
    # 创建子目录
    for subdir in OUTPUT_CONFIG['subdirs'].values():
        full_path = os.path.join(base_dir, subdir)
        if not os.path.exists(full_path):
            os.makedirs(full_path)
    
    # 创建日志目录
    log_dir = os.path.dirname(LOGGING_CONFIG['file_path'])
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

# 初始化时创建输出目录
create_output_directories()