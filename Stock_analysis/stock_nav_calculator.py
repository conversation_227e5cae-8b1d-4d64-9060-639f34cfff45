# -*- coding: utf-8 -*-
"""
股票策略净值计算器
根据配置文件中的数据源计算各策略净值
"""

import pandas as pd
import numpy as np
from datetime import datetime
import nav_analysis as na
from config import (
    DATA_CONFIG, STRATEGY_TYPES, PREDEFINED_STRATEGY_TYPES,
    TIME_CONFIG, ANALYSIS_CONFIG, OUTPUT_CONFIG, FILE_NAMING,
    DISPLAY_CONFIG, TABLE_CONFIG, get_output_path, get_time_suffix,
    get_strategy_display_name
)


class StockNavCalculator:
    """股票策略净值计算器"""
    
    def __init__(self, csv_file_path: str = None, start_date: str = None, end_date: str = None):
        """
        初始化计算器
        
        参数:
            csv_file_path: CSV文件路径，默认使用配置文件中的路径
            start_date: 开始日期，格式'YYYY-MM-DD'，如'2023-01-01'
            end_date: 结束日期，格式'YYYY-MM-DD'，如'2024-12-31'
        """
        self.csv_file_path = csv_file_path or DATA_CONFIG['csv_file_path']
        self.start_date = pd.to_datetime(start_date) if start_date else None
        self.end_date = pd.to_datetime(end_date) if end_date else None
        self.data = None
        self.nav_data = {}
        self.returns_data = {}
        
    def load_data(self):
        """加载CSV数据"""
        print("正在加载数据...")
        self.data = pd.read_csv(self.csv_file_path)
        self.data[DATA_CONFIG['date_column']] = pd.to_datetime(self.data[DATA_CONFIG['date_column']])
        
        # 应用时间过滤
        original_count = len(self.data)
        date_col = DATA_CONFIG['date_column']
        
        if self.start_date is not None:
            self.data = self.data[self.data[date_col] >= self.start_date]
            print(f"应用开始日期过滤: {self.start_date.strftime(TIME_CONFIG['date_format'])}")
            
        if self.end_date is not None:
            self.data = self.data[self.data[date_col] <= self.end_date]
            print(f"应用结束日期过滤: {self.end_date.strftime(TIME_CONFIG['date_format'])}")
        
        filtered_count = len(self.data)
        print(f"数据加载完成，原始记录：{original_count}条，过滤后：{filtered_count}条")
        
        if filtered_count == 0:
            raise ValueError("过滤后没有数据，请检查时间范围设置")
        
        # 显示时间范围
        min_date = self.data[date_col].min()
        max_date = self.data[date_col].max()
        print(f"数据时间范围: {min_date.strftime(TIME_CONFIG['date_format'])} 至 {max_date.strftime(TIME_CONFIG['date_format'])}")
        
        # 显示策略概况
        strategies = self.data[DATA_CONFIG['strategy_column']].unique()
        print(f"发现{len(strategies)}个策略：{list(strategies)}")
        
    def identify_strategy_types(self):
        """识别策略类型"""
        strategy_types = {}
        strategy_col = DATA_CONFIG['strategy_column']
        index_price_col = DATA_CONFIG['index_close_price_column']
        
        for strategy in self.data[strategy_col].unique():
            # 首先检查预定义分类
            if strategy in PREDEFINED_STRATEGY_TYPES:
                strategy_types[strategy] = PREDEFINED_STRATEGY_TYPES[strategy]
            else:
                # 动态识别策略类型
                strategy_data = self.data[self.data[strategy_col] == strategy]
                has_index_price = strategy_data[index_price_col].notna().any()
                
                if has_index_price:
                    strategy_types[strategy] = '中性策略'
                else:
                    strategy_types[strategy] = '指数增强策略'
                
        print("\n策略类型识别结果：")
        for strategy, strategy_type in strategy_types.items():
            display_name = get_strategy_display_name(strategy)
            print(f"  {display_name}({strategy}): {strategy_type}")
            
        return strategy_types
        
    def calculate_nav_for_strategy(self, strategy_name: str, strategy_type: str):
        """
        计算单个策略的净值
        
        参数:
            strategy_name: 策略名称
            strategy_type: 策略类型（'指数增强策略' 或 '中性策略'）
        """
        strategy_col = DATA_CONFIG['strategy_column']
        date_col = DATA_CONFIG['date_column']
        
        strategy_data = self.data[self.data[strategy_col] == strategy_name].copy()
        strategy_data = strategy_data.sort_values(date_col)
        
        if strategy_type == '指数增强策略':
            # 指数增强策略：直接使用策略盈亏比例计算净值
            nav_series = self._calculate_index_enhanced_nav(strategy_data)
        else:
            # 中性策略：计算超额净值
            nav_series = self._calculate_neutral_strategy_nav(strategy_data)
            
        return nav_series
        
    def _calculate_index_enhanced_nav(self, strategy_data: pd.DataFrame):
        """计算指数增强策略净值"""
        date_col = DATA_CONFIG['date_column']
        earnings_col = DATA_CONFIG['strategy_earnings_column']
        
        nav_series = pd.Series(index=strategy_data[date_col], dtype=float)
        nav_series.iloc[0] = 1.0  # 初始净值为1
        
        for i in range(1, len(strategy_data)):
            # 使用策略盈亏比例计算净值
            return_rate = strategy_data.iloc[i][earnings_col]
            if pd.notna(return_rate):
                nav_series.iloc[i] = nav_series.iloc[i-1] * (1 + return_rate)
            else:
                nav_series.iloc[i] = nav_series.iloc[i-1]
                
        return nav_series
        
    def _calculate_neutral_strategy_nav(self, strategy_data: pd.DataFrame):
        """计算中性策略超额净值"""
        date_col = DATA_CONFIG['date_column']
        earnings_col = DATA_CONFIG['strategy_earnings_column']
        index_change_col = DATA_CONFIG['index_change_column']
        
        nav_series = pd.Series(index=strategy_data[date_col], dtype=float)
        nav_series.iloc[0] = 1.0  # 初始净值为1
        
        for i in range(1, len(strategy_data)):
            # 计算策略收益率
            strategy_return = strategy_data.iloc[i][earnings_col]
            
            # 计算基准指数收益率
            index_return = strategy_data.iloc[i][index_change_col]
            
            if pd.notna(strategy_return) and pd.notna(index_return):
                # 超额收益率 = 策略收益率 - 基准收益率
                excess_return = strategy_return - index_return
                nav_series.iloc[i] = nav_series.iloc[i-1] * (1 + excess_return)
            else:
                nav_series.iloc[i] = nav_series.iloc[i-1]
                
        return nav_series
        
    def calculate_all_nav(self):
        """计算所有策略的净值"""
        print("\n开始计算策略净值...")
        
        # 识别策略类型
        strategy_types = self.identify_strategy_types()
        
        # 计算每个策略的净值
        for strategy_name, strategy_type in strategy_types.items():
            display_name = get_strategy_display_name(strategy_name)
            print(f"\n正在计算{display_name}({strategy_name}, {strategy_type})的净值...")
            
            try:
                nav_series = self.calculate_nav_for_strategy(strategy_name, strategy_type)
                self.nav_data[strategy_name] = nav_series
                
                # 计算收益率序列
                returns_series = nav_series.pct_change().dropna()
                self.returns_data[strategy_name] = returns_series
                
                print(f"  净值计算完成，数据点数：{len(nav_series)}")
                print(f"  期初净值：{nav_series.iloc[0]:{DISPLAY_CONFIG['float_format']}}")
                print(f"  期末净值：{nav_series.iloc[-1]:{DISPLAY_CONFIG['float_format']}}")
                total_return = (nav_series.iloc[-1]/nav_series.iloc[0]-1)
                print(f"  总收益率：{total_return:{DISPLAY_CONFIG['percentage_format']}}")
                
            except Exception as e:
                print(f"  计算{display_name}净值时出错：{e}")
                
    def get_nav_dataframe(self):
        """获取净值DataFrame"""
        if not self.nav_data:
            raise ValueError("请先调用calculate_all_nav()计算净值")
            
        # 找到所有日期的并集
        all_dates = set()
        for nav_series in self.nav_data.values():
            all_dates.update(nav_series.index)
        all_dates = sorted(list(all_dates))
        
        # 创建DataFrame
        nav_df = pd.DataFrame(index=all_dates)
        for strategy_name, nav_series in self.nav_data.items():
            # 使用前向填充，但只在策略有数据的日期范围内
            strategy_dates = nav_series.index
            start_date = strategy_dates.min()
            end_date = strategy_dates.max()
            
            # 创建该策略的完整序列
            strategy_full_series = pd.Series(index=all_dates, dtype=float)
            
            # 只在策略存在的日期范围内填充数据
            mask = (strategy_full_series.index >= start_date) & (strategy_full_series.index <= end_date)
            strategy_full_series.loc[mask] = nav_series.reindex(
                strategy_full_series.index[mask], method='ffill'
            )
            
            nav_df[strategy_name] = strategy_full_series
            
        return nav_df
        
    def get_returns_dataframe(self):
        """获取收益率DataFrame"""
        if not self.returns_data:
            raise ValueError("请先调用calculate_all_nav()计算净值")
            
        # 找到所有日期的并集
        all_dates = set()
        for returns_series in self.returns_data.values():
            all_dates.update(returns_series.index)
        all_dates = sorted(list(all_dates))
        
        # 创建DataFrame
        returns_df = pd.DataFrame(index=all_dates)
        for strategy_name, returns_series in self.returns_data.items():
            # 只在策略有数据的日期填充收益率，其他日期为NaN
            strategy_dates = returns_series.index
            start_date = strategy_dates.min()
            end_date = strategy_dates.max()
            
            strategy_full_series = pd.Series(index=all_dates, dtype=float)
            mask = (strategy_full_series.index >= start_date) & (strategy_full_series.index <= end_date)
            strategy_full_series.loc[mask] = returns_series.reindex(
                strategy_full_series.index[mask], fill_value=0
            )
            
            returns_df[strategy_name] = strategy_full_series
            
        return returns_df


def analyze_basic_stats(nav_df, returns_df):
    """基础统计指标分析（与nav_analysis_example.py中的函数相同）"""
    print("\n=== 基础统计指标分析 ===")
    results = []
    
    for strategy in nav_df.columns:
        try:
            nav_series = nav_df[strategy].dropna()
            returns_series = returns_df[strategy]
            
            if len(nav_series) < 2:
                print(f"\n{strategy}: 数据不足，跳过分析")
                continue
            
            # 计算时间跨度
            start_date = nav_series.index[0]
            end_date = nav_series.index[-1]
            days = (end_date - start_date).days
            years = days / TIME_CONFIG['calendar_days_per_year']
            
            # 计算实际收益率（总收益率）
            actual_return = na.calculate_returns(nav_series, period='daily', annualized=False)
            annual_return = na.calculate_returns(nav_series, period='daily', annualized=True)
            volatility = na.calculate_volatility(returns_series, period='daily', annualized=True)
            max_drawdown = na.calculate_max_drawdown(nav_series)
            
            display_name = get_strategy_display_name(strategy)
            print(f"\n{display_name}({strategy}):")
            float_format = DISPLAY_CONFIG['float_format'][1:]  # 去掉开头的点
            print(f"  数据时间跨度: {days}天 ({years:{float_format}}年)")
            print(f"  实际收益率: {actual_return:{DISPLAY_CONFIG['percentage_format']}}")
            print(f"  年化收益率: {annual_return:{DISPLAY_CONFIG['percentage_format']}}")
            if years < TIME_CONFIG['short_period_threshold']:
                print(f"  ⚠️  注意：时间跨度过短，年化收益率仅供参考")
            print(f"  年化波动率: {volatility:{DISPLAY_CONFIG['percentage_format']}}")
            print(f"  最大回撤: {max_drawdown:{DISPLAY_CONFIG['percentage_format']}}")
            
            results.append({
                TABLE_CONFIG['index_name']: display_name,
                TABLE_CONFIG['columns'][0]: days,  # 数据天数
                TABLE_CONFIG['columns'][1]: years,  # 数据年数
                TABLE_CONFIG['columns'][2]: actual_return,  # 实际收益率
                TABLE_CONFIG['columns'][3]: annual_return,  # 年化收益率
                TABLE_CONFIG['columns'][4]: volatility,  # 年化波动率
                TABLE_CONFIG['columns'][5]: max_drawdown,  # 最大回撤
                TABLE_CONFIG['columns'][6]: annual_return / volatility if volatility != 0 else 0  # 收益风险比
            })
        except Exception as e:
            print(f"分析{strategy}时出错: {e}")
    
    return pd.DataFrame(results).set_index(TABLE_CONFIG['index_name'])


def main(start_date=None, end_date=None):
    """
    主函数
    
    参数:
        start_date: 开始日期，格式'YYYY-MM-DD'
        end_date: 结束日期，格式'YYYY-MM-DD'
    """
    # 初始化计算器
    calculator = StockNavCalculator(
        start_date=start_date,
        end_date=end_date
    )
    
    # 加载数据
    calculator.load_data()
    
    # 计算所有策略净值
    calculator.calculate_all_nav()
    
    # 获取净值和收益率数据
    nav_df = calculator.get_nav_dataframe()
    returns_df = calculator.get_returns_dataframe()
    
    print(f"\n净值数据形状：{nav_df.shape}")
    print(f"收益率数据形状：{returns_df.shape}")
    
    # 使用nav_analysis进行基础统计分析
    stats_results = analyze_basic_stats(nav_df, returns_df)
    print(f"\n基础统计指标汇总：")
    print(stats_results)
    
    # 生成时间后缀
    time_suffix = get_time_suffix(start_date, end_date)
    
    # 保存结果到配置的输出目录
    nav_path = get_output_path('nav_data', 'data', time_suffix)
    returns_path = get_output_path('returns_data', 'data', time_suffix)
    stats_path = get_output_path('basic_stats', 'data', time_suffix)
    
    nav_df.to_csv(nav_path)
    returns_df.to_csv(returns_path)
    stats_results.to_csv(stats_path)
    
    print(f"\n结果已保存到输出目录：")
    print(f"  净值数据: {nav_path}")
    print(f"  收益率数据: {returns_path}")
    print(f"  统计指标: {stats_path}")
    
    return nav_df, returns_df, stats_results


if __name__ == "__main__":
    nav_df, returns_df, stats_results = main()