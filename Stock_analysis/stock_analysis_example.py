# -*- coding: utf-8 -*-
"""
股票策略分析示例
展示如何使用stock_nav_calculator计算净值并集成到nav_analysis框架中
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from stock_nav_calculator import StockNavCalculator, analyze_basic_stats
import nav_analysis as na
from config import (
    PRESET_TIME_PERIODS, CHART_CONFIG, get_output_path, 
    get_time_suffix, get_strategy_display_name
)

# 设置中文字体
plt.rcParams['font.sans-serif'] = CHART_CONFIG['font_family']
plt.rcParams['axes.unicode_minus'] = False


def comprehensive_analysis(start_date=None, end_date=None):
    """
    综合分析示例
    
    参数:
        start_date: 开始日期，格式'YYYY-MM-DD'
        end_date: 结束日期，格式'YYYY-MM-DD'
    """
    print("=== 股票策略综合分析 ===")
    
    if start_date or end_date:
        print(f"时间范围设置:")
        if start_date:
            print(f"  开始日期: {start_date}")
        if end_date:
            print(f"  结束日期: {end_date}")
    
    # 1. 计算策略净值
    print("\n1. 计算策略净值")
    calculator = StockNavCalculator(
        start_date=start_date,
        end_date=end_date
    )
    calculator.load_data()
    calculator.calculate_all_nav()
    
    # 获取净值和收益率数据
    nav_df = calculator.get_nav_dataframe()
    returns_df = calculator.get_returns_dataframe()
    
    # 2. 基础统计分析
    print("\n2. 基础统计分析")
    basic_stats = analyze_basic_stats(nav_df, returns_df)
    
    # 3. 风险调整收益指标分析
    print("\n3. 风险调整收益指标分析")
    risk_adjusted_stats = analyze_risk_adjusted_returns(nav_df, returns_df)
    
    # 4. 相关性分析
    print("\n4. 相关性分析")
    correlation_analysis(returns_df)
    
    # 5. 可视化分析
    print("\n5. 生成可视化图表")
    time_suffix = get_time_suffix(start_date, end_date)
    create_visualizations(nav_df, returns_df, time_suffix)
    
    return nav_df, returns_df, basic_stats, risk_adjusted_stats


def analyze_risk_adjusted_returns(nav_df, returns_df):
    """风险调整收益指标分析"""
    print("\n=== 风险调整收益指标分析 ===")
    results = []
    
    for strategy in nav_df.columns:
        try:
            nav_series = nav_df[strategy].dropna()
            returns_series = returns_df[strategy]
            
            if len(nav_series) < 2:
                print(f"\n{strategy}: 数据不足，跳过分析")
                continue
            
            # 计算实际收益率和风险调整收益指标
            actual_return = na.calculate_actual_returns(nav_series)
            sharpe_ratio = na.calculate_sharpe_ratio(returns_series, period='daily')
            calmar_ratio = na.calculate_calmar_ratio(nav_series, period='daily')
            sortino_ratio = na.calculate_sortino_ratio(returns_series, period='daily')
            
            print(f"\n{strategy}:")
            print(f"  实际收益率: {actual_return:.2%}")
            print(f"  夏普比率: {sharpe_ratio:.3f}")
            print(f"  卡玛比率: {calmar_ratio:.3f}")
            print(f"  索提诺比率: {sortino_ratio:.3f}")
            
            results.append({
                '策略': strategy,
                '实际收益率': actual_return,
                '夏普比率': sharpe_ratio,
                '卡玛比率': calmar_ratio,
                '索提诺比率': sortino_ratio
            })
        except Exception as e:
            print(f"分析{strategy}时出错: {e}")
            results.append({
                '策略': strategy,
                '实际收益率': np.nan,
                '夏普比率': np.nan,
                '卡玛比率': np.nan,
                '索提诺比率': np.nan
            })
    
    if results:
        return pd.DataFrame(results).set_index('策略')
    else:
        return pd.DataFrame()


def correlation_analysis(returns_df):
    """相关性分析"""
    print("\n=== 相关性分析 ===")
    
    # 计算相关系数矩阵
    correlation_matrix = na.calculate_correlation(returns_df)
    print("\n策略间相关系数矩阵：")
    print(correlation_matrix.round(3))
    
    # 绘制相关性热力图
    na.plot_correlation_heatmap(correlation_matrix, title='策略相关性热力图')
    plt.tight_layout()
    
    # 保存到配置的输出目录
    chart_path = get_output_path('correlation_heatmap', 'charts')
    plt.savefig(chart_path, dpi=CHART_CONFIG['dpi'], bbox_inches=CHART_CONFIG['bbox_inches'])
    plt.show()


def create_visualizations(nav_df, returns_df, time_suffix=""):
    """创建可视化图表"""
    
    # 1. 净值走势图
    plt.figure(figsize=CHART_CONFIG['figure_size'])
    colors = CHART_CONFIG['colors']
    
    for i, strategy in enumerate(nav_df.columns):
        color = colors[i % len(colors)]
        display_name = get_strategy_display_name(strategy)
        plt.plot(nav_df.index, nav_df[strategy], 
                label=display_name, linewidth=CHART_CONFIG['line_width'], color=color)
    
    plt.title('策略净值走势图', fontsize=16)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('净值', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=CHART_CONFIG['grid_alpha'])
    plt.tight_layout()
    
    # 保存到配置的输出目录
    chart_path = get_output_path('nav_chart', 'charts', time_suffix)
    plt.savefig(chart_path, dpi=CHART_CONFIG['dpi'], bbox_inches=CHART_CONFIG['bbox_inches'])
    plt.show()
    
    # 2. 净值与回撤图（使用nav_analysis的功能）
    for strategy in nav_df.columns:
        try:
            display_name = get_strategy_display_name(strategy)
            na.plot_nav_with_drawdown(nav_df[strategy], title=f'{display_name}净值与回撤')
            plt.tight_layout()
            
            # 保存到配置的输出目录
            chart_path = get_output_path('drawdown_chart', 'charts', f"{time_suffix}_{strategy}")
            plt.savefig(chart_path, dpi=CHART_CONFIG['dpi'], bbox_inches=CHART_CONFIG['bbox_inches'])
            plt.show()
        except Exception as e:
            print(f"绘制{display_name}净值回撤图时出错: {e}")
    
    # 3. 收益率分布图
    plt.figure(figsize=(15, 10))
    n_strategies = len(returns_df.columns)
    n_cols = 3
    n_rows = (n_strategies + n_cols - 1) // n_cols
    
    for i, strategy in enumerate(returns_df.columns):
        plt.subplot(n_rows, n_cols, i + 1)
        display_name = get_strategy_display_name(strategy)
        returns_df[strategy].hist(bins=50, alpha=CHART_CONFIG['alpha'])
        plt.title(f'{display_name}收益率分布')
        plt.xlabel('日收益率')
        plt.ylabel('频数')
        plt.grid(True, alpha=CHART_CONFIG['grid_alpha'])
    
    plt.tight_layout()
    
    # 保存到配置的输出目录
    chart_path = get_output_path('returns_distribution', 'charts', time_suffix)
    plt.savefig(chart_path, dpi=CHART_CONFIG['dpi'], bbox_inches=CHART_CONFIG['bbox_inches'])
    plt.show()


def risk_analysis(start_date=None, end_date=None):
    """
    风险分析示例
    
    参数:
        start_date: 开始日期，格式'YYYY-MM-DD'
        end_date: 结束日期，格式'YYYY-MM-DD'
    """
    print("\n=== 风险分析 ===")
    
    # 加载数据
    calculator = StockNavCalculator(
        start_date=start_date,
        end_date=end_date
    )
    calculator.load_data()
    calculator.calculate_all_nav()
    
    nav_df = calculator.get_nav_dataframe()
    returns_df = calculator.get_returns_dataframe()
    
    # 风险指标分析
    risk_results = []
    
    for strategy in returns_df.columns:
        try:
            returns_series = returns_df[strategy]
            
            # 计算VaR和ES
            var_95 = na.calculate_historical_var(returns_series, confidence_level=0.95)
            var_99 = na.calculate_historical_var(returns_series, confidence_level=0.99)
            es_95 = na.calculate_expected_shortfall(returns_series, confidence_level=0.95)
            
            print(f"\n{strategy}风险指标:")
            print(f"  95% VaR: {var_95:.4f}")
            print(f"  99% VaR: {var_99:.4f}")
            print(f"  95% ES: {es_95:.4f}")
            
            risk_results.append({
                '策略': strategy,
                '95% VaR': var_95,
                '99% VaR': var_99,
                '95% ES': es_95
            })
            
        except Exception as e:
            print(f"分析{strategy}风险指标时出错: {e}")
    
    risk_df = pd.DataFrame(risk_results).set_index('策略')
    print(f"\n风险指标汇总:")
    print(risk_df.round(4))
    
    return risk_df


def main_with_time_selection():
    """带时间选择的主函数"""
    print("=== 股票策略综合分析系统 ===")
    print("请选择分析模式：")
    print("1. 全时间段分析")
    print("2. 指定时间范围分析")
    print("3. 预设时间段分析")
    
    choice = input("请输入选择 (1, 2, 或 3): ").strip()
    
    start_date = None
    end_date = None
    
    if choice == "2":
        print("\n请输入时间范围（格式：YYYY-MM-DD，如2023-01-01）")
        start_input = input("开始日期（留空表示从最早开始）: ").strip()
        end_input = input("结束日期（留空表示到最晚结束）: ").strip()
        
        start_date = start_input if start_input else None
        end_date = end_input if end_input else None
        
    elif choice == "3":
        print("\n预设时间段选择：")
        preset_options = list(PRESET_TIME_PERIODS.keys())
        for i, option in enumerate(preset_options, 1):
            print(f"{i}. {option}")
        
        preset_choice = input(f"请选择预设时间段 (1-{len(preset_options)}): ").strip()
        
        try:
            choice_idx = int(preset_choice) - 1
            if 0 <= choice_idx < len(preset_options):
                selected_period = preset_options[choice_idx]
                start_date, end_date = PRESET_TIME_PERIODS[selected_period]
            else:
                print("无效选择，使用全时间段分析")
        except ValueError:
            print("无效输入，使用全时间段分析")
    
    # 运行综合分析
    nav_df, returns_df, basic_stats, risk_adjusted_stats = comprehensive_analysis(start_date, end_date)
    
    # 运行风险分析
    risk_stats = risk_analysis(start_date, end_date)
    
    # 生成时间后缀
    time_suffix = get_time_suffix(start_date, end_date)
    
    print("\n=== 分析完成 ===")
    print("生成的文件:")
    print(f"- 数据文件保存在: {OUTPUT_CONFIG['base_dir']}/{OUTPUT_CONFIG['subdirs']['data']}/")
    print(f"- 图表文件保存在: {OUTPUT_CONFIG['base_dir']}/{OUTPUT_CONFIG['subdirs']['charts']}/")
    print(f"- 报告文件保存在: {OUTPUT_CONFIG['base_dir']}/{OUTPUT_CONFIG['subdirs']['reports']}/")
    print(f"- 时间后缀: {time_suffix}")
    
    return nav_df, returns_df, basic_stats, risk_adjusted_stats, risk_stats

if __name__ == "__main__":
    # 运行带时间选择的分析
    results = main_with_time_selection()