# 股票策略分析系统配置说明

## 概述

股票策略分析系统现已采用统一的配置管理方式，所有配置参数、策略分类和输出路径都集中在`config.py`文件中管理。

## 配置文件结构

### 1. 基础配置 (DATA_CONFIG)

```python
DATA_CONFIG = {
    'csv_file_path': 'Stock_analysis/stock_ret_indicator_summaries_export_20250617.csv',
    'date_column': 'trade_date',
    'strategy_column': 'strategy_name',
    'index_column': 'index_name',
    'strategy_earnings_column': 'strategy_earnings_ratio',
    'index_change_column': 'index_change_pct',
    'index_close_price_column': 'index_close_price',
    'index_close_price_pre_column': 'index_close_price_pre'
}
```

**用途**：定义数据源文件路径和字段映射

### 2. 策略分类配置 (STRATEGY_TYPES)

```python
STRATEGY_TYPES = {
    '指数增强策略': {
        'description': '直接使用策略盈亏比例计算净值',
        'calculation_method': 'direct_return',
        'strategies': []  # 动态识别
    },
    '中性策略': {
        'description': '计算超额净值（策略收益率 - 基准收益率）',
        'calculation_method': 'excess_return',
        'strategies': []  # 动态识别
    }
}
```

**用途**：定义策略类型和计算方法

### 3. 策略显示名称映射 (STRATEGY_DISPLAY_NAMES)

```python
STRATEGY_DISPLAY_NAMES = {
    '1000': '中证1000策略',
    '500': '中证500策略',
    '500barra': '中证500Barra策略',
    'mlp500': 'MLP500策略',
    # ... 更多映射
}
```

**用途**：将策略代码映射为友好的显示名称

### 4. 时间配置 (TIME_CONFIG)

```python
TIME_CONFIG = {
    'date_format': '%Y-%m-%d',
    'short_period_threshold': 0.25,  # 少于3个月认为是短期数据
    'trading_days_per_year': 252,
    'calendar_days_per_year': 365.25
}
```

**用途**：时间相关的配置参数

### 5. 预设时间段 (PRESET_TIME_PERIODS)

```python
PRESET_TIME_PERIODS = {
    '2023年全年': ('2023-01-01', '2023-12-31'),
    '2024年全年': ('2024-01-01', '2024-12-31'),
    '2023年上半年': ('2023-01-01', '2023-06-30'),
    '2023年下半年': ('2023-07-01', '2023-12-31'),
    # ... 更多预设
}
```

**用途**：定义常用的时间段选项

### 6. 输出配置 (OUTPUT_CONFIG)

```python
OUTPUT_CONFIG = {
    'base_dir': 'Stock_analysis/output',
    'subdirs': {
        'data': 'data',
        'charts': 'charts',
        'reports': 'reports'
    }
}
```

**用途**：定义输出目录结构

### 7. 文件命名配置 (FILE_NAMING)

```python
FILE_NAMING = {
    'nav_data': '策略净值数据',
    'returns_data': '策略收益率数据',
    'basic_stats': '策略统计指标',
    'risk_adjusted_stats': '风险调整收益指标',
    # ... 更多文件类型
}
```

**用途**：统一文件命名规则

### 8. 图表配置 (CHART_CONFIG)

```python
CHART_CONFIG = {
    'figure_size': (12, 8),
    'dpi': 300,
    'font_family': ['SimHei', 'Arial Unicode MS'],
    'line_width': 2,
    'colors': ['#1f77b4', '#ff7f0e', '#2ca02c', ...],
    # ... 更多图表设置
}
```

**用途**：统一图表样式和格式

## 输出目录结构

```
Stock_analysis/output/
├── data/           # 数据文件
│   ├── 策略净值数据_[时间后缀].csv
│   ├── 策略收益率数据_[时间后缀].csv
│   └── 策略统计指标_[时间后缀].csv
├── charts/         # 图表文件
│   ├── 策略净值走势图_[时间后缀].png
│   ├── 策略相关性热力图_[时间后缀].png
│   └── [策略名]_净值回撤图_[时间后缀].png
├── reports/        # 报告文件
└── logs/           # 日志文件
```

## 配置系统优势

### 1. 集中管理
- 所有配置参数集中在一个文件中
- 便于维护和修改
- 避免硬编码

### 2. 灵活配置
- 支持自定义策略分类
- 可配置输出路径和文件命名
- 支持预设时间段

### 3. 统一输出
- 所有生成文件保存在统一的输出目录
- 按类型分类存放（数据、图表、报告）
- 自动创建目录结构

### 4. 易于扩展
- 新增策略类型只需修改配置
- 新增文件类型只需添加命名规则
- 新增预设时间段只需更新配置

## 使用示例

### 1. 修改数据源
```python
# 在config.py中修改
DATA_CONFIG['csv_file_path'] = '新的数据文件路径.csv'
```

### 2. 添加新策略显示名称
```python
# 在config.py中添加
STRATEGY_DISPLAY_NAMES['new_strategy'] = '新策略显示名称'
```

### 3. 添加预设时间段
```python
# 在config.py中添加
PRESET_TIME_PERIODS['2025年全年'] = ('2025-01-01', '2025-12-31')
```

### 4. 修改输出目录
```python
# 在config.py中修改
OUTPUT_CONFIG['base_dir'] = '新的输出目录路径'
```

## 辅助函数

### 1. get_output_path()
```python
# 获取输出文件路径
path = get_output_path('nav_data', 'data', '_from_2023-01-01')
# 返回: Stock_analysis/output/data/策略净值数据_from_2023-01-01.csv
```

### 2. get_strategy_display_name()
```python
# 获取策略显示名称
display_name = get_strategy_display_name('1000')
# 返回: 中证1000策略
```

### 3. get_time_suffix()
```python
# 生成时间后缀
suffix = get_time_suffix('2023-01-01', '2023-12-31')
# 返回: _from_2023-01-01_to_2023-12-31
```

## 配置最佳实践

### 1. 数据源配置
- 将数据文件路径配置在DATA_CONFIG中
- 字段名映射便于适配不同数据源

### 2. 策略管理
- 使用STRATEGY_DISPLAY_NAMES提供友好名称
- 通过PREDEFINED_STRATEGY_TYPES强制指定策略类型

### 3. 输出管理
- 使用统一的输出目录结构
- 通过时间后缀区分不同分析结果

### 4. 图表样式
- 在CHART_CONFIG中统一图表样式
- 支持中文字体显示

## 注意事项

1. **路径配置**：确保配置的路径存在或程序有权限创建
2. **字段映射**：DATA_CONFIG中的字段名必须与实际CSV文件匹配
3. **时间格式**：时间相关配置使用标准格式
4. **扩展性**：新增配置项时保持命名一致性

## 总结

配置系统的引入使得股票策略分析系统更加灵活、可维护和可扩展。通过集中管理配置参数，用户可以轻松定制分析行为，同时保持代码的整洁和一致性。