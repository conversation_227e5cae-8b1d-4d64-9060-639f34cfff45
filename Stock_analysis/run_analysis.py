# -*- coding: utf-8 -*-
"""
快速运行股票策略分析
支持时间范围选择
"""

import sys
from stock_nav_calculator import StockNavCalculator, analyze_basic_stats
from config import PRESET_TIME_PERIODS, get_output_path, get_time_suffix

def quick_analysis(start_date=None, end_date=None):
    """
    快速分析
    
    参数:
        start_date: 开始日期，格式'YYYY-MM-DD'
        end_date: 结束日期，格式'YYYY-MM-DD'
    """
    print("开始股票策略分析...")
    
    if start_date or end_date:
        print(f"时间范围设置:")
        if start_date:
            print(f"  开始日期: {start_date}")
        if end_date:
            print(f"  结束日期: {end_date}")
    
    # 1. 初始化并计算净值
    calculator = StockNavCalculator(
        start_date=start_date,
        end_date=end_date
    )
    calculator.load_data()
    calculator.calculate_all_nav()
    
    # 2. 获取数据
    nav_df = calculator.get_nav_dataframe()
    returns_df = calculator.get_returns_dataframe()
    
    # 3. 基础分析
    stats_results = analyze_basic_stats(nav_df, returns_df)
    
    # 4. 生成时间后缀并保存结果
    time_suffix = get_time_suffix(start_date, end_date)
    
    nav_path = get_output_path('nav_data', 'data', time_suffix)
    returns_path = get_output_path('returns_data', 'data', time_suffix)
    stats_path = get_output_path('basic_stats', 'data', time_suffix)
    
    # 5. 保存结果
    nav_df.to_csv(nav_path)
    returns_df.to_csv(returns_path)
    stats_results.to_csv(stats_path)
    
    print(f"\n分析完成！结果已保存到输出目录：")
    print(f"  净值数据: {nav_path}")
    print(f"  收益率数据: {returns_path}")
    print(f"  统计指标: {stats_path}")
    return nav_df, returns_df, stats_results

def interactive_analysis():
    """交互式分析，让用户选择时间范围"""
    print("=== 股票策略分析系统 ===")
    print("请选择分析模式：")
    print("1. 全时间段分析")
    print("2. 指定时间范围分析")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    start_date = None
    end_date = None
    
    if choice == "2":
        print("\n请输入时间范围（格式：YYYY-MM-DD，如2023-01-01）")
        start_input = input("开始日期（留空表示从最早开始）: ").strip()
        end_input = input("结束日期（留空表示到最晚结束）: ").strip()
        
        start_date = start_input if start_input else None
        end_date = end_input if end_input else None
    
    return quick_analysis(start_date, end_date)

def parse_command_line():
    """解析命令行参数"""
    start_date = None
    end_date = None
    
    # 简单的命令行参数解析
    for i, arg in enumerate(sys.argv):
        if arg == "--start" and i + 1 < len(sys.argv):
            start_date = sys.argv[i + 1]
        elif arg == "--end" and i + 1 < len(sys.argv):
            end_date = sys.argv[i + 1]
    
    return start_date, end_date

if __name__ == "__main__":
    # 检查是否有命令行参数
    if len(sys.argv) > 1:
        start_date, end_date = parse_command_line()
        nav_df, returns_df, stats_results = quick_analysis(start_date, end_date)
    else:
        # 交互式模式
        nav_df, returns_df, stats_results = interactive_analysis()