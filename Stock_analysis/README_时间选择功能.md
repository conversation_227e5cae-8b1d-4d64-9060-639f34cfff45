# 股票策略分析系统 - 时间选择功能使用说明

## 功能概述

股票策略分析系统现已支持灵活的时间范围选择功能，允许用户指定分析的时间段，进行更精确的策略表现分析。

## 主要功能

### 1. 基础时间过滤
- 支持指定开始日期和结束日期
- 自动过滤数据并显示实际分析的时间范围
- 生成带时间后缀的结果文件

### 2. 多种使用方式

#### 方式一：命令行参数
```bash
# 分析2023年全年数据
python Stock_analysis/run_analysis.py --start 2023-01-01 --end 2023-12-31

# 分析2024年至今数据
python Stock_analysis/run_analysis.py --start 2024-01-01

# 分析截止到2023年底的数据
python Stock_analysis/run_analysis.py --end 2023-12-31
```

#### 方式二：交互式选择
```bash
python Stock_analysis/run_analysis.py
```
然后根据提示选择：
1. 全时间段分析
2. 指定时间范围分析

#### 方式三：程序化调用
```python
from stock_nav_calculator import StockNavCalculator

# 创建指定时间范围的计算器
calculator = StockNavCalculator(
    'Stock_analysis/stock_ret_indicator_summaries_export_20250617.csv',
    start_date='2023-01-01',
    end_date='2023-12-31'
)
```

### 3. 时间范围对比分析

使用专门的时间范围分析工具：
```bash
python Stock_analysis/time_range_analysis.py
```

提供三种分析模式：

#### 模式1：不同时间段对比分析
自动分析以下预设时间段：
- 2023年全年
- 2024年全年  
- 2023年上半年
- 2023年下半年
- 最近6个月

生成对比汇总表，显示各策略在不同时间段的表现差异。

#### 模式2：特定时间段分析
用户自定义时间范围进行分析。

#### 模式3：滚动时间窗口分析
使用6个月滚动窗口分析策略表现趋势。

## 输出文件

### 带时间后缀的文件命名
- `策略净值数据_from_2023-01-01_to_2023-12-31.csv`
- `策略收益率数据_from_2024-01-01.csv`
- `策略统计指标_to_2023-12-31.csv`

### 时间对比分析文件
- `时间段对比汇总.csv` - 各策略在不同时间段的表现对比
- `滚动窗口分析结果.csv` - 滚动窗口分析结果
- `策略统计指标_2023年全年.csv` - 各时间段的详细统计

## 使用示例

### 示例1：分析2023年策略表现
```bash
python Stock_analysis/run_analysis.py --start 2023-01-01 --end 2023-12-31
```

**结果显示：**
- 1000策略：年化收益18.56%，波动率6.88%，最大回撤-5.78%
- 500barra策略：年化收益22.20%，波动率5.58%，最大回撤-1.60%
- mlp500策略：年化收益90.94%，波动率8.55%，最大回撤-1.42%

### 示例2：对比2023年vs 2024年表现
运行时间对比分析，可以看到：

**2023年表现（正收益）：**
- mlp500：+90.94%年化收益
- 500barra：+22.20%年化收益
- 1000：+18.56%年化收益

**2024年表现（负收益）：**
- mlp500：-93.54%年化收益
- 500barra：-90.15%年化收益
- 1000：-93.18%年化收益

### 示例3：分析最近6个月表现
```bash
python Stock_analysis/run_analysis.py --start 2024-07-01
```

**表现最佳策略：**
- 1000：+69.53%年化收益
- A500：+27.52%年化收益
- air：+15.13%年化收益

## 技术特性

### 1. 智能数据过滤
- 自动应用时间过滤条件
- 显示过滤前后的数据量
- 验证时间范围有效性

### 2. 灵活的时间格式
- 支持标准日期格式：YYYY-MM-DD
- 支持部分时间范围（只指定开始或结束日期）
- 自动处理边界情况

### 3. 结果文件管理
- 自动生成时间后缀避免文件覆盖
- 清晰的文件命名规则
- 支持批量时间段分析

### 4. 性能优化
- 在数据加载阶段进行时间过滤
- 减少内存使用和计算时间
- 支持大数据集的高效处理

## 注意事项

1. **日期格式**：请使用YYYY-MM-DD格式，如2023-01-01
2. **数据可用性**：系统会自动检查指定时间范围内是否有数据
3. **策略覆盖**：不同时间段可能包含不同的策略组合
4. **文件管理**：建议定期清理生成的分析文件

## 常见问题

**Q: 如何分析特定月份的数据？**
A: 使用--start和--end参数指定月份范围，如：
```bash
python Stock_analysis/run_analysis.py --start 2023-06-01 --end 2023-06-30
```

**Q: 如何比较不同策略在相同时间段的表现？**
A: 使用时间范围对比分析工具，选择模式1进行自动对比分析。

**Q: 生成的文件太多怎么办？**
A: 可以创建子文件夹按时间段整理，或使用脚本批量处理结果文件。

## 更新日志

- **v2.0** - 新增时间选择功能
- **v2.1** - 添加交互式时间选择
- **v2.2** - 新增时间范围对比分析工具
- **v2.3** - 添加滚动窗口分析功能