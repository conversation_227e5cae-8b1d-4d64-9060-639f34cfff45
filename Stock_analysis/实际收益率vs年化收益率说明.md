# 实际收益率 vs 年化收益率说明

## 概念区别

### 实际收益率（总收益率）
- **定义**：策略在整个投资期间的总收益率
- **计算公式**：(期末净值 / 期初净值) - 1
- **特点**：反映策略在实际投资期间的真实表现

### 年化收益率
- **定义**：将实际收益率换算成年化的收益率
- **计算公式**：(1 + 实际收益率)^(1/投资年数) - 1
- **特点**：便于不同时间段策略的对比分析

## 实际案例对比

### 案例1：1000策略（2023年数据）
- **投资期间**：322天（0.88年）
- **实际收益率**：16.20%
- **年化收益率**：18.56%
- **解读**：在0.88年内获得16.20%收益，相当于年化18.56%

### 案例2：mlp500策略（2023年数据）
- **投资期间**：18天（0.05年）
- **实际收益率**：3.24%
- **年化收益率**：90.94%
- **解读**：在18天内获得3.24%收益，但年化收益率高达90.94%（仅供参考）

### 案例3：500策略（2024年数据）
- **投资期间**：528天（1.45年）
- **实际收益率**：-71.20%
- **年化收益率**：-57.73%
- **解读**：在1.45年内亏损71.20%，年化亏损率57.73%

## 使用建议

### 1. 短期数据（< 3个月）
- **重点关注**：实际收益率
- **原因**：年化收益率容易产生误导
- **示例**：mlp500的3.24%实际收益率比90.94%年化收益率更有参考价值

### 2. 中期数据（3个月 - 1年）
- **综合考虑**：实际收益率和年化收益率
- **注意**：年化收益率仍需谨慎解读
- **示例**：1000策略的16.20%实际收益率和18.56%年化收益率都有参考价值

### 3. 长期数据（> 1年）
- **重点关注**：年化收益率
- **原因**：更好地反映策略的长期表现
- **示例**：500策略的-57.73%年化收益率比-71.20%实际收益率更适合对比

## 系统输出示例

### 修改前（只有年化收益率）
```
mlp500:
  年化收益率: 90.94%
  年化波动率: 8.55%
  最大回撤: -1.42%
```

### 修改后（同时显示两种收益率）
```
mlp500:
  数据时间跨度: 18天 (0.05年)
  实际收益率: 3.24%
  年化收益率: 90.94%
  ⚠️  注意：时间跨度过短，年化收益率仅供参考
  年化波动率: 8.55%
  最大回撤: -1.42%
```

## CSV文件字段说明

更新后的统计指标CSV文件包含以下字段：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 策略 | 策略名称 | 1000 |
| 数据天数 | 实际数据天数 | 322 |
| 数据年数 | 换算成的年数 | 0.88 |
| **实际收益率** | **总收益率** | **0.1620 (16.20%)** |
| 年化收益率 | 年化后的收益率 | 0.1856 (18.56%) |
| 年化波动率 | 年化波动率 | 0.0688 (6.88%) |
| 最大回撤 | 最大回撤率 | -0.0578 (-5.78%) |
| 收益风险比 | 年化收益率/年化波动率 | 2.70 |

## 实际应用场景

### 1. 策略评估
- **短期策略**：主要看实际收益率，判断策略在特定时期的表现
- **长期策略**：主要看年化收益率，评估策略的长期盈利能力

### 2. 策略对比
- **相同时间段**：可以直接比较实际收益率
- **不同时间段**：应该比较年化收益率

### 3. 投资决策
- **资金配置**：结合实际收益率和年化收益率，评估策略的风险收益特征
- **时间选择**：根据策略在不同时间段的实际表现，选择合适的投资时机

## 注意事项

1. **时间跨度影响**：时间跨度越短，年化收益率的参考价值越低
2. **市场环境**：不同市场环境下的收益率不能简单类比
3. **样本偏差**：短期高收益不代表长期表现
4. **风险考量**：高收益往往伴随高风险，需要综合评估

## 总结

实际收益率和年化收益率各有其适用场景：
- **实际收益率**：反映真实投资体验，适合短期和具体时段分析
- **年化收益率**：便于标准化比较，适合长期和跨时段分析

在使用时应该根据具体情况选择合适的指标，并结合时间跨度、市场环境等因素进行综合判断。