# -- coding: utf-8 --**
import os
import pandas as pd
from datetime import datetime, timedelta
from module import FileExtractor


class CTA_settle_file_data:
    def __init__(self):
        pass

    def data_split_deal(self, datasrc, split_begin, split_end):
        datasrc = datasrc.iloc[split_begin:split_end, :]
        datasrc_list = datasrc['财信期货有限公司'].tolist()
        result = list()
        for i in datasrc_list:
            if '--' in i:
                datasrc_list.remove(i)
            if '共' in i:  # 删掉包含共后续所有数据
                del datasrc_list[datasrc_list.index(i):]
        for j in datasrc_list:
            result.append(j.split('|'))
        df = pd.DataFrame(result)
        df.replace('\s+', '', regex=True, inplace=True)
        df.columns = df.iloc[2, :]
        return df.iloc[3:, 1:-1]  # 去掉中文开头/index/空白列

    def settle_data_split_deal(self, datasrc, date):
        table_data_index = datasrc[datasrc['财信期货有限公司'].str.contains(
            '成交记录|平仓明细|持仓明细|持仓汇总')].index.tolist()
        print(table_data_index)
        # 按-符号拆分数据表，以布尔值确定分割位置
        # settleData = self.data_split_deal(datasrc, table_data_index[2], table_data_index[3])  # 结算数据
        tradeData = self.data_split_deal(datasrc, table_data_index[0], table_data_index[1])  # 交易明细
        closeData = self.data_split_deal(datasrc, table_data_index[1], table_data_index[2])  # 平仓明细
        holdingDetail = self.data_split_deal(datasrc, table_data_index[2], table_data_index[3])  # 持仓明细
        holdingData = self.data_split_deal(datasrc, table_data_index[3], len(datasrc))  # 持仓汇总
        holdingData['date'] = date
        # settleData['date'] = date
        tradeData['date'] = date
        closeData['date'] = date
        holdingDetail['date'] = date
        # print(holdingData)
        return [tradeData, closeData, holdingDetail, holdingData]

    def settle_file_data(self, srcpath, file_name, settle_file_name):
        file = FileExtractor(srcpath, file_name)
        file.extract_rar()
        datasrc = pd.read_table(os.path.join(file.extract_path, str(settle_file_name)), skipinitialspace=True,
                                encoding='gbk')
        print(datasrc)
        file.cleanup()
        return datasrc

def symbol_get(data):
    config = pd.read_excel('config.xlsx')
    data['code'] = data['symbol'].apply(lambda x: x.rstrip('0123456789'))  # 取合约代码
    data['code'] = data['code'].apply(lambda x: x.upper())  # 合约代码全部大写，与config列一致
    data['name_value'] = data['value'].apply(lambda x: abs(x))
    # data = data[~data['code'].str.contains('IF|IH|IC|IM')]  # 剔除股指合约T|TF|TS|TL
    # 合并合约名称、行业等数据
    data['name_value'] = data['value'].apply(lambda x: abs(x))
    data = pd.merge(data, config, on='code')
    return data

def holding_data(date, file_name, srcpath, choice=0):
    # day = TradeDate()
    date = datetime.strptime(date, '%Y%m%d')
    today = datetime.now()
    all_tradeData = pd.DataFrame()
    all_closeData = pd.DataFrame()
    all_holdingDetail = pd.DataFrame()
    all_holdingData = pd.DataFrame()
    while date < today:
        date_str = date.strftime('%Y%m%d')
        src_path = srcpath + date_str[4:]
        filename = file_name[0] + date_str
        # print(date_str,srcpath,filename,date_str[4:])
        if os.path.exists(src_path + '\\' + filename + '.rar'):
            try:
                cta = CTA_settle_file_data()
                files_data = cta.settle_file_data(src_path, filename, file_name[1])
                tradeData, closeData, holdingDetail, holdingData = cta.settle_data_split_deal(files_data, date_str)
                if not choice:
                    # CTA风险指标计算
                    risk_signal_calc(file_name[0], holdingData, src_path)
                all_holdingData = pd.concat([all_holdingData, holdingData])
                all_holdingDetail = pd.concat([all_holdingDetail, holdingDetail])
                all_tradeData = pd.concat([all_tradeData, tradeData])
                all_closeData = pd.concat([all_closeData, closeData])
                date += timedelta(days=1)
            except:
                date += timedelta(days=1)
                continue
        else:
            date += timedelta(days=1)
            continue
    if choice:
        with pd.ExcelWriter('D:\\OneDrive\\工作\\财信证券\\1.风控报表\\1.日报\\CTA历史数据汇总.xlsx',
                            engine='openpyxl', ) as f:
            all_holdingData.to_excel(f, index=False, sheet_name='holdingData')
            all_holdingDetail.to_excel(f, index=False, sheet_name='holdingDetail')
            all_tradeData.to_excel(f, index=False, sheet_name='tradeData')
            all_closeData.to_excel(f, index=False, sheet_name='closeData')

    return all_holdingData


def op_cffex_data(date):
    file_name = ['12043336财信CTA1号', '12043336.txt']  # ['18180001财信CTA2号' , '18180001.txt']
    srcpath = "D:\\OneDrive\\工作\\财信证券\\1.风控报表\\1.日报\\"
    holdings = holding_data(date, file_name, srcpath, 1)
    op_cffex = holdings[holdings['Instrument'].str.contains('MO|HO|IO')]
    op_cffex.to_excel('D:\\OneDrive\\工作\\财信证券\\1.风控报表\\1.日报\\中金所期权历史持仓.xlsx', index=False)
    # print(op_cffex)


def risk_signal_calc(name, data, srcpath):
    print(data)
    data['MarketValue(Short)'] = data['MarketValue(Short)'].astype(float)
    data['MarketValue(Long)'] = data['MarketValue(Long)'].astype(float)
    data['MarginOccupied'] = data['MarginOccupied'].astype(float)
    cta_src = pd.read_excel(srcpath + '\\Fund.xlsx')
    cta_value = cta_src.iloc[(int(name[-2]) - 2), 0]
    margin = data['MarginOccupied'].sum()
    marginRatio = data['MarginOccupied'].sum() / cta_value
    op_value = data['MarketValue(Short)'].sum() + data['MarketValue(Long)'].sum()
    op_data = data[(data['MarketValue(Short)'] != 0) | (data['MarketValue(Long)'] != 0)]
    future_data = data[~((data['MarketValue(Short)'] != 0) | (data['MarketValue(Long)'] != 0))]
    future_margin_max = future_data['MarginOccupied'].max()
    future_margin_max_name = future_data.loc[future_data['MarginOccupied'].idxmax(), 'Instrument']
    op_margin = op_data['MarginOccupied'].sum()
    col_names = ['产品', '保证金', '风险度', '权利金', '期权保证金', '期货最大保证金', '期货最大保证金合约']
    result = [[name[-5:], margin, marginRatio, op_value, op_margin, future_margin_max, future_margin_max_name]]
    result_df = pd.DataFrame(result, columns=col_names)
    print(result_df)
    with pd.ExcelWriter(srcpath + '\\CTA数据.xlsx', mode='a', engine='openpyxl', if_sheet_exists='replace') as writer:
        result_df.to_excel(writer, index=False, sheet_name=name[-5:])
    return 1


def cta_risk_signal(date):
    file_name = [['99101087多策略CTA3号', '99101087.txt'], ['18180001财信CTA2号', '18180001.txt']]
    srcpath = "D:\\OneDrive\\工作\\财信证券\\1.风控报表\\1.日报\\"
    holding_data(date, file_name[0], srcpath)
    holding_data(date, file_name[1], srcpath)


def main():
    # op_cffex_data('20240920')  # 中金所期权数据
    cta_risk_signal('20250101')


if __name__ == '__main__':
    main()
