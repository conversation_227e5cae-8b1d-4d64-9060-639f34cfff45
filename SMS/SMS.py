# -*- coding: utf-8 -*-
import pandas as pd
from module import TradeDate, PathManager
from datetime import datetime


def read_asset_unint(x):
    stock = pd.read_excel(x)
    # 去掉stock首列的空行
    stock = stock[~(stock['序号'].isnull())]
    value = stock.tail(1)['单元资产净值(净价)'].values[0]  # 取单元资产净值
    margin = stock.tail(1)['占用保证金(静态)'].values[0]  # 取期货账户保证金
    all_margin = stock.loc[stock['资产单元名称'] == '多策略投资-期货投机', '单元资产净值(净价)'].values[
        0]  # 获取期货账户总金额
    return value / 10000, margin, all_margin


def filepath():
    srcpath = "D:\\OneDrive\\工作\\财信证券\\1.风控报表\\1.日报\\"
    filepaths = list()
    date = TradeDate()
    filepath = PathManager()
    filename = [
        '综合信息查询_单元资产_' + date.get_trade_date(1, '%Y%m%d') + '.xls',
        '综合信息查询_单元资产_' + date.get_trade_date(0, '%Y%m%d') + '.xls',
        'Fund.xlsx', 'CTA数据.xlsx', '股票数据.xlsx', 'underlying_summary.xlsx',
        date.get_trade_date(0, '%Y%m%d') + '.txt',
        '部门盈亏数据.xlsx']

    filepaths.append(
        filepath.get_full_path(srcpath, date.get_trade_date(1, '%m%d'),
                               filename[0]))  # 0 获取T-1日单元资产表，目标根据资产净值重新计算T-1日盈亏，更新T-1日的数据
    filepaths.append(
        filepath.get_full_path(srcpath, date.get_trade_date(0, '%m%d'), filename[1]))  # 1 获取本日单元资产表，目标数据：资产净值、基金净值
    filepaths.append(
        filepath.get_full_path(srcpath, date.get_trade_date(1, '%m%d'), filename[2]))  # 2 获取T-1日委外产品表，目标数据：基金净值
    filepaths.append(filepath.get_full_path(srcpath, date.get_trade_date(0, '%m%d'), filename[3]))  # 3 T日CTA盈亏数据
    filepaths.append(filepath.get_full_path(srcpath, date.get_trade_date(0, '%m%d'), filename[4]))  # 4 T日股票盈亏数据
    filepaths.append(filepath.get_full_path(srcpath, date.get_trade_date(0, '%m%d'), filename[5]))  # 5 T日期权敞口数据
    filepaths.append(filepath.get_full_path(srcpath, date.get_trade_date(0, '%m%d'), filename[6]))  # 6 输出txt位置
    filepaths.append(filepath.get_full_path(srcpath, date.get_trade_date(0, '%m%d'), filename[7]))  # 7 T日估算净值更新T日部门盈亏
    filepaths.append(
        filepath.get_full_path(srcpath, date.get_trade_date(1, '%m%d'), filename[7]))  # 8 T-1日净值更新后重算T-1日部门盈亏
    filepaths.append(filepath.get_full_path(srcpath, date.get_trade_date(0, '%m%d'), 'all_strategy.xlsx'))  # 9 输出汇总数据位置
    filepaths.append(filepath.get_full_path(srcpath, date.get_trade_date(1, '%m%d'), filename[3]))  # 10 T-1日CTA盈亏数据
    filepaths.append(filepath.get_full_path(srcpath, date.get_trade_date(2, '%m%d'), filename[3]))  # 11 T-2日CTA盈亏数据
    return filepaths


def print_txt(filename, context):
    with open(filename, 'w', encoding='UTF-8') as f:
        f.write(context)


def main():
    today = TradeDate()
    # day = datetime.now()
    filepaths = filepath()  # 获取所有路径
    # print(filepaths)

    # 部门初始资产
    asset_initial = 47757.05193
    cta3_initial = 3000.00
    cta2_initial = 9846.65 - 2019.083
    # 现金管理平仓盈亏
    interest_pnl = 1920314.58  # 利息返还
    cash_gc = 73346.36  # 逆回购
    # 上下账、分红调整项
    delta = -1283586.7+1375410.71
    wwsh = -1626179.96 / 10000.0
    # 资产表数据读取、处理
    asset_T1_all, marginT1, all_margrinT1 = read_asset_unint(filepaths[0])  # T-1日资产表数据
    asset_T_all, marginT, all_margrinT = read_asset_unint(filepaths[1])  # T日资产表数据
    # 委外数据读取、处理
    fund = pd.read_excel(filepaths[2])  # T-1日委外盈利
    funds = fund.iloc[:, 0].to_list()
    cta3_T1 = funds[1] / 10000  # T-1日CTA1产品净值
    cta2_T1 = funds[0] / 10000  # T-1日CTA2产品净值
    # hr_T1 = funds[2] / 10000  # T-1日翰荣产品净值
    # 股票数据读取、处理
    stock = pd.read_excel(filepaths[4], sheet_name='元数据', index_col=0)  # T日股票数据
    # print(stock)
    stock_O32 = stock.loc['O32', '投资规模']  # O32股票规模
    # stock_hr = stock.loc['翰荣五号', '投资规模']  # 翰荣股票规模
    # hr_pnl = stock.loc['翰荣五号', '当日浮动盈亏']  # T日翰荣盈亏
    mm_fund_pnl = stock.loc['货基', '当日浮动盈亏']  # T日货基盈亏
    stock_pnl = stock.loc['总计', '当日浮动盈亏']  # T日股票整体盈亏
    stock_pnl_pct = stock.loc['总计', '时点收益率']  # T日股票整体盈亏比例
    stock_ex = stock.loc['总计', '投资规模']  # 股票净敞口
    stock_value = stock_O32  # + stock_hr  # T日股票总资产
    stock_ex_pct = stock_ex / 10000  # T日股票净敞口比例
    # cta数据读取、处理
    cta = pd.read_excel(filepaths[3], sheet_name='元数据')  # T日CTA数据

    op_sell = cta.iloc[0, :].to_list()
    cta_trend = cta.iloc[1, :].to_list()
    op_index_bias = cta.iloc[2, :].to_list()
    cta_other = cta.iloc[3, :].to_list()
    cta_op_pnl = op_sell[0]  # 卖权T日盈亏
    cta_op_allpnl = op_sell[1]  # 卖权今年以来盈亏
    cta_op_pnl_pct = op_sell[2]  # T日卖权盈亏比例
    cta_tdcy_pnl = cta_trend[0]  # 趋势策略盈亏
    cta_tdcy_allpnl = cta_trend[1]  # 趋势策略5.7以来盈亏
    cta_tdcy_pnl_pct = cta_trend[2]  # T日趋势策略盈亏比例
    cta_tdcy_nval = cta_trend[3]  # 趋势策略净值
    cta_tdcy_leverage = cta_trend[4]  # 趋势策略杠杆
    cta_op_bias_pnl = op_index_bias[0]  # 期现价差策略盈亏
    # cta_op_bias_allpnl = op_index_bias[1]  # 期现价差策略7.10以来盈亏
    # cta_op_bias_pnl_pct = op_index_bias[2]  # T日期现价差策略盈亏比例
    cta_other_pnl = cta_other[0]  # 截面策略盈亏
    cta_other_allpnl = cta_other[1]  # 截面策略7.30以来盈亏
    cta_other_pnl_pct = cta_other[2]  # T日截面策略盈亏比例
    cta_other_nval = cta_other[3]  # 截面策略净值
    cta_other_leverage = cta_other[4]  # 截面策略杠杆

    # 保证金数据
    ctaT1 = pd.read_excel(filepaths[10], sheet_name='CTA2号')  # T1日CTA2号数据
    ctaT2 = pd.read_excel(filepaths[11], sheet_name='CTA2号')  # T2日CTA2号数据
    cta1_margin = ctaT1.iloc[0, 1]
    cta1_riskratio = ctaT1.iloc[0, 2]
    cta1_op_margin = ctaT1.iloc[0, 4]
    cta2_margin = ctaT2.iloc[0, 1]
    cta2_op_margin = ctaT2.iloc[0, 4]

    cta3T1 = pd.read_excel(filepaths[10], sheet_name='CTA3号')  # T1日CTA2号数据
    cta3T2 = pd.read_excel(filepaths[11], sheet_name='CTA3号')  # T2日CTA2号数据
    cta31_margin = cta3T1.iloc[0, 1]
    cta31_riskratio = cta3T1.iloc[0, 2]
    # cta31_op_margin = cta3T1.iloc[0, 4]
    cta32_margin = cta3T2.iloc[0, 1]
    # cta32_op_margin = cta3T2.iloc[0, 4]

    # 期权数据读取
    greeks = pd.read_excel(filepaths[5], index_col=0)  # T日期权数据
    op_cashgreeks = greeks.iloc[-1, 1:4].to_list()

    # 计算T日现金管理盈亏
    cash_pnl = (mm_fund_pnl + interest_pnl + cash_gc + delta) / 10000

    # 要素数据

    cta3_T = cta3_T1 + cta_other_pnl  # cta1号T日净值
    cta2_T = cta2_T1 + cta_tdcy_pnl + cta_op_pnl + cta_op_bias_pnl  # cta2号T日净值
    # hr_T = hr_T1  # + hr_pnl  # 翰荣T日净值
    assetT1 = asset_T1_all + cta2_T1 + cta3_T1  # T1日部门资产
    asset = asset_T_all + cta2_T + cta3_T  # T日部门资产
    pnl_all = asset - asset_initial  # T日部门总体盈亏
    pnl_all_pct = pnl_all / asset_initial  # T日部门总体盈亏比例
    stock_all_pnl = pnl_all - wwsh - (cta2_T - cta2_initial) - (cta3_T - cta3_initial) - cash_pnl
    today_pnl = asset - assetT1  # T日部门盈亏，需要补充现金盈亏情况

    all_strategy = [
        ['股票策略', "{:.2f}（{}）".format(stock_pnl, stock_pnl_pct), stock_O32.round(2), stock_all_pnl.round(2), '/',
         stock_ex.round(2)],
        ['CTA趋势策略', "{:.2f}（{}）".format(cta_tdcy_pnl, cta_tdcy_pnl_pct), '10000', cta_tdcy_allpnl.round(2),
         cta_tdcy_leverage, cta_tdcy_nval.round(2)],
        ['CTA截面策略', "{:.2f}（{}）".format(cta_other_pnl, cta_other_pnl_pct), '5000', cta_other_allpnl.round(2),
         cta_other_leverage, cta_other_nval.round(2)],
        ['期权策略', "{:.2f}（{}）".format(cta_op_pnl, cta_op_pnl_pct), '5000', cta_op_allpnl.round(2), '/', '/']
    ]
    all_strategy_colnum = ['type', 'pnl_today', 'initial_asset', 'pnl_all', 'levelrage', 'net_export']
    all_strategy_df = pd.DataFrame(all_strategy, columns=all_strategy_colnum)
    print(all_strategy_df)
    all_strategy_df.to_excel(filepaths[9], index=False)

    context = [
        '{}多策略投资部投资情况'.format(today.get_trade_date(0, '%Y%m%d')),
        '投资规模：{:.2f}万元'.format(asset),
        '当日盈亏：{:.2f}万元'.format(today_pnl),
        '总体盈亏：{:.2f}万元'.format(pnl_all),
        '时点收益率：{:.2%}'.format(pnl_all_pct),
        '其中：',
        '股票策略',
        '-股票持仓：{:.2f}万元'.format(stock_O32),
        '-总体盈亏：{:.2f}万元'.format(stock_all_pnl),
        '衍生品策略',
        '-CTA2号规模：{:.2f}万元'.format(cta2_T),
        '-CTA2号盈亏：{:.2f}万元'.format(cta2_T - cta2_initial),
        '-CTA3号规模：{:.2f}万元'.format(cta3_T),
        '-CTA3号盈亏：{:.2f}万元'.format(cta3_T - cta3_initial),
        '已完结产品盈亏：{:.2f}万元'.format(wwsh),
        '现金管理：{:.2f}万元'.format(cash_pnl),
        '\n',
        # '截止{:n}月{:n}日收盘，多策略投资部总盈亏约{:.2f}万元，当日盈亏{:.2f}万元（{:.3%}）：'.format(
        #    day.month, day.day, pnl_all, today_pnl, (today_pnl / asset)),
        # '-股票策略盈亏约{:.2f}万元（{}，其中中性策略超额11.46万元，敞口0.71万元，结算基差2.36万元；敞口策略盈亏万元），\n持仓规模{:.2f}万元，净敞口规模{:.2f}万元，净敞口占授权规模（6000万）比例{:.2%}；'.format(
        #    stock_pnl, stock_pnl_pct, stock_value, stock_ex, stock_ex_pct),
        # '-CTA趋势策略盈亏约{:.2f}万元（{}），投入规模10000万元，2025年累计盈亏{:.2f}万元，本日策略杠杆率{}，净敞口规模为{:.2f}万元；'.format(
        #    cta_tdcy_pnl, cta_tdcy_pnl_pct, cta_tdcy_allpnl, cta_tdcy_leverage, cta_tdcy_nval),
        # '-CTA截面策略盈亏约{:.2f}万元（{}），投入规模5000万元，2025年累计盈亏{:.2f}万元，本日策略杠杆率{}，净敞口规模为{:.2f}万元；'.format(
        #    cta_other_pnl, cta_other_pnl_pct, cta_other_allpnl, cta_other_leverage, cta_other_nval),
        # '-期权策略盈亏约{:.2f}万元（{}），投入规模5000万元，2025年累计盈亏{:.2f}万元，{}'.format(cta_op_pnl, cta_op_pnl_pct,
        # cta_op_allpnl,
        # op_cashgreeks),
        # '-股指基差策略（暂停运行）盈亏约{:.2f}万元（{}），7月10日以来累计盈亏{:.2f}万元。'.format(cta_op_bias_pnl,
        #                                                                                    cta_op_bias_pnl_pct,
        #                                                                                    cta_op_bias_allpnl),
        # '-差额为现金管理收益。',
        # '请知悉。'

    ]
    # 头部
    report = f"\n\n\n▣▣ 多策略投资部日报 {datetime.now().strftime('%Y%m%d')} \n\n"
    # 汇总行（新增净敞口）
    report += f"▶ 当日总盈亏：{today_pnl:.2f}万元 ({(today_pnl / asset):.2%})\n"
    report += f"▶ 全年累计：{pnl_all:.2f}万元 ({(pnl_all / asset):.2%})\n"
    report += f"▶ 股票净敞口：{stock_ex:.2f} 万元(已使用{stock_ex_pct:.2%})\n"
    report += f"▶ 现金管理（不含CTA返还）：{cash_pnl:.2f} 万元\n\n"
    # 股票策略
    report += f"▌股票策略：\n"
    report += f"   → 本日盈亏:{stock_pnl:.2f}万元 ({stock_pnl_pct}) | 累计盈亏:{stock_all_pnl:.2f}万元\n"
    report += f"   → 持仓规模:{stock_value:.2f}万元\n"
    report += f"   → 超额:万元 | 敞口:万元 | 基差:万元\n"
    report += f"   → 股指合约隐含基差成本:万元\n"
    # 衍生品策略
    report += f"▌衍生品策略：\n"
    report += f"  ▪CTA趋势策略：（投入规模10000万元）\n"
    report += f"    → 本日盈亏:{cta_tdcy_pnl:.2f}万元 ({cta_tdcy_pnl_pct}) | 累计盈亏:{cta_tdcy_allpnl:.2f}万元\n"
    report += f"    → 杠杆:{cta_tdcy_leverage} | 风险敞口:{cta_tdcy_nval:.2f}万元\n"
    report += f"  ▪CTA截面策略：（投入规模5000万元）\n"
    report += f"    → 本日盈亏:{cta_other_pnl:.2f}万元 ({cta_other_pnl_pct}) | 累计盈亏:{cta_other_allpnl:.2f}万元\n"
    report += f"    → 杠杆:{cta_other_leverage} | 风险敞口:{cta_other_nval:.2f}万元\n"
    report += f"  ▪期权策略：（投入规模5000万元）\n"
    report += f"    → 本日盈亏:{cta_op_pnl:.2f}万元 ({cta_op_pnl_pct})| 累计盈亏:{cta_op_allpnl:.2f}万元\n"
    report += f"    → 风险敞口:$delta {op_cashgreeks[0]:.2f}万元 | $gamma {op_cashgreeks[1]:.2f}万元 | $vega {op_cashgreeks[2]:.2f}万元\n"
    report += f"▌保证金占用：\n"
    report += f"  ▪股指期货（T日）：{marginT / 10000.0:.2f}万元（风险度:{(marginT / all_margrinT):.2%}）\n"
    report += f"  ▪CTA2号（T-1日）：{cta1_margin / 10000.0:.2f}万元（风险度:{cta1_riskratio:.2%}）\n"
    report += f"    → 期货:{(cta1_margin - cta1_op_margin) / 10000.0:.2f}万元 | 较前日 {(cta1_margin - cta1_op_margin - cta2_margin + cta2_op_margin) / 10000.0:.2f}万元\n"
    report += f"    → 期权:{cta1_op_margin / 10000.0:.2f}万元 | 较前日 {(cta1_op_margin - cta2_op_margin) / 10000.0:.2f}万元\n"
    report += f"  ▪CTA3号（T-1日）：{cta31_margin / 10000.0:.2f}万元（风险度:{cta31_riskratio:.2%}）\n"
    report += f"    → 期货:{(cta31_margin) / 10000.0:.2f}万元 | 较前日 {(cta31_margin - cta32_margin) / 10000.0:.2f}万元\n"
    report += f"▌当日持仓调整\n"
    report += f"  ▪中证500 万元 \n"
    report += f"  ▪CTA截面策略 万元 \n"
    report += f"  ▪期权策略 万元 \n"
    print(report)
    # context += report
    print_txt(filepaths[6], '\n'.join(context) + report)
    print('\n'.join(context))
    # T-1日部门数据
    print('T-1日部门盈亏：{}'.format(assetT1))


if __name__ == '__main__':
    main()
