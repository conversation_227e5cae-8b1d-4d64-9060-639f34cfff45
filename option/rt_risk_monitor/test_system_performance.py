#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期权实时风险监控系统性能测试脚本

测试内容：
1. 数据导入完整性检查
2. 计算效率测试
3. 前后端同步性测试
4. 系统压力测试
"""

import requests
import time
import pandas as pd
import json
from datetime import datetime
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SystemPerformanceTester:
    def __init__(self, base_url="http://localhost:5002"):
        self.base_url = base_url
        self.test_results = {}
        
    def test_data_import_completeness(self):
        """测试数据导入完整性"""
        print("=" * 50)
        print("1. 数据导入完整性测试")
        print("=" * 50)
        
        try:
            # 检查当前数据状态
            response = requests.get(f"{self.base_url}/api/status", timeout=10)
            if response.status_code == 200:
                status = response.json()
                print(f"系统初始化状态: {status['initialized']}")
                print(f"当前数据文件: {status['current_file']}")
                print(f"数据条数: {status['data_count']}")
                print(f"系统时间: {status['system_time']}")
                
                self.test_results['data_import'] = {
                    'status': 'PASS' if status['data_count'] > 0 else 'FAIL',
                    'data_count': status['data_count'],
                    'initialized': status['initialized']
                }
            else:
                print(f"无法获取系统状态: HTTP {response.status_code}")
                self.test_results['data_import'] = {'status': 'FAIL', 'error': 'API不可访问'}
                
        except Exception as e:
            print(f"数据导入测试失败: {str(e)}")
            self.test_results['data_import'] = {'status': 'FAIL', 'error': str(e)}
    
    def test_calculation_efficiency(self):
        """测试计算效率"""
        print("\n" + "=" * 50)
        print("2. 计算效率测试")
        print("=" * 50)
        
        try:
            # 连续请求多次API，测试响应时间
            response_times = []
            data_consistency = []
            
            for i in range(10):
                start_time = time.time()
                response = requests.get(f"{self.base_url}/api/data", timeout=10)
                end_time = time.time()
                
                response_time = end_time - start_time
                response_times.append(response_time)
                
                if response.status_code == 200:
                    data = response.json()
                    data_consistency.append(len(data))
                    print(f"请求 {i+1}: 响应时间 {response_time:.3f}s, 数据条数 {len(data)}")
                else:
                    print(f"请求 {i+1}: HTTP错误 {response.status_code}")
                
                time.sleep(0.5)  # 间隔0.5秒
            
            # 计算统计信息
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
            
            print(f"\n响应时间统计:")
            print(f"平均响应时间: {avg_response_time:.3f}s")
            print(f"最大响应时间: {max_response_time:.3f}s")
            print(f"最小响应时间: {min_response_time:.3f}s")
            
            # 检查数据一致性
            data_consistent = len(set(data_consistency)) <= 2  # 允许1-2条数据的差异
            print(f"数据一致性: {'PASS' if data_consistent else 'FAIL'}")
            print(f"数据条数变化: {data_consistency}")
            
            self.test_results['calculation_efficiency'] = {
                'status': 'PASS' if avg_response_time < 1.0 and data_consistent else 'FAIL',
                'avg_response_time': avg_response_time,
                'max_response_time': max_response_time,
                'min_response_time': min_response_time,
                'data_consistent': data_consistent
            }
            
        except Exception as e:
            print(f"计算效率测试失败: {str(e)}")
            self.test_results['calculation_efficiency'] = {'status': 'FAIL', 'error': str(e)}
    
    def test_frontend_backend_sync(self):
        """测试前后端同步性"""
        print("\n" + "=" * 50)
        print("3. 前后端同步性测试")
        print("=" * 50)
        
        try:
            # 获取系统配置
            response = requests.get(f"{self.base_url}/api/status", timeout=10)
            if response.status_code == 200:
                status = response.json()
                backend_interval = status['update_interval']
                frontend_interval = status['frontend_interval']
                
                print(f"后端更新间隔: {backend_interval}s")
                print(f"前端请求间隔: {frontend_interval}s")
                
                # 检查同步性
                sync_ratio = frontend_interval / backend_interval
                is_synchronized = 0.8 <= sync_ratio <= 1.2  # 允许20%的误差
                
                print(f"同步比率: {sync_ratio:.2f}")
                print(f"同步状态: {'PASS' if is_synchronized else 'FAIL'}")
                
                if not is_synchronized:
                    print("建议: 调整前后端更新间隔以提高同步性")
                
                self.test_results['sync_test'] = {
                    'status': 'PASS' if is_synchronized else 'FAIL',
                    'backend_interval': backend_interval,
                    'frontend_interval': frontend_interval,
                    'sync_ratio': sync_ratio
                }
            else:
                print(f"无法获取同步信息: HTTP {response.status_code}")
                self.test_results['sync_test'] = {'status': 'FAIL', 'error': 'API不可访问'}
                
        except Exception as e:
            print(f"同步性测试失败: {str(e)}")
            self.test_results['sync_test'] = {'status': 'FAIL', 'error': str(e)}
    
    def test_system_stress(self):
        """系统压力测试"""
        print("\n" + "=" * 50)
        print("4. 系统压力测试")
        print("=" * 50)
        
        try:
            # 并发请求测试
            import threading
            import queue
            
            result_queue = queue.Queue()
            num_threads = 5
            requests_per_thread = 10
            
            def worker():
                thread_results = []
                for i in range(requests_per_thread):
                    try:
                        start_time = time.time()
                        response = requests.get(f"{self.base_url}/api/data", timeout=5)
                        end_time = time.time()
                        
                        thread_results.append({
                            'success': response.status_code == 200,
                            'response_time': end_time - start_time,
                            'data_count': len(response.json()) if response.status_code == 200 else 0
                        })
                    except Exception as e:
                        thread_results.append({
                            'success': False,
                            'error': str(e),
                            'response_time': None
                        })
                
                result_queue.put(thread_results)
            
            # 启动并发线程
            threads = []
            start_time = time.time()
            
            for i in range(num_threads):
                thread = threading.Thread(target=worker)
                thread.start()
                threads.append(thread)
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # 收集结果
            all_results = []
            while not result_queue.empty():
                all_results.extend(result_queue.get())
            
            # 统计结果
            successful_requests = sum(1 for r in all_results if r['success'])
            total_requests = len(all_results)
            success_rate = successful_requests / total_requests * 100
            
            response_times = [r['response_time'] for r in all_results if r['response_time'] is not None]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            
            print(f"并发线程数: {num_threads}")
            print(f"总请求数: {total_requests}")
            print(f"成功请求数: {successful_requests}")
            print(f"成功率: {success_rate:.1f}%")
            print(f"总耗时: {total_time:.2f}s")
            print(f"平均响应时间: {avg_response_time:.3f}s")
            print(f"请求吞吐量: {total_requests/total_time:.1f} 请求/秒")
            
            self.test_results['stress_test'] = {
                'status': 'PASS' if success_rate >= 95 and avg_response_time < 2.0 else 'FAIL',
                'success_rate': success_rate,
                'avg_response_time': avg_response_time,
                'throughput': total_requests/total_time
            }
            
        except Exception as e:
            print(f"压力测试失败: {str(e)}")
            self.test_results['stress_test'] = {'status': 'FAIL', 'error': str(e)}
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 50)
        print("测试报告")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('status') == 'PASS')
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试数: {passed_tests}")
        print(f"测试通过率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n详细结果:")
        for test_name, result in self.test_results.items():
            status = result.get('status', 'UNKNOWN')
            print(f"- {test_name}: {status}")
            if status == 'FAIL' and 'error' in result:
                print(f"  错误: {result['error']}")
        
        # 保存报告到文件
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'pass_rate': passed_tests/total_tests*100
                },
                'results': self.test_results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n详细报告已保存到: {report_file}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始期权实时风险监控系统性能测试")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.test_data_import_completeness()
        self.test_calculation_efficiency()
        self.test_frontend_backend_sync()
        self.test_system_stress()
        self.generate_report()

def main():
    """主函数"""
    tester = SystemPerformanceTester()
    tester.run_all_tests()

if __name__ == '__main__':
    main()
