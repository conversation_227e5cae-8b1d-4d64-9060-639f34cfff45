#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期权数据问题诊断脚本

检查以下问题：
1. Wind API连接状态
2. 期权代码格式是否正确
3. 静态数据获取是否成功
4. 实时价格订阅是否有效
5. 计算输入数据的有效性
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from WindPy import w
import config

def check_wind_connection():
    """检查Wind API连接"""
    print("=" * 50)
    print("1. Wind API连接检查")
    print("=" * 50)
    
    try:
        w.start()
        is_connected = w.isconnected()
        print(f"Wind API连接状态: {'已连接' if is_connected else '未连接'}")
        
        if is_connected:
            # 测试简单查询
            test_data = w.wsd("000001.SZ", "close", "2025-08-18", "2025-08-18")
            if test_data.ErrorCode == 0:
                print("Wind API功能测试: 正常")
                return True
            else:
                print(f"Wind API功能测试失败: 错误代码 {test_data.ErrorCode}")
                return False
        else:
            print("Wind API连接失败")
            return False
            
    except Exception as e:
        print(f"Wind API检查出错: {str(e)}")
        return False

def analyze_option_codes():
    """分析期权代码格式"""
    print("\n" + "=" * 50)
    print("2. 期权代码格式分析")
    print("=" * 50)
    
    try:
        # 读取数据文件
        try:
            df = pd.read_csv(config.DATA_FILE, encoding='utf-8')
        except UnicodeDecodeError:
            df = pd.read_csv(config.DATA_FILE, encoding='gbk')
        
        df.columns = df.columns.str.strip()
        df_options = df[df['代码'].str.len() > 6].copy()
        
        print(f"总数据行数: {len(df)}")
        print(f"期权数据行数: {len(df_options)}")
        
        # 分析期权代码格式
        print("\n期权代码样例:")
        for i, symbol in enumerate(df_options['代码'].head(10)):
            print(f"  {i+1}. {symbol}")
        
        # 检查交易所信息
        if '交易所' in df_options.columns:
            exchanges = df_options['交易所'].value_counts()
            print(f"\n交易所分布:")
            for exchange, count in exchanges.items():
                print(f"  {exchange}: {count}个合约")
        
        return df_options
        
    except Exception as e:
        print(f"期权代码分析出错: {str(e)}")
        return pd.DataFrame()

def test_wind_static_data(df_options):
    """测试Wind静态数据获取"""
    print("\n" + "=" * 50)
    print("3. Wind静态数据获取测试")
    print("=" * 50)
    
    if df_options.empty:
        print("没有期权数据可测试")
        return
    
    # 构建Wind代码
    sample_symbols = df_options['代码'].head(5).tolist()
    sample_exchanges = df_options['交易所'].head(5).tolist() if '交易所' in df_options.columns else ['DCE'] * 5
    
    wind_codes = []
    for symbol, exchange in zip(sample_symbols, sample_exchanges):
        suffix = '.' + exchange[:3].upper()
        wind_code = symbol + suffix
        wind_codes.append(wind_code)
    
    print(f"测试Wind代码样例:")
    for i, code in enumerate(wind_codes):
        print(f"  {i+1}. {code}")
    
    # 测试静态数据获取
    try:
        today_str = datetime.now().strftime("%Y%m%d")
        trade_date_option = f"tradeDate={today_str};"
        codes_str = ",".join(wind_codes)
        
        print(f"\n查询日期: {today_str}")
        print(f"查询字段: ptmday,exe_ratio")
        
        wss_data = w.wss(codes_str, "ptmday,exe_ratio", trade_date_option)
        
        print(f"查询结果错误代码: {wss_data.ErrorCode}")
        
        if wss_data.ErrorCode == 0:
            print("静态数据获取成功!")
            print("\n到期天数数据:")
            for i, (code, days) in enumerate(zip(wss_data.Codes, wss_data.Data[0])):
                print(f"  {code}: {days}天")
            
            print("\n合约乘数数据:")
            for i, (code, mult) in enumerate(zip(wss_data.Codes, wss_data.Data[1])):
                print(f"  {code}: {mult}")
                
        else:
            print(f"静态数据获取失败: 错误代码 {wss_data.ErrorCode}")
            
            # 尝试单个代码查询
            print("\n尝试单个代码查询:")
            for code in wind_codes[:2]:
                single_data = w.wss(code, "ptmday,exe_ratio", trade_date_option)
                print(f"  {code}: 错误代码 {single_data.ErrorCode}")
                if single_data.ErrorCode == 0 and single_data.Data:
                    print(f"    到期天数: {single_data.Data[0][0] if single_data.Data[0] else 'N/A'}")
                    print(f"    合约乘数: {single_data.Data[1][0] if len(single_data.Data) > 1 and single_data.Data[1] else 'N/A'}")
        
    except Exception as e:
        print(f"静态数据测试出错: {str(e)}")

def test_wind_realtime_data(df_options):
    """测试Wind实时数据获取"""
    print("\n" + "=" * 50)
    print("4. Wind实时数据获取测试")
    print("=" * 50)
    
    if df_options.empty:
        print("没有期权数据可测试")
        return
    
    # 构建测试代码
    sample_symbols = df_options['代码'].head(3).tolist()
    sample_exchanges = df_options['交易所'].head(3).tolist() if '交易所' in df_options.columns else ['DCE'] * 3
    
    wind_codes = []
    underlying_codes = []
    
    for symbol, exchange in zip(sample_symbols, sample_exchanges):
        suffix = '.' + exchange[:3].upper()
        wind_code = symbol + suffix
        wind_codes.append(wind_code)
        
        # 提取标的代码
        import re
        option_regex = re.compile(r"^(.*?)[-]?([cp])[-]?(\d+\.?\d*)$", re.IGNORECASE)
        match = option_regex.match(symbol)
        if match:
            underlying = match.group(1).upper() + suffix
            underlying_codes.append(underlying)
    
    all_codes = wind_codes + underlying_codes
    
    print(f"测试期权代码:")
    for code in wind_codes:
        print(f"  {code}")
    
    print(f"\n测试标的代码:")
    for code in underlying_codes:
        print(f"  {code}")
    
    # 测试实时数据获取
    try:
        codes_str = ",".join(all_codes)
        print(f"\n查询实时价格...")
        
        wsq_data = w.wsq(codes_str, "rt_latest")
        
        print(f"实时数据查询错误代码: {wsq_data.ErrorCode}")
        
        if wsq_data.ErrorCode == 0:
            print("实时数据订阅成功!")
            
            # 等待数据到达
            print("等待实时数据...")
            time.sleep(3)
            
            # 检查数据
            print("\n当前实时价格:")
            for code in all_codes:
                # 这里需要从全局变量中获取价格，但为了测试，我们直接查询
                current_data = w.wsq(code, "rt_latest")
                if current_data.ErrorCode == 0 and current_data.Data:
                    price = current_data.Data[0][0] if current_data.Data[0] else 0
                    print(f"  {code}: {price}")
                else:
                    print(f"  {code}: 无数据 (错误代码: {current_data.ErrorCode})")
        else:
            print(f"实时数据订阅失败: 错误代码 {wsq_data.ErrorCode}")
            
    except Exception as e:
        print(f"实时数据测试出错: {str(e)}")

def test_calculation_inputs():
    """测试计算输入数据"""
    print("\n" + "=" * 50)
    print("5. 计算输入数据检查")
    print("=" * 50)
    
    try:
        # 模拟当前系统的数据获取
        from data import get_realtime_option_data
        
        option_data = get_realtime_option_data()
        
        if option_data.empty:
            print("获取的期权数据为空")
            return
        
        print(f"获取到 {len(option_data)} 条期权数据")
        
        # 检查关键字段
        print("\n关键字段统计:")
        for col in ['S', 'op_price', 't', 'k', 'mult']:
            if col in option_data.columns:
                values = option_data[col]
                zero_count = (values == 0).sum()
                null_count = values.isnull().sum()
                valid_count = len(values) - zero_count - null_count
                
                print(f"  {col}:")
                print(f"    有效值: {valid_count}")
                print(f"    零值: {zero_count}")
                print(f"    空值: {null_count}")
                if valid_count > 0:
                    print(f"    范围: {values[values > 0].min():.4f} - {values[values > 0].max():.4f}")
        
        # 显示样例数据
        print("\n样例数据:")
        print(option_data[['symbol', 'underlying_code', 'S', 'op_price', 't', 'k']].head())
        
        # 检查计算条件
        valid_mask = (option_data['S'] > 0) & (option_data['op_price'] > 0) & (option_data['t'] > 0)
        valid_count = valid_mask.sum()
        
        print(f"\n满足计算条件的合约数: {valid_count}/{len(option_data)}")
        
        if valid_count == 0:
            print("警告: 没有合约满足计算条件!")
            print("可能的问题:")
            print("  1. 标的价格为0 (Wind实时数据未获取)")
            print("  2. 期权价格为0 (Wind实时数据未获取)")
            print("  3. 到期时间为0 (Wind静态数据未获取)")
        
    except Exception as e:
        print(f"计算输入数据检查出错: {str(e)}")

def main():
    """主函数"""
    print("期权数据问题诊断工具")
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查Wind连接
    wind_ok = check_wind_connection()
    
    if not wind_ok:
        print("\n诊断结果: Wind API连接失败，无法继续诊断")
        return
    
    # 2. 分析期权代码
    df_options = analyze_option_codes()
    
    # 3. 测试静态数据
    test_wind_static_data(df_options)
    
    # 4. 测试实时数据
    test_wind_realtime_data(df_options)
    
    # 5. 测试计算输入
    test_calculation_inputs()
    
    print("\n" + "=" * 50)
    print("诊断完成")
    print("=" * 50)
    print("如果发现问题，请检查:")
    print("1. Wind终端是否正常登录")
    print("2. 期权代码格式是否正确")
    print("3. 交易所后缀是否匹配")
    print("4. 当前是否在交易时间")
    print("5. Wind数据权限是否包含期权数据")

if __name__ == '__main__':
    main()
