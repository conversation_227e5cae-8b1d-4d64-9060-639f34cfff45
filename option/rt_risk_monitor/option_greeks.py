# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
import dataframe_image as dfi
import matplotlib as mpl
from typing import Optional
import os
from greeks_calculator import PyVollibNumbaGreeksCalculator

# 获取当前脚本的目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 实例化计算器
greeks_calculator = PyVollibNumbaGreeksCalculator()

# 定义全局常量，如果greeks_calculator不提供
INTEREST_RATE = 0.0015  # 无风险利率 0.15%
PRICE_PCT = 0.03  # 价格变动百分比常量，用于压力测试

mpl.rcParams['font.family'] = 'Arial Unicode MS'

"""
期权组合分析引擎

这个模块提供了期权组合的分析功能，包括数据处理、希腊字母计算和结果汇总。
核心计算逻辑已经分离到greeks_calculator模块中，提高了代码的模块化和可维护性。

主要功能：
1. 读取期权组合数据
2. 计算期权希腊字母
3. 汇总期权组合风险
4. 生成分析报告
"""


def op_porf(file_path: str = 'porf_oprt.xlsx') -> pd.DataFrame:
    """处理期权组合数据
    
    Args:
        file_path: Excel文件路径
        
    Returns:
        包含计算结果的DataFrame
    """
    try:

        # 构建文件的完整路径
        full_file_path = os.path.join(current_dir, file_path)

        # 读取Excel数据
        op_porfs = pd.read_excel(full_file_path).dropna(axis=1, how='all')

        # 直接处理整个数据集

        chunk_df = op_porfs  # 将整个DataFrame视为一个chunk

        # 确保包含所有必需的列
        required_cols = ['symbol', 'underlying_code', 'position', 'op_price', 'k', 't', 'sign', 'mult', 'S']
        for col in required_cols:
            if col not in chunk_df.columns:  # 检查列是否存在
                raise ValueError(f"输入文件缺少必需的列: {col}")

        # 提取数据
        symbols = chunk_df['symbol'].tolist()
        underlyings = chunk_df['underlying_code'].tolist()
        positions = chunk_df['position'].values.astype(np.float64)
        option_prices = chunk_df['op_price'].values.astype(np.float64)
        K_values = chunk_df['k'].values.astype(np.float64)
        t_values_days = chunk_df['t'].values.astype(np.float64)
        t_values_years = t_values_days / 365.0  # 将天数转换为年
        r_values = np.full(len(chunk_df), INTEREST_RATE, dtype=np.float64)
        flags = chunk_df['sign'].astype(str).str.lower().map({'c': 1, 'p': -1}).values.astype(np.int32)
        mults = chunk_df['mult'].values.astype(np.float64)
        S_values = chunk_df['S'].values.astype(np.float64)  # 标的物价格

        # 1. 计算隐含波动率 (IV)
        # 调用计算器实例的方法 calculate_implied_volatility_batch(S_arr, K_arr, T_arr, r_arr, market_price_arr, option_type_arr)
        iv_values = greeks_calculator.calculate_implied_volatility_batch(S_values, K_values, t_values_years, r_values,
                                                                         option_prices, flags)

        # 2. 计算标准希腊字母 (Delta, Gamma, Vega, Theta, Rho)
        # 调用计算器实例的方法 calculate_greeks_batch(S_arr, K_arr, T_arr, r_arr, sigma_arr, option_type_arr)
        greeks_results = greeks_calculator.calculate_greeks_batch(S_values, K_values, t_values_years, r_values,
                                                                  iv_values,
                                                                  flags)

        # 提取标准希腊字母
        deltas = np.array(greeks_results['delta'])
        gammas = np.array(greeks_results['gamma'])
        vegas = np.array(greeks_results['vega'])
        thetas = np.array(greeks_results['theta'])
        rhos = np.array(greeks_results['rho'])

        # 3. 计算现金希腊字母 ($delta, $gamma, $vega, $theta, $rho) 
        # 调用计算器实例的方法 calculate_cash_greeks_batch
        cash_greeks_results = greeks_calculator.calculate_cash_greeks_batch(
            S_values, K_values, t_values_years, r_values, iv_values, flags, positions, mults
        )

        # 提取现金希腊字母
        cash_deltas_wan = np.array(cash_greeks_results['cash_delta'])
        cash_gammas_wan = np.array(cash_greeks_results['cash_gamma']) / 100.0
        cash_vegas_wan = np.array(cash_greeks_results['cash_vega'])
        cash_thetas_wan = np.array(cash_greeks_results['cash_theta'])
        cash_rhos_wan = np.array(cash_greeks_results['cash_rho'])

        # 4. 计算单一合约权利金总额并转换为万元
        premium_totals_wan = option_prices * mults * np.abs(positions) / 10000.0

        # 5. 计算压力测试盈亏 (Delta + Gamma 近似)
        # Gamma 效应
        gamma_effect = 0.5 * cash_gammas_wan * 100 * PRICE_PCT ** 2
        # 压力测试盈亏
        stPnl_up = cash_deltas_wan * PRICE_PCT + gamma_effect
        stPnl_down = -cash_deltas_wan * PRICE_PCT + gamma_effect

        # 6. 准备结果DataFrame
        result_df = pd.DataFrame({
            'symbol': symbols,
            'underlying': underlyings,
            'position': positions,
            'oprice': option_prices,
            'k': K_values,
            't': t_values_days,
            't_years': t_values_years,
            'sign': flags,
            'mult': mults,
            'price': S_values,
            'IV': iv_values,
            'delta': deltas,
            'gamma': gammas,
            'vega': vegas,
            'theta': thetas,
            'rho': rhos,
            '$delta': cash_deltas_wan,
            '$gamma': cash_gammas_wan,
            '$vega': cash_vegas_wan,
            '$theta': cash_thetas_wan,
            '$rho': cash_rhos_wan,
            'premium': premium_totals_wan,
            'stPnl_up': stPnl_up,
            'stPnl_down': stPnl_down
        })

        # 导出单一合约计算结果
        single_contracts_path = os.path.join(current_dir, 'single_contracts_detail.xlsx')
        result_df.to_excel(single_contracts_path, index=False)
        return result_df

    except Exception as e:
        print(f"处理期权组合数据时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()


def op_greeks_sum(cashgreeks: pd.DataFrame) -> Optional[pd.DataFrame]:
    """汇总希腊字母并生成报告

    Args:
        cashgreeks: 包含单一合约计算结果的DataFrame

    Returns:
        汇总结果的DataFrame，如果出错则返回None
    """
    try:
        if cashgreeks.empty:
            print("警告：输入数据为空，无法进行汇总")
            return None

        # 确保所有需要的列都存在于数据框中
        required_cols = ['underlying', 'position', '$delta', '$gamma', '$vega', 'premium', 'IV', 'stPnl_up',
                         'stPnl_down']
        missing_cols = [col for col in required_cols if col not in cashgreeks.columns]
        if missing_cols:
            print(f"警告：缺少以下列：{', '.join(missing_cols)}")
            # 对于缺失的列，添加默认值0
            for col in missing_cols:
                cashgreeks[col] = 0

        # 定义汇总函数
        agg_funcs = {
            'position': 'sum',
            '$delta': 'sum',
            '$gamma': 'sum',
            '$vega': 'sum',
            'premium': 'sum',
            'stPnl_up': 'sum',
            'stPnl_down': 'sum',
            # IV需要计算加权平均，这里先不加
            'IV': lambda x: np.average(x, weights=np.abs(cashgreeks.loc[x.index, '$vega'])) if np.sum(
                np.abs(cashgreeks.loc[x.index, '$vega'])) != 0 else np.nan
        }

        # 按标的物名称分组汇总
        # 根据用户需求，按underlying分组
        symbol_summary = cashgreeks.groupby('underlying').agg(agg_funcs)

        # 按$gamma由小到大排序
        symbol_summary = symbol_summary.sort_values(by='$gamma', ascending=True)

        # 计算总计行
        total_row = pd.DataFrame({
            'position': [symbol_summary['position'].sum()],
            '$delta': [symbol_summary['$delta'].sum()],
            '$gamma': [symbol_summary['$gamma'].sum()],
            '$vega': [symbol_summary['$vega'].sum()],
            'premium': [symbol_summary['premium'].sum()],
            'stPnl_up': [symbol_summary['stPnl_up'].sum()],
            'stPnl_down': [symbol_summary['stPnl_down'].sum()],
            # 计算总计的加权平均IV
            'IV': [np.average(cashgreeks['IV'], weights=np.abs(cashgreeks['$vega'])) if np.sum(
                np.abs(cashgreeks['$vega'])) != 0 else np.nan]
        }, index=['Total'])

        # 合并汇总和总计
        final_summary = pd.concat([symbol_summary, total_row])

        # 定义风险限额 (万元)
        INPUT_SCALE = 5000  # 万元
        LIMIT_DELTA_SINGLE = INPUT_SCALE * 0.05
        LIMIT_GAMMA_SINGLE = INPUT_SCALE * 0.10
        LIMIT_VEGA_SINGLE = INPUT_SCALE * 0.001
        LIMIT_DELTA_TOTAL = INPUT_SCALE * 0.25
        LIMIT_GAMMA_TOTAL = INPUT_SCALE * 0.50
        LIMIT_VEGA_TOTAL = INPUT_SCALE * 0.005

        # 定义颜色标记函数
        def color_risk(val, limit_single, limit_total, is_total=False):
            if pd.isna(val):
                return ''
            abs_val = abs(val)
            limit = limit_total if is_total else limit_single

            if abs_val > limit:
                return 'background-color: salmon'  # 红色：超出限额
            elif abs_val >= limit * 0.75:  # 警告：limit * 0.75 <= abs_val <= limit
                return 'background-color: yellow'
            else:
                return ''  # 无颜色

        # 应用颜色标记
        styled_summary = final_summary.style.apply(
            lambda row: [
                color_risk(row['$delta'], LIMIT_DELTA_SINGLE, LIMIT_DELTA_TOTAL, row.name == 'Total'),
                color_risk(row['$gamma'], LIMIT_GAMMA_SINGLE, LIMIT_GAMMA_TOTAL, row.name == 'Total'),
                color_risk(row['$vega'], LIMIT_VEGA_SINGLE, LIMIT_VEGA_TOTAL, row.name == 'Total'),
                '', '', '', '', ''  # 其他列不标记颜色
            ],
            axis=1,
            subset=['$delta', '$gamma', '$vega', 'position', 'premium', 'IV', 'stPnl_up', 'stPnl_down']
            # 指定应用样式的列
        )

        # 格式化输出
        styled_summary = styled_summary.format({
            'position': '{:.0f}',
            '$delta': '{:.2f}',
            '$gamma': '{:.2f}',
            '$vega': '{:.2f}',
            'premium': '{:.2f}',
            'IV': '{:.4f}',
            'stPnl_up': '{:.2f}',
            'stPnl_down': '{:.2f}'
        })

        # 导出图片报告
        # 需要安装 dataframe-image 和 kaleido
        # pip install dataframe-image kaleido
        try:
            summary_image_path = os.path.join(current_dir, 'underlying_summary.jpg')
            dfi.export(styled_summary, filename=summary_image_path, fontsize=18,
                       table_conversion="matplotlib", dpi=300, max_rows=-1)
        except Exception as img_e:
            print(f"生成图片报告时出错: {str(img_e)}. 请确保已安装 dataframe-image 和 kaleido")
            try:
                summary_image_path = os.path.join(current_dir, 'underlying_summary.jpg')
                dfi.export(styled_summary, filename=summary_image_path, fontsize=18,
                           table_conversion="selenium", dpi=300, max_rows=-1)
            except Exception as img_e2:
                print(f"使用selenium生成图片报告时也出错: {str(img_e2)}. 请检查环境配置。")

        # 导出Excel报告 (包含颜色标记)
        # Pandas Styler可以直接导出到Excel，保留样式
        summary_excel_path = os.path.join(current_dir, 'underlying_summary.xlsx')
        styled_summary.to_excel(summary_excel_path, index=True)

        return final_summary

    except Exception as e:
        print(f"汇总希腊字母时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == '__main__':

    print("正在处理实际期权组合数据...")
    cashgreeks = op_porf()
    if not cashgreeks.empty:
        op_greeks_sum(cashgreeks)

    print("\n计算完成")
    # 根据操作系统选择合适的暂停命令
    # if os.name == 'nt':  # Windows
    #    os.system('pause')
    # else:  # macOS/Linux
    #    input("按回车键退出...")
