import numpy as np
from numba import jit, vectorize, float64, int32


# 默认无风险利率
DEFAULT_R_RATE = 0.015  # 示例值，例如2%

# Numba JIT隐含波动率计算的默认参数(如果直接使用)
# 注意: PyVollibNumbaGreeksCalculator.calculate_implied_volatility_batch不使用这些参数
# 根据用户请求，它使用py_vollib_vectorized.vectorized_implied_volatility
DEFAULT_IV_TOL = 1e-5
DEFAULT_IV_MAX_ITER = 100
DEFAULT_IV_INITIAL_GUESS = 0.2
DEFAULT_IV_MIN_SIGMA = 1e-4
DEFAULT_IV_MAX_SIGMA = 5.0

# 重新实现Black-Scholes公式的核心计算函数，使其与Numba兼容

@jit(float64(float64), nopython=True, cache=True)
def norm_cdf(x):
    """标准正态分布累积分布函数"""
    # 使用近似算法计算标准正态分布的CDF
    a1 = 0.254829592
    a2 = -0.284496736
    a3 = 1.421413741
    a4 = -1.453152027
    a5 = 1.061405429
    p = 0.3275911
    
    # 取绝对值处理 和 erfc 参数转换
    sign = 1.0
    # The input parameter is 'x' as per the function signature
    if x < 0.0: 
        sign = -1.0
    
    x_abs = np.abs(x) # Absolute value of the input x
    
    # Abramowitz & Stegun formula 7.1.26 for erfc(z) where z = |x|/sqrt(2)
    # erfc(z) approx. exp(-z^2) * poly_val(t_poly)
    # where t_poly = 1 / (1 + p*z)
    
    z = x_abs / np.sqrt(2.0) # Argument for erfc polynomial and exp term
    
    t_poly = 1.0 / (1.0 + p * z)
    # Polynomial part of erfc approximation for erfc(z)
    poly_val = (((((a5 * t_poly + a4) * t_poly) + a3) * t_poly + a2) * t_poly + a1) * t_poly
    erfc_approx = poly_val * np.exp(-z * z) # This approximates erfc(z)
    
    # Relation: norm_cdf(x) = 0.5 * (1 + sign_of_x * erf(|x|/sqrt(2)))
    # And erf(u) = 1 - erfc(u)
    # So, norm_cdf(x) = 0.5 * (1 + sign_of_x * (1 - erfc(|x|/sqrt(2))))
    # Let y_intermediate = 1 - erfc_approx (where erfc_approx is erfc(|x|/sqrt(2)))
    # return 0.5 * (1.0 + sign_of_x * y_intermediate)
    
    y_intermediate = 1.0 - erfc_approx
    return 0.5 * (1.0 + sign * y_intermediate)

@jit(float64(float64), nopython=True, cache=True)
def norm_pdf(x):
    """标准正态分布概率密度函数"""
    return np.exp(-0.5 * x * x) / np.sqrt(2.0 * np.pi)

@jit(float64(float64, float64, float64, float64, float64), nopython=True, cache=True)
def d1(S, K, T, r, sigma):
    """Black-Scholes公式中的d1参数"""
    if T <= 1e-6 or sigma <= 1e-6:
        return np.inf * np.sign(S - K)  # 避免除以零，并处理到期情况
    return (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))

@jit(float64(float64, float64, float64, float64, float64), nopython=True, cache=True)
def d2(S, K, T, r, sigma):
    """Black-Scholes公式中的d2参数"""
    if T <= 1e-6 or sigma <= 1e-6:
        return np.inf * np.sign(S - K)  # 避免除以零
    return d1(S, K, T, r, sigma) - sigma * np.sqrt(T)

@jit(float64(float64, float64, float64, float64, float64, int32), nopython=True, cache=True)
def black_scholes_price_jit(S, K, T, r, sigma, option_type_val):
    """计算Black-Scholes期权价格"""
    flag = 1 if option_type_val == 1 else -1  # 1表示看涨期权，-1表示看跌期权
    q = 0.0  # 股息率，假设为0
    
    if T <= 1e-6:  # 处理到期期权
        if flag == 1:  # 看涨期权
            return max(0.0, S - K)
        else:  # 看跌期权
            return max(0.0, K - S)
    
    d1_val = d1(S, K, T, r, sigma)
    d2_val = d2(S, K, T, r, sigma)
    
    if flag == 1:  # 看涨期权
        return S * np.exp(-q * T) * norm_cdf(d1_val) - K * np.exp(-r * T) * norm_cdf(d2_val)
    else:  # 看跌期权
        return K * np.exp(-r * T) * norm_cdf(-d2_val) - S * np.exp(-q * T) * norm_cdf(-d1_val)

@jit(float64(float64, float64, float64, float64, float64, int32), nopython=True, cache=True)
def delta_jit(S, K, T, r, sigma, option_type_val):
    """计算期权Delta"""
    flag = 1 if option_type_val == 1 else -1  # 1表示看涨期权，-1表示看跌期权
    q = 0.0  # 股息率，假设为0
    
    if T <= 1e-6:  # 处理到期期权
        if flag == 1:  # 看涨期权
            return 1.0 if S > K else (0.5 if S == K else 0.0)
        else:  # 看跌期权
            return -1.0 if S < K else (-0.5 if S == K else 0.0)
    
    d1_val = d1(S, K, T, r, sigma)
    
    if flag == 1:  # 看涨期权
        return np.exp(-q * T) * norm_cdf(d1_val)
    else:  # 看跌期权
        return np.exp(-q * T) * (norm_cdf(d1_val) - 1.0)

@jit(float64(float64, float64, float64, float64, float64), nopython=True, cache=True)
def gamma_jit(S, K, T, r, sigma):
    """计算期权Gamma"""
    q = 0.0  # 股息率，假设为0
    
    if T <= 1e-6 or sigma <= 1e-6:
        return 0.0
    
    d1_val = d1(S, K, T, r, sigma)
    return np.exp(-q * T) * norm_pdf(d1_val) / (S * sigma * np.sqrt(T))

@jit(float64(float64, float64, float64, float64, float64), nopython=True, cache=True)
def vega_jit(S, K, T, r, sigma):
    """计算期权Vega（每1%波动率变化）"""
    q = 0.0  # 股息率，假设为0
    
    if T <= 1e-6:
        return 0.0
    
    d1_val = d1(S, K, T, r, sigma)
    # 返回每1%波动率变化的vega
    return S * np.exp(-q * T) * norm_pdf(d1_val) * np.sqrt(T) / 100.0

@jit(float64(float64, float64, float64, float64, float64, int32), nopython=True, cache=True)
def theta_jit(S, K, T, r, sigma, option_type_val):
    """计算期权Theta（每天）"""
    flag = 1 if option_type_val == 1 else -1  # 1表示看涨期权，-1表示看跌期权
    q = 0.0  # 股息率，假设为0
    
    if T <= 1e-6:
        return 0.0
    
    d1_val = d1(S, K, T, r, sigma)
    d2_val = d2(S, K, T, r, sigma)
    
    term1 = -S * np.exp(-q * T) * norm_pdf(d1_val) * sigma / (2 * np.sqrt(T))
    
    if flag == 1:  # 看涨期权
        term2 = -r * K * np.exp(-r * T) * norm_cdf(d2_val)
        term3 = q * S * np.exp(-q * T) * norm_cdf(d1_val)
    else:  # 看跌期权
        term2 = r * K * np.exp(-r * T) * norm_cdf(-d2_val)
        term3 = -q * S * np.exp(-q * T) * norm_cdf(-d1_val)
    
    # 返回每天的theta（原始theta除以365）
    return (term1 + term2 + term3) / 365.0

@jit(float64(float64, float64, float64, float64, float64, int32), nopython=True, cache=True)
def rho_jit(S, K, T, r, sigma, option_type_val):
    """计算期权Rho（每1%利率变化）"""
    flag = 1 if option_type_val == 1 else -1  # 1表示看涨期权，-1表示看跌期权
    q = 0.0  # 股息率，假设为0
    
    if T <= 1e-6:
        return 0.0
    
    d2_val = d2(S, K, T, r, sigma)
    
    if flag == 1:  # 看涨期权
        return K * T * np.exp(-r * T) * norm_cdf(d2_val) / 100.0
    else:  # 看跌期权
        return -K * T * np.exp(-r * T) * norm_cdf(-d2_val) / 100.0

# 使用我们Numba兼容实现的向量化函数
@vectorize([float64(float64, float64, float64, float64, float64, int32)], nopython=True, cache=True)
def price_vec(S, K, T, r, sigma, option_type_val):
    # 直接使用我们的black_scholes_price_jit函数
    return black_scholes_price_jit(S, K, T, r, sigma, option_type_val)

@vectorize([float64(float64, float64, float64, float64, float64, int32)], nopython=True, cache=True)
def delta_vec(S, K, T, r, sigma, option_type_val):
    # 直接使用我们的delta_jit函数
    return delta_jit(S, K, T, r, sigma, option_type_val)

@vectorize([float64(float64, float64, float64, float64, float64)], nopython=True, cache=True)
def gamma_vec(S, K, T, r, sigma):
    # 直接使用我们的gamma_jit函数
    return gamma_jit(S, K, T, r, sigma)

@vectorize([float64(float64, float64, float64, float64, float64)], nopython=True, cache=True)
def vega_vec(S, K, T, r, sigma):
    # 直接使用我们的vega_jit函数
    return vega_jit(S, K, T, r, sigma)

@vectorize([float64(float64, float64, float64, float64, float64, int32)], nopython=True, cache=True)
def theta_vec(S, K, T, r, sigma, option_type_val):
    # 直接使用我们的theta_jit函数
    return theta_jit(S, K, T, r, sigma, option_type_val)

@vectorize([float64(float64, float64, float64, float64, float64, int32)], nopython=True, cache=True)
def rho_vec(S, K, T, r, sigma, option_type_val):
    # 直接使用我们的rho_jit函数
    return rho_jit(S, K, T, r, sigma, option_type_val)

@jit(float64(float64, float64, float64, float64, float64, int32, float64, int32, float64), nopython=True, cache=True)
def implied_volatility_jit(S, K, T, r, market_price, option_type_val, tol=DEFAULT_IV_TOL, max_iter=DEFAULT_IV_MAX_ITER, initial_guess=DEFAULT_IV_INITIAL_GUESS):
    min_sigma_val = DEFAULT_IV_MIN_SIGMA
    max_sigma_val = DEFAULT_IV_MAX_SIGMA

    if T <= 1e-6:
        # 当 T <= 1e-6 时，black_scholes_price_jit 返回内在价值。
        # 此处传递给它的特定sigma（min_sigma_val或其他）不会改变此结果。
        price_at_intrinsic = black_scholes_price_jit(S, K, T, r, min_sigma_val, option_type_val)
        if abs(market_price - price_at_intrinsic) < tol:
            return min_sigma_val # 市场价格与内在价值匹配，返回定义的 min_sigma
        # 如果市场价格与即将到期期权的内在价值不匹配，则存在问题。
        # 返回 initial_guess 是一种备选方案。
        return initial_guess

    sigma = initial_guess

    for _ in range(max_iter):
        sigma = max(min_sigma_val, min(sigma, max_sigma_val)) # 确保 sigma 在边界内

        price = black_scholes_price_jit(S, K, T, r, sigma, option_type_val)
        diff = price - market_price

        if abs(diff) < tol:
            return sigma

        # 计算 dPrice/dSigma (未缩放的 Vega)
        d1_val_for_vega = d1(S, K, T, r, sigma) # 此处的 sigma 已经被限制在边界内
        d_price_d_sigma = 0.0
        # 检查 d1 是否为无穷大（例如，如果 S 或 K 为零，或者尽管进行了限制，T/sigma 仍然异常小）
        # 这是一种安全措施；当 S,K > 0 且 T,sigma 远离零时，d1 应该是有限的。
        if np.isinf(d1_val_for_vega):
            d_price_d_sigma = 1e-12 # 有效的零 vega，使用一个微小的占位符
        else:
            d_price_d_sigma = S * norm_pdf(d1_val_for_vega) * np.sqrt(T)

        # Vega 过小，牛顿-拉夫森法不稳定或无效
        if abs(d_price_d_sigma) < 1e-12: # 对 Vega 使用稍小的阈值
            if abs(diff) < tol: # 如果差值已经很小，则接受当前的 sigma
                return sigma
            # 回退到小的固定步长
            if diff > 0: # 价格过高，尝试降低 sigma
                sigma -= 0.005
            else: # 价格过低，尝试提高 sigma
                sigma += 0.005
        else:
            # 牛顿-拉夫森步骤
            sigma_prev = sigma
            sigma = sigma - diff / d_price_d_sigma
            # 基本检查，以防止 sigma 跳跃过大时出现剧烈振荡
            if abs(sigma - sigma_prev) > max_sigma_val / 2 and _ < max_iter / 2 : # 大跳跃的启发式方法
                sigma = sigma_prev + (sigma - sigma_prev) * 0.5 # 将步长减半

    # 达到 max_iter 后，返回尽力而为的有界 sigma。
    return max(min_sigma_val, min(sigma, max_sigma_val))

@vectorize([float64(float64, float64, float64, float64, float64, int32, float64)], nopython=True, cache=True)
def implied_volatility_vec(S, K, T, r, market_price, option_type_val, initial_guess):
    # 使用定义的tol和max_iter常量。initial_guess直接传递。
    return implied_volatility_jit(S, K, T, r, market_price, option_type_val, DEFAULT_IV_TOL, DEFAULT_IV_MAX_ITER, initial_guess)

class PyVollibNumbaGreeksCalculator:
    def __init__(self):
        pass


    def calculate_greeks_batch(self, S_arr, K_arr, T_arr, r_arr, sigma_arr, option_type_arr):
        # 确保所有输入都是具有一致 dtype 的 numpy 数组
        S_arr = np.asarray(S_arr, dtype=np.float64)
        K_arr = np.asarray(K_arr, dtype=np.float64)
        T_arr = np.asarray(T_arr, dtype=np.float64)
        r_arr = np.asarray(r_arr, dtype=np.float64)
        sigma_arr = np.asarray(sigma_arr, dtype=np.float64)
        option_type_arr = np.asarray(option_type_arr, dtype=np.int32)
        
        prices = price_vec(S_arr, K_arr, T_arr, r_arr, sigma_arr, option_type_arr)
        deltas = delta_vec(S_arr, K_arr, T_arr, r_arr, sigma_arr, option_type_arr)
        gammas = gamma_vec(S_arr, K_arr, T_arr, r_arr, sigma_arr)
        vegas = vega_vec(S_arr, K_arr, T_arr, r_arr, sigma_arr)
        thetas = theta_vec(S_arr, K_arr, T_arr, r_arr, sigma_arr, option_type_arr)
        rhos = rho_vec(S_arr, K_arr, T_arr, r_arr, sigma_arr, option_type_arr)

        return {
            "price": prices,
            "delta": deltas,
            "gamma": gammas,
            "vega": vegas,
            "theta": thetas,
            "rho": rhos
        }

    def calculate_implied_volatility_batch(self, S_arr, K_arr, T_arr, r_arr, market_price_arr, option_type_arr, initial_guess=0.2):
        
        # 如果输入是标量，则将其全部转换为 numpy 数组
        S_arr = np.asarray(S_arr, dtype=np.float64)
        K_arr = np.asarray(K_arr, dtype=np.float64)
        T_arr = np.asarray(T_arr, dtype=np.float64)
        r_arr = np.asarray(r_arr, dtype=np.float64)
        market_price_arr = np.asarray(market_price_arr, dtype=np.float64)
        option_type_arr = np.asarray(option_type_arr, dtype=np.int32)
        
        # 将initial_guess转换为与输入数组相同长度的数组
        if isinstance(initial_guess, (float, int)):
            initial_guess_arr = np.full_like(S_arr, float(initial_guess), dtype=np.float64)
        else:
            initial_guess_arr = np.asarray(initial_guess, dtype=np.float64) # 确保是numpy数组且类型正确

        ivs = implied_volatility_vec(
            S_arr, K_arr, T_arr, r_arr, market_price_arr, option_type_arr, initial_guess_arr
        )
        return ivs

    def calculate_cash_greeks_batch(self, S_arr, K_arr, T_arr, r_arr, sigma_arr, option_type_arr, positions_arr, mults_arr):
        """
        计算一批期权的现金希腊值。

        参数:
        S_arr (array-like): 标的资产价格数组。
        K_arr (array-like): 执行价格数组。
        T_arr (array-like): 到期时间数组 (年化)。
        r_arr (array-like): 无风险利率数组。
        sigma_arr (array-like): 波动率数组。
        option_type_arr (array-like): 期权类型数组 (1 表示看涨, -1 表示看跌)。
        positions_arr (array-like): 头寸数组 (正数表示权利方, 负数表示义务方)。
        mults_arr (array-like): 合约乘数数组。

        返回:
        dict: 包含现金希腊值 ("cash_price", "cash_delta", "cash_gamma", "cash_vega", "cash_theta", "cash_rho") 的字典。
        """
        # 首先获取标准希腊值
        greeks = self.calculate_greeks_batch(S_arr, K_arr, T_arr, r_arr, sigma_arr, option_type_arr)

        # 确保输入数组是 numpy 数组并具有正确的 dtype
        S_arr = np.asarray(S_arr, dtype=np.float64) # S_arr is needed for cash delta and cash gamma
        positions_arr = np.asarray(positions_arr, dtype=np.float64)
        option_type_arr = np.asarray(option_type_arr, dtype=np.int32)
        mults_arr = np.asarray(mults_arr, dtype=np.float64)

        # 计算现金希腊值
        cash_greeks = {}
                
        # Cash Delta: Delta * S * Position * Multiplier / 10000
        # Delta的符号已经在delta_jit中考虑了期权类型，这里只需乘以头寸方向
        cash_greeks["cash_delta"] = (greeks["delta"] * S_arr * positions_arr * mults_arr) / 10000.0
        
        # Cash Gamma: Gamma * S^2 * Position * Multiplier / 10000
        # Gamma对于买方和卖方符号相反（买方为正，卖方为负）
        cash_greeks["cash_gamma"] = (greeks["gamma"] * (S_arr**2) * positions_arr * mults_arr) / 10000.0
        
        # Cash Vega: Vega * Position * Multiplier / 10000
        # Vega对于买方和卖方符号相反（买方为正，卖方为负）
        cash_greeks["cash_vega"] = (greeks["vega"] * positions_arr * mults_arr) / 10000.0
        
        # Cash Theta: Theta * Position * Multiplier / 10000
        # Theta与头寸方向相反（买方为负，卖方为正）
        cash_greeks["cash_theta"] = (greeks["theta"] * positions_arr * mults_arr) / 10000.0
        
        # Cash Rho: Rho * Position * Multiplier / 10000
        # Rho的符号已经在rho_jit中考虑了期权类型，这里只需乘以头寸方向
        cash_greeks["cash_rho"] = (greeks["rho"] * positions_arr * mults_arr) / 10000.0
        
        return cash_greeks
    
def main():
    calculator = PyVollibNumbaGreeksCalculator()
    S = 4191
    K = 4300
    T = 29 / 365.0
    r = 0.015
    market_price = 27.5
    option_type = 1
    position = 16 
    contract_mult = 5

    # 计算隐含波动率
    iv = calculator.calculate_implied_volatility_batch(S, K, T, r, market_price, option_type)
    print(f"计算得到的隐含波动率: {iv}")

    # 计算标准希腊值
    greeks = calculator.calculate_greeks_batch(S, K, T, r, iv, option_type)
    print("标准希腊值:")
    for name, value in greeks.items():
        print(f"  {name}: {value}")

    # 计算现金希腊值
    cash_greeks = calculator.calculate_cash_greeks_batch(S, K, T, r, iv, option_type, position, contract_mult)
    print("现金希腊值:")
    for name, value in cash_greeks.items():
        print(f"  {name}: {value}")

    # 测试义务方头寸
    position_short = -9 # 假设持有5手义务方头寸
    cash_greeks_short = calculator.calculate_cash_greeks_batch(S, K, T, r, iv, option_type, position_short, contract_mult)
    print("\n现金希腊值 (义务方):")
    for name, value in cash_greeks_short.items():
        print(f"  {name}: {value}")



if __name__ == "__main__":
    main()