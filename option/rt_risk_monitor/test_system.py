#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期权实时风险监控系统测试脚本
"""

import pandas as pd
import numpy as np
import os
import time
from datetime import datetime
from data import StaticDataProcessor
from greeks_calculator import PyVollibNumbaGreeksCalculator

def test_data_processing():
    """测试数据处理功能"""
    print("=" * 50)
    print("测试1: 数据处理功能")
    print("=" * 50)
    
    # 创建测试数据
    test_data = {
        '代码': ['a2509-C-4200', 'a2509-P-4100', 'y2509-P-8000', 'invalid-code', 'zero-position'],
        '数量': [94, 6, 64, 50, 0],  # 包含0持仓的数据
        '方向': ['空', '空', '空', '多', '多'],
        '交易所': ['DCE', 'DCE', 'DCE', 'DCE', 'DCE']
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_position_data.csv'
    df.to_csv(test_file, index=False, encoding='utf-8')
    
    print(f"原始测试数据 ({len(df)} 行):")
    print(df)
    
    # 测试处理
    processor = StaticDataProcessor()
    try:
        result = processor.process_static_data(test_file)
        print(f"\n处理后数据 ({len(result)} 行):")
        if not result.empty:
            print(result[['symbol', 'underlying_code', 'position', 'k', 'sign']])
            print("✅ 数据处理测试通过")
        else:
            print("❌ 数据处理结果为空")
        
        # 清理测试文件
        os.remove(test_file)
        
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        return False
    
    return True

def test_greeks_calculation():
    """测试希腊字母计算功能"""
    print("\n" + "=" * 50)
    print("测试2: 希腊字母计算功能")
    print("=" * 50)
    
    try:
        calculator = PyVollibNumbaGreeksCalculator()
        
        # 测试数据
        S = np.array([4142.0, 8630.0])
        K = np.array([4200.0, 8000.0])
        T = np.array([0.008, 0.008])
        r = np.array([0.015, 0.015])
        market_prices = np.array([1.0, 0.5])
        option_types = np.array([1, -1])  # Call, Put
        positions = np.array([-94, -64])
        mults = np.array([10, 10])
        
        print("测试数据:")
        print(f"标的价格: {S}")
        print(f"执行价格: {K}")
        print(f"期权价格: {market_prices}")
        
        # 计算隐含波动率
        iv_values = calculator.calculate_implied_volatility_batch(
            S, K, T, r, market_prices, option_types
        )
        print(f"\n隐含波动率: {iv_values}")
        
        # 计算希腊字母
        greeks = calculator.calculate_greeks_batch(S, K, T, r, iv_values, option_types)
        print(f"Delta: {greeks['delta']}")
        print(f"Gamma: {greeks['gamma']}")
        print(f"Vega: {greeks['vega']}")
        
        # 计算现金希腊字母
        cash_greeks = calculator.calculate_cash_greeks_batch(
            S, K, T, r, iv_values, option_types, positions, mults
        )
        print(f"\n现金Delta: {cash_greeks['cash_delta']}")
        print(f"现金Gamma: {cash_greeks['cash_gamma']}")
        print(f"现金Vega: {cash_greeks['cash_vega']}")
        
        print("✅ 希腊字母计算测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 希腊字母计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_daily_report_generation():
    """测试每日报告生成功能"""
    print("\n" + "=" * 50)
    print("测试3: 每日报告生成功能")
    print("=" * 50)
    
    try:
        current_time = datetime.now()
        print(f"当前时间: {current_time.strftime('%H:%M:%S')}")
        
        # 创建模拟的希腊字母数据
        test_data = pd.DataFrame({
            'symbol': ['a2509-C-4200', 'a2509-P-4100', 'y2509-P-8000'],
            'underlying': ['A2509', 'A2509', 'Y2509'],
            'position': [-94, -6, -64],
            '$delta': [5.83, -0.49, -0.38],
            '$gamma': [3.08, 0.03, -0.004],
            '$vega': [0.043, 0.004, 0.008],
            'premium': [0.094, 0.008, 0.032],
            'stPnl_up': [0.69, 0.029, 0.013],
            'stPnl_down': [0.37, 0.021, 0.013],
            'IV': [0.099, 0.101, 0.334]
        })
        
        today_str = current_time.strftime('%Y%m%d')
        detail_file = f'test_single_contracts_detail_{today_str}.xlsx'
        summary_file = f'test_underlying_summary_{today_str}.xlsx'
        
        # 生成详细报告
        test_data.to_excel(detail_file, index=False)
        print(f"✅ 详细报告已生成: {detail_file}")
        
        # 生成汇总报告
        summary = test_data.groupby('underlying').agg({
            'position': 'sum',
            '$delta': 'sum',
            '$gamma': 'sum',
            '$vega': 'sum',
            'premium': 'sum',
            'stPnl_up': 'sum',
            'stPnl_down': 'sum'
        })
        
        # 计算加权平均IV
        def weighted_iv(group):
            weights = np.abs(group['$vega'])
            if weights.sum() != 0:
                return np.average(group['IV'], weights=weights)
            return np.nan
        
        iv_summary = test_data.groupby('underlying').apply(weighted_iv)
        summary['IV'] = iv_summary
        
        summary.to_excel(summary_file, index=True)
        print(f"✅ 汇总报告已生成: {summary_file}")
        
        print("\n汇总数据预览:")
        print(summary)
        
        # 清理测试文件
        if os.path.exists(detail_file):
            os.remove(detail_file)
        if os.path.exists(summary_file):
            os.remove(summary_file)
        
        print("✅ 每日报告生成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 每日报告生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_serialization():
    """测试JSON序列化功能"""
    print("\n" + "=" * 50)
    print("测试4: JSON序列化功能")
    print("=" * 50)
    
    try:
        import json
        
        # 创建包含numpy数据类型的测试数据
        test_data = {
            'symbol': 'a2509-C-4200',
            'position': np.float64(-94.0),
            'k': np.float64(4200.0),
            'sign': np.int32(1),
            'delta': np.float64(0.062),
            'gamma': np.float64(0.003)
        }
        
        print("原始数据类型:")
        for key, value in test_data.items():
            print(f"  {key}: {type(value)} = {value}")
        
        # 转换为JSON兼容的数据类型
        json_data = {}
        for key, value in test_data.items():
            if isinstance(value, (np.integer, np.int32, np.int64)):
                json_data[key] = int(value)
            elif isinstance(value, (np.floating, np.float32, np.float64)):
                json_data[key] = float(value)
            else:
                json_data[key] = value
        
        # 测试JSON序列化
        json_str = json.dumps(json_data)
        print(f"\nJSON序列化成功: {json_str[:100]}...")
        
        # 测试反序列化
        parsed_data = json.loads(json_str)
        print("✅ JSON序列化测试通过")
        return True
        
    except Exception as e:
        print(f"❌ JSON序列化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("期权实时风险监控系统 - 功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(test_data_processing())
    test_results.append(test_greeks_calculation())
    test_results.append(test_daily_report_generation())
    test_results.append(test_json_serialization())
    
    # 汇总测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    test_names = [
        "数据处理功能",
        "希腊字母计算功能", 
        "每日报告生成功能",
        "JSON序列化功能"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！系统功能正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return 1

if __name__ == '__main__':
    exit(main())