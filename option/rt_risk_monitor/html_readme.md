期权风险监测工具 - 说明文档
1. 项目概述
“期权风险监测”是一个纯前端的、高度交互的Web应用程序，旨在为期权交易员或风险管理人员提供一个直观的界面，用以监控和分析期权持仓的风险暴露。

该工具通过一个两层级的视图（汇总视图和详细视图），动态计算并可视化展示关键的风险指标（Greeks），如 Delta、Gamma 和 Vega。用户可以自定义风险阈值和视觉警报，模拟调整持仓，并以图片形式导出风险报告。

2. 功能特点
动态风险汇总：自动根据底层详细合约数据，动态计算并生成按标的物（underlying）分类的汇总风险报告。

两级下钻视图：提供一个清晰的汇总视图，并支持点击任意标的进入详细合约视图，方便进行深度分析。

高度可定制的设置：

风险限额：用户可以通过弹出的设置窗口，基于“投入规模”和自定义的“风险系数”来设定两级（单一标的 和 总体）风险限额。

颜色配置：警告色、危险色以及贡献度渐变色均可由用户通过颜色选择器自由定义。

实时风险可视化：

阈值警报：当风险指标超过设定的75%阈值时，单元格会以黄色高亮；超过100%时，则以红色高亮，实现分层级风险预警。

贡献度分析：在详细视图中，每个合约的风险指标单元格会根据其对该标的物总风险的贡献度，显示不同深浅的渐变色，贡献越大，颜色越深。

交互式仓位调整：用户可以直接在详细视图中修改任一合约的持仓（position），所有相关的汇总数据和风险指标会立即重新计算并更新。

一键报告导出：在主表视图下，用户可以一键将当前的汇总风险报告保存为PNG图片，方便分享和存档。

3. 代码结构与详解
该工具完全由一个独立的HTML文件构成，内含 HTML、CSS 和 JavaScript，无需任何后端服务即可运行。

3.1 HTML 结构
HTML 结构主要分为以下几个部分：

<header>: 包含应用的主标题、副标题以及打开设置窗口的“设置”按钮。

设置模态窗口 (#settings-modal): 一个默认隐藏的弹出窗口，用于配置风险限额和颜色。内部划分为“风险限额”和“颜色配置”两大功能区。

主内容区 (<main>):

返回按钮 (#back-button-container): 在详细视图中显示，用于返回主汇总视图。

汇总视图 (#summary-view-wrapper): 包含主汇总表 (#summary-table) 和保存图片的按钮。

详细视图 (#detail-view): 包含详细合约表 (#detail-table)，默认隐藏。

加载提示 (#loading): 初始加载时显示的提示信息。

3.2 CSS 样式
Tailwind CSS: 主要的样式框架，用于快速构建现代化和响应式的用户界面。

内联 <style>: 包含一些自定义的辅助样式，如表格容器的最大高度、滚动条样式、输入框美化以及模态窗口的过渡动画等。

3.3 JavaScript 逻辑
JavaScript 是该工具的核心，负责所有的数据处理、计算、渲染和交互。

核心变量与状态
detailCsvData: 字符串常量，硬编码了所有详细合约的原始CSV数据。

detailData: 由 detailCsvData 解析而来的对象数组，是应用所有计算的基础数据源。

perUnitGreeks: 一个 Map 对象，用于存储每个合约单位持仓的风险指标。这是实现持仓调整后风险能被准确重计算的关键。

settings: 一个全局对象，存储了所有用户可配置的参数，包括风险系数、颜色和计算得出的具体阈值。

主要功能函数
初始化 (initialize)

这是应用的入口函数。

它首先初始化设置 (initSettings)，然后解析CSV数据 (parseCSV)，接着计算并存储单位风险指标 (storePerUnitGreeks)，最后绑定所有事件监听器并执行首次渲染。

设置模块 (initSettings, renderSettingsModal, calculateThresholds)

initSettings: 创建并初始化 settings 对象中的默认值。

renderSettingsModal: 动态生成设置窗口内的所有HTML元素，包括输入框和颜色选择器，并绑定默认值。

calculateThresholds: 根据“投入规模”和用户设定的“风险系数”，计算出所有具体的风险限额（SINGLE 和 TOTAL），并更新 settings.thresholds 对象和设置窗口中的显示值。

数据处理与计算 (parseCSV, storePerUnitGreeks, calculateSummaryData)

parseCSV: 将原始CSV字符串转换为结构化的对象数组。

storePerUnitGreeks: 在初始化时调用，遍历所有合约，用其风险值除以持仓数，得到单位风险值并缓存，为后续的持仓调整提供计算依据。

calculateSummaryData: 核心计算函数。它遍历 detailData，按 underlying 进行分组，对各风险指标求和，并使用 Vega 加权平均 的方式计算每个标的汇总的 IV。

视图渲染 (renderTable, renderCell, renderFooter)

renderTable: 负责渲染整个表格（包括表头、表体和表尾）。

renderCell: 渲染单个单元格，这是所有颜色逻辑的集中处理点。它会根据单元格所属的表格（汇总/详细）、行类型（数据行/汇总行）和风险指标类型，精确地应用不同的颜色规则（阈值警报色优先于贡献度渐变色）。

renderFooter: 渲染表格底部的“汇总”行，并根据表格类型（主表/详细表）应用 TOTAL 或 SINGLE 级别的风险阈值颜色。

交互功能 (updatePosition, showDetailView, showSummaryView)

updatePosition: 当用户修改详细视图中的持仓时触发。它会根据缓存的单位风险值 (perUnitGreeks) 和新的持仓数，重新计算该合约的所有风险指标，然后调用 refreshDisplay 更新整个应用界面。

showDetailView / showSummaryView: 控制汇总视图和详细视图之间的切换。

图片导出 (saveSummaryAsImage)

利用 html2canvas 库，获取主汇总表 div 的DOM元素，将其渲染到一个HTML <canvas> 元素上。

然后将 canvas 转换为 PNG 图片的 Data URL，并创建一个临时的 <a> 标签来触发浏览器下载。

4. 如何使用
将代码保存为一个 .html 文件（例如 risk_monitor.html）。

使用任何现代的网页浏览器（如 Chrome, Firefox, Edge）打开该文件即可。

所有功能均可在本地离线运行。