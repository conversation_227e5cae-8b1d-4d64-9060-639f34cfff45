# 期权实时风险监控系统

## 项目概述

这是一个专业的期权实时风险监控系统，整合了数据处理、希腊字母计算和Web可视化功能。系统将静态数据和实时数据分开处理，实现高效的实时期权风险监控，为期权交易员和风险管理人员提供及时准确的风险信息。

## 核心特性

### 🚀 实时监控
- **实时数据流**：通过Wind API获取实时期权价格和标的价格
- **秒级更新**：系统每秒更新一次希腊字母计算结果
- **自动刷新**：Web界面自动刷新显示最新风险数据

### 📊 高性能计算
- **向量化计算**：使用numba加速希腊字母计算，性能提升5-10倍
- **并行处理**：多线程处理实时数据和计算任务
- **内存优化**：高效的数据结构和缓存机制

### 🎯 智能风险管理
- **分层风险阈值**：支持单一标的和总体风险限额设置
- **颜色预警**：超过75%阈值黄色警告，超过100%红色危险
- **贡献度分析**：详细视图显示每个合约的风险贡献度

### 🌐 现代化界面
- **响应式设计**：支持桌面和移动设备访问
- **交互式操作**：点击汇总行查看详细合约信息
- **实时更新**：页面标题显示最后更新时间

## 系统架构

### 数据分离设计
```
静态数据处理 (程序启动时)     实时数据处理 (持续更新)
├── 期权合约信息              ├── 标的价格 (S)
├── 执行价格 (K)              ├── 期权价格 (op_price)
├── 到期时间 (t)              └── 实时计算希腊字母
├── 合约乘数 (mult)
└── 持仓信息 (position)
```

### 文件结构
```
option/rt_risk_monitor/
├── main.py                 # 主程序入口
├── data.py                 # 数据处理模块（静态+实时）
├── greeks_calculator.py    # 希腊字母计算引擎（禁止修改）
├── app.py                  # Flask Web应用
├── option_greeks.py        # 期权分析引擎
├── config.py               # 配置文件
├── requirements.txt        # 依赖包列表
├── templates/
│   └── index.html         # Web界面模板
├── position_20250812.csv  # 示例持仓数据
└── README.md              # 说明文档
```

## 快速开始

### 1. 环境准备
```bash
# 安装Python依赖
pip install -r requirements.txt

# 确保已安装Wind终端和WindPy
# 注意：WindPy需要在安装了Wind终端的环境中使用
```

### 2. 数据准备
将期权持仓数据保存为CSV文件，包含以下列：
- 代码：期权合约代码
- 数量：持仓数量
- 方向：多/空
- 交易所：交易所代码

### 3. 启动系统
```bash
# 方法1：运行主程序
python main.py

# 方法2：直接运行Web应用
python app.py

# 方法3：测试数据处理
python data.py
```

### 4. 访问界面
打开浏览器访问：http://localhost:5000

## 使用指南

### 主界面功能
1. **汇总视图**：显示按标的分组的风险汇总
2. **详细视图**：点击任意标的查看具体合约明细
3. **设置面板**：自定义风险限额和颜色配置
4. **图片导出**：一键保存风险报告为PNG图片

### 风险指标说明
- **$delta**：价格敏感性（万元/1%价格变动）
- **$gamma**：Delta敏感性（万元/1%价格变动²）
- **$vega**：波动率敏感性（万元/1%波动率变动）
- **premium**：权利金总额（万元）
- **stPnl_up/down**：压力测试盈亏（3%价格变动）

### 颜色编码
- **绿色**：风险在安全范围内
- **黄色**：风险接近阈值（75%-100%）
- **红色**：风险超过阈值（>100%）
- **渐变色**：在详细视图中表示风险贡献度

## 配置说明

### 风险限额配置
```python
RISK_LIMITS = {
    'INPUT_SCALE': 5000,  # 投入规模（万元）
    'RATIOS': {
        'delta_single': 0.05,   # 单一标的Delta限额比例
        'gamma_single': 0.10,   # 单一标的Gamma限额比例
        'vega_single': 0.001,   # 单一标的Vega限额比例
        'delta_total': 0.25,    # 总体Delta限额比例
        'gamma_total': 0.50,    # 总体Gamma限额比例
        'vega_total': 0.005,    # 总体Vega限额比例
    }
}
```

### 实时更新配置
```python
REALTIME_UPDATE_INTERVAL = 1  # 实时数据更新间隔（秒）
WEB_PORT = 5000              # Web服务器端口
INTEREST_RATE = 0.015        # 无风险利率
```

## API接口

### 获取实时数据
```
GET /api/data
返回：期权希腊字母实时数据（JSON格式）
```

### 获取系统状态
```
GET /api/status
返回：系统初始化状态和数据统计
```

## 技术特点

### 高性能计算
- 使用numba JIT编译加速数值计算
- 向量化操作处理批量期权数据
- 多线程并行处理实时数据流

### 数据处理优化
- 静态数据一次性处理，避免重复计算
- 实时数据增量更新，降低系统负载
- 智能缓存机制，提高响应速度

### 错误处理
- 完善的异常处理机制
- 数据验证和边界检查
- 优雅的系统关闭和资源清理

## 注意事项

1. **Wind API依赖**：系统需要Wind终端环境支持
2. **数据格式**：确保持仓数据格式正确
3. **网络连接**：实时数据需要稳定的网络连接
4. **系统资源**：建议在配置较高的机器上运行

## 故障排除

### 常见问题
1. **Wind API连接失败**：检查Wind终端是否正常运行
2. **数据更新停止**：检查网络连接和API订阅状态
3. **计算结果异常**：验证输入数据的完整性和格式

### 日志查看
系统会在控制台输出详细的运行日志，包括：
- 系统初始化状态
- 数据更新频率
- 错误信息和警告

## 扩展开发

系统采用模块化设计，便于扩展：
- 添加新的风险指标计算
- 集成其他数据源
- 自定义Web界面功能
- 增加报告导出格式

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和Wind API使用协议。