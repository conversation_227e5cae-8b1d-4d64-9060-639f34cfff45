@echo off
chcp 65001 >nul
title Option Risk Monitor

:: Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not found. Install Python and add to PATH.
    pause
    exit /b
)

:: Switch to script directory
cd /d "%~dp0" 2>nul || (
    echo [ERROR] Can't switch to script directory
    echo Current directory: %cd%
    pause
    exit /b
)

:menu
cls
echo ========================================
echo  Option Real-time Risk Monitoring System
echo ========================================
echo.
echo 1. Normal Mode (requires Wind API)
echo 2. Demo Mode (uses simulated data)
echo 3. Exit
echo.

:choice
set /p "choice=Enter choice (1-3): "
if "%choice%"=="" goto choice

if "%choice%"=="1" (
    echo Starting Normal Mode...
    python main.py
    if errorlevel 1 echo [ERROR] Failed to run main.py
    pause
    goto menu
) else if "%choice%"=="2" (
    echo Starting Demo Mode...
    python demo_mode.py
    if errorlevel 1 echo [ERROR] Failed to run demo_mode.py
    pause
    goto menu
) else if "%choice%"=="3" (
    exit
) else (
    echo Invalid selection. Please try again.
    timeout /t 2 >nul
    goto menu
)