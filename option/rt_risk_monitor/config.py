# -*- coding: utf-8 -*-
"""
期权实时风险监控系统配置文件
"""

# 数据文件配置
DATA_FILE = 'position_20250812.csv'  # 持仓数据文件
OUTPUT_DIR = 'out'  # 输出文件目录

# Wind API配置
WIND_TIMEOUT = 30  # Wind API超时时间（秒）
REALTIME_UPDATE_INTERVAL = 2  # 实时数据更新间隔（秒）- 优化为2秒，与前端同步
WIND_WAIT_TIME = 3  # 等待Wind数据到达的时间（秒）

# 希腊字母计算配置
INTEREST_RATE = 0.015  # 无风险利率
PRICE_PCT = 0.03  # 压力测试价格变动百分比

# 系统配置
SYSTEM_REINIT_WAIT_TIME = 2  # 系统重新初始化等待时间（秒）
BROWSER_DELAY = 3  # 浏览器打开延迟时间（秒）

# Web服务器配置
WEB_HOST = '0.0.0.0'
WEB_PORT = 5002  # 主程序端口
WEB_PORT_DEMO = 5001  # 演示模式端口
WEB_DEBUG = False
WEB_THREADED = True

# API配置
API_TIMEOUT = 30  # API请求超时时间（秒）
DATA_UPDATE_INTERVAL = 2000  # 前端数据更新间隔（毫秒）

# 性能优化配置
PERFORMANCE_MONITORING = True  # 是否启用性能监控
PERFORMANCE_REPORT_INTERVAL = 100  # 性能报告间隔（更新次数）
MAX_CALCULATION_TIME = 1.0  # 最大允许计算时间（秒），超过则警告

# 风险限额配置（万元）
RISK_LIMITS = {
    'INPUT_SCALE': 5000,  # 投入规模
    'RATIOS': {
        'delta_single': 0.05,   # 单一标的Delta限额比例
        'gamma_single': 0.10,   # 单一标的Gamma限额比例
        'vega_single': 0.001,   # 单一标的Vega限额比例
        'delta_total': 0.25,    # 总体Delta限额比例
        'gamma_total': 0.50,    # 总体Gamma限额比例
        'vega_total': 0.005,    # 总体Vega限额比例
    }
}

# 颜色配置
COLORS = {
    'warning': '#FFEB3B',   # 警告颜色（75%阈值）
    'danger': '#F44336',    # 危险颜色（100%阈值）
    'gradient': '#FFA500',  # 贡献度渐变色
}

# 日志配置
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'