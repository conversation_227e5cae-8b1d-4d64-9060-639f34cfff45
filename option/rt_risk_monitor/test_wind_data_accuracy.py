#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wind数据准确性测试脚本

验证修改后的系统：
1. 只使用Wind API获取的准确到期天数
2. 不进行任何推算
3. 数据质量检查
4. 计算结果验证
"""

import requests
import json
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_wind_data_accuracy():
    """测试Wind数据准确性"""
    print("=" * 60)
    print("Wind数据准确性测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 获取系统状态
        response = requests.get("http://localhost:5002/api/status", timeout=10)
        if response.status_code != 200:
            print(f"无法获取系统状态: HTTP {response.status_code}")
            return False
        
        status = response.json()
        print(f"\n系统状态:")
        print(f"  初始化状态: {status['initialized']}")
        print(f"  数据条数: {status['data_count']}")
        print(f"  当前文件: {status['current_file']}")
        
        # 获取实际数据
        response = requests.get("http://localhost:5002/api/data", timeout=10)
        if response.status_code != 200:
            print(f"无法获取数据: HTTP {response.status_code}")
            return False
        
        data = response.json()
        print(f"\n数据获取结果:")
        print(f"  总合约数: {len(data)}")
        
        if len(data) == 0:
            print("  错误: 没有获取到任何数据")
            return False
        
        # 分析数据质量
        print(f"\n数据质量分析:")
        
        # 检查到期时间
        valid_maturity = sum(1 for item in data if item.get('t_years', 0) > 0)
        invalid_maturity = len(data) - valid_maturity
        print(f"  有效到期时间: {valid_maturity}/{len(data)}")
        print(f"  无效到期时间: {invalid_maturity}")
        
        # 检查期权价格
        valid_oprice = sum(1 for item in data if item.get('oprice', 0) > 0)
        invalid_oprice = len(data) - valid_oprice
        print(f"  有效期权价格: {valid_oprice}/{len(data)}")
        print(f"  无效期权价格: {invalid_oprice}")
        
        # 检查标的价格
        valid_price = sum(1 for item in data if item.get('price', 0) > 0)
        invalid_price = len(data) - valid_price
        print(f"  有效标的价格: {valid_price}/{len(data)}")
        print(f"  无效标的价格: {invalid_price}")
        
        # 检查希腊字母计算结果
        valid_delta = sum(1 for item in data if item.get('delta') is not None and item.get('delta') != 0)
        valid_gamma = sum(1 for item in data if item.get('gamma') is not None and item.get('gamma') != 0)
        valid_vega = sum(1 for item in data if item.get('vega') is not None and item.get('vega') != 0)
        valid_theta = sum(1 for item in data if item.get('theta') is not None and item.get('theta') != 0)
        
        print(f"\n希腊字母计算结果:")
        print(f"  有效Delta: {valid_delta}/{len(data)}")
        print(f"  有效Gamma: {valid_gamma}/{len(data)}")
        print(f"  有效Vega: {valid_vega}/{len(data)}")
        print(f"  有效Theta: {valid_theta}/{len(data)}")
        
        # 显示样例数据
        print(f"\n样例数据 (前3个合约):")
        for i, item in enumerate(data[:3]):
            print(f"  合约 {i+1}: {item['symbol']}")
            print(f"    到期时间: {item.get('t_years', 'N/A'):.6f} 年")
            print(f"    期权价格: {item.get('oprice', 'N/A')}")
            print(f"    标的价格: {item.get('price', 'N/A')}")
            print(f"    Delta: {item.get('delta', 'N/A'):.6f}")
            print(f"    Gamma: {item.get('gamma', 'N/A'):.6f}")
            print(f"    Vega: {item.get('vega', 'N/A'):.6f}")
            print(f"    Theta: {item.get('theta', 'N/A'):.6f}")
            print()
        
        # 评估数据质量
        print(f"数据质量评估:")
        
        # 计算总体质量分数
        maturity_score = valid_maturity / len(data) * 100
        price_score = min(valid_oprice, valid_price) / len(data) * 100
        greeks_score = min(valid_delta, valid_gamma, valid_vega, valid_theta) / len(data) * 100
        
        print(f"  到期时间质量: {maturity_score:.1f}%")
        print(f"  价格数据质量: {price_score:.1f}%")
        print(f"  希腊字母质量: {greeks_score:.1f}%")
        
        overall_score = (maturity_score + price_score + greeks_score) / 3
        print(f"  总体质量分数: {overall_score:.1f}%")
        
        # 判断测试结果
        if overall_score >= 95:
            print(f"\n✅ 测试通过: 数据质量优秀 ({overall_score:.1f}%)")
            return True
        elif overall_score >= 80:
            print(f"\n⚠️  测试警告: 数据质量良好但有改进空间 ({overall_score:.1f}%)")
            return True
        else:
            print(f"\n❌ 测试失败: 数据质量不足 ({overall_score:.1f}%)")
            return False
            
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        return False

def test_no_estimation_logic():
    """验证系统不再使用推算逻辑"""
    print("\n" + "=" * 60)
    print("推算逻辑移除验证")
    print("=" * 60)
    
    # 检查代码中是否还有推算相关的函数
    data_file = "data.py"
    
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有推算相关的代码
        estimation_keywords = [
            "_estimate_maturity_from_symbol",
            "推算到期时间",
            "简单估算",
            "默认30天",
            "expiry_date"
        ]
        
        found_estimation = []
        for keyword in estimation_keywords:
            if keyword in content:
                found_estimation.append(keyword)
        
        if found_estimation:
            print(f"❌ 发现推算相关代码: {found_estimation}")
            return False
        else:
            print("✅ 确认已移除所有推算逻辑")
            return True
            
    except Exception as e:
        print(f"代码检查失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("Wind数据准确性和代码清理验证")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试1: Wind数据准确性
    test1_result = test_wind_data_accuracy()
    
    # 测试2: 推算逻辑移除验证
    test2_result = test_no_estimation_logic()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    tests_passed = sum([test1_result, test2_result])
    total_tests = 2
    
    print(f"通过测试: {tests_passed}/{total_tests}")
    print(f"测试结果:")
    print(f"  Wind数据准确性: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"  推算逻辑移除: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if tests_passed == total_tests:
        print(f"\n🎉 所有测试通过! 系统现在只使用Wind准确数据")
    else:
        print(f"\n⚠️  有 {total_tests - tests_passed} 个测试失败，需要进一步检查")
    
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == '__main__':
    main()
