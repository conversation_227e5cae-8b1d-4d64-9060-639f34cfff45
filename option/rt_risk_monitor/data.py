import pandas as pd
import re
import io
import os
import random
import threading
import time
from datetime import datetime
from WindPy import w
import config

# 全局变量存储实时价格数据
realtime_prices = {}
data_lock = threading.Lock()

# 启动Wind API
w.start()
w.isconnected()

def DemoCallback(indata):
    """Wind API实时数据回调函数"""
    global realtime_prices
    if indata.ErrorCode == 0:
        with data_lock:
            for i, code in enumerate(indata.Codes):
                if len(indata.Data) > 0 and len(indata.Data[0]) > i:
                    realtime_prices[code] = indata.Data[0][i]
    else:
        print(f"Wind API错误: {indata.ErrorCode}")

class StaticDataProcessor:
    """静态数据处理器 - 处理期权基础信息"""
    
    def __init__(self):
        self.static_data = None
        self.processed = False
        self.current_file_path = None
    
    def process_static_data(self, file_path, force_reload=False):
        """处理静态数据，支持重新加载"""
        # 如果是新文件或强制重新加载，则重新处理
        if force_reload or self.current_file_path != file_path or not self.processed:
            print(f"处理数据文件: {file_path}")
            self.current_file_path = file_path
            self.processed = False

        # 改进的文件读取，支持多种编码和错误处理
        df = self._read_csv_with_validation(file_path)
        if df is None or df.empty:
            print(f"错误: 无法读取或文件为空: {file_path}")
            return pd.DataFrame()

        # 数据预处理和验证
        df = self._preprocess_dataframe(df)
        df_options = self._filter_option_data(df)

        if df_options.empty:
            print("警告: 没有找到有效的期权数据")
            return pd.DataFrame()

        processed_data = []
        option_regex = re.compile(r"^(.*?)[-]?([cp])[-]?(\d+\.?\d*)$", re.IGNORECASE)

        for _, row in df_options.iterrows():
            symbol = str(row['symbol']).strip()  # 去除空格
            
            # 检查持仓数量，删除position为0的数据
            position_value = row['position'] * -1 if row['方向'] == '空' else row['position']
            if position_value == 0:
                print(f"跳过持仓为0的合约: {symbol}")
                continue
            
            # 更精确的期权代码解析
            match = option_regex.match(symbol)
            if not match:
                print(f"无法解析期权代码: {symbol}")
                continue

            underlying_code = match.group(1).upper()  # 标准化为大写
            option_type = match.group(2).lower()
            strike_price = match.group(3)
            
            exchange = row['交易所']
            suffix = '.' + exchange[:3].upper()  # 标准化交易所后缀
            
            processed_data.append({
                'symbol': symbol,
                'underlying_code': underlying_code,
                'position': position_value,
                'k': float(strike_price) if strike_price else None,
                'sign': 1 if option_type == 'c' else -1,
                'exchange_suffix': suffix,
                'wind_symbol': symbol + suffix,
                'wind_underlying': underlying_code + suffix
            })

        self.static_data = pd.DataFrame(processed_data)
        
        # 获取静态数据（到期天数和合约乘数）
        self._fetch_static_market_data()
        self.processed = True
        print(f"数据处理完成，共处理 {len(self.static_data)} 个期权合约")
        return self.static_data
    
    def reset(self):
        """重置处理器状态"""
        self.static_data = None
        self.processed = False
        self.current_file_path = None

    def _read_csv_with_validation(self, file_path):
        """改进的CSV读取方法，支持多种编码和数据验证"""
        import os

        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在: {file_path}")
            return None

        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            print(f"错误: 文件为空: {file_path}")
            return None

        print(f"读取文件: {file_path} (大小: {file_size} 字节)")

        # 尝试多种编码读取
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                print(f"成功使用 {encoding} 编码读取文件，共 {len(df)} 行数据")
                return df
            except UnicodeDecodeError:
                print(f"使用 {encoding} 编码读取失败，尝试下一种编码...")
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取时发生错误: {str(e)}")
                continue

        print("错误: 所有编码都无法读取文件")
        return None

    def _preprocess_dataframe(self, df):
        """数据预处理"""
        # 清理列名
        df.columns = df.columns.str.strip()

        # 检查必需的列
        required_columns = ['代码', '数量', '方向', '交易所']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"警告: 缺少必需的列: {missing_columns}")
            print(f"当前列名: {list(df.columns)}")

        # 数据类型转换和清理
        if '数量' in df.columns:
            df['数量'] = pd.to_numeric(df['数量'], errors='coerce').fillna(0)

        return df

    def _filter_option_data(self, df):
        """过滤和处理期权数据"""
        if '代码' not in df.columns:
            print("错误: 数据中没有'代码'列")
            return pd.DataFrame()

        # 过滤期权数据（代码长度大于6）
        df_options = df[df['代码'].str.len() > 6].copy()
        print(f"过滤后的期权数据: {len(df_options)} 条")

        # 重命名列
        df_options.rename(columns={'代码': 'symbol', '数量': 'position'}, inplace=True)

        return df_options
    
    def _fetch_static_market_data(self):
        """获取静态市场数据"""
        if self.static_data.empty:
            return
            
        codes_for_wss = set(self.static_data['wind_symbol'].tolist())
        
        # 获取剩余到期天数和合约乘数
        today_str = datetime.now().strftime("%Y%m%d")
        trade_date_option = f"tradeDate={today_str};"
        wss_codes_str = ",".join(list(codes_for_wss))
        wss_data = w.wss(wss_codes_str, "ptmday,exe_ratio", trade_date_option)
        
        if wss_data.ErrorCode == 0:
            maturity_days = {code: data for code, data in zip(wss_data.Codes, wss_data.Data[0])}
            contract_multipliers = {code: data for code, data in zip(wss_data.Codes, wss_data.Data[1])}
            
            self.static_data['t'] = self.static_data['wind_symbol'].map(
                lambda x: maturity_days.get(x, 0) / 365.0 if maturity_days.get(x) else None
            )
            self.static_data['mult'] = self.static_data['wind_symbol'].map(
                lambda x: contract_multipliers.get(x, 1)
            )
        else:
            print(f"获取静态数据失败: {wss_data.ErrorCode}")
            # 设置默认值
            self.static_data['t'] = 0.1  # 默认到期时间
            self.static_data['mult'] = 10  # 默认合约乘数

class RealTimeDataManager:
    """实时数据管理器"""
    
    def __init__(self, static_processor):
        self.static_processor = static_processor
        self.subscription_active = False
        
    def start_realtime_subscription(self):
        """启动实时数据订阅"""
        if self.subscription_active:
            return
            
        static_data = self.static_processor.static_data
        if static_data is None or static_data.empty:
            print("静态数据未准备好，无法启动实时订阅")
            return
            
        # 收集需要订阅的代码
        codes_for_wsq = set()
        codes_for_wsq.update(static_data['wind_symbol'].tolist())
        codes_for_wsq.update(static_data['wind_underlying'].tolist())
        
        # 启动实时订阅
        codes_str = ",".join(list(codes_for_wsq))
        result = w.wsq(codes_str, "rt_latest", func=DemoCallback)
        
        if result.ErrorCode == 0:
            self.subscription_active = True
            print(f"实时数据订阅成功，订阅了 {len(codes_for_wsq)} 个代码")
        else:
            print(f"实时数据订阅失败: {result.ErrorCode}")
    
    def stop_realtime_subscription(self):
        """停止实时数据订阅"""
        if self.subscription_active:
            w.cancelRequest(0)  # 取消所有订阅
            self.subscription_active = False
            print("实时数据订阅已停止")
    
    def get_current_data(self):
        """获取当前完整数据（静态+实时）"""
        static_data = self.static_processor.static_data.copy()
        
        with data_lock:
            # 添加实时价格数据
            static_data['S'] = static_data['wind_underlying'].map(
                lambda x: realtime_prices.get(x, 0)
            )
            static_data['op_price'] = static_data['wind_symbol'].map(
                lambda x: realtime_prices.get(x, 0)
            )
        
        # 返回最终格式的数据
        final_df = static_data[['symbol', 'underlying_code', 'position', 'k', 'sign', 'S', 'op_price', 't', 'mult']]
        return final_df


# 全局实例
static_processor = StaticDataProcessor()
realtime_manager = RealTimeDataManager(static_processor)

def initialize_system(file_path=None, force_reload=False):
    if file_path is None:
        file_path = config.DATA_FILE
    """初始化系统 - 支持重新初始化"""
    print("正在初始化期权风险监控系统...")
    
    # 如果是重新初始化，先停止现有订阅
    if force_reload:
        realtime_manager.stop_realtime_subscription()
        static_processor.reset()
    
    # 处理静态数据
    static_data = static_processor.process_static_data(file_path, force_reload)
    if static_data.empty:
        print("静态数据处理失败")
        return False
    
    # 启动实时数据订阅
    realtime_manager.start_realtime_subscription()
    
    # 等待一段时间让实时数据到达
    print("等待实时数据...")
    time.sleep(config.WIND_WAIT_TIME)
    
    return True

def reload_system_with_new_file(file_path):
    """使用新文件重新加载系统"""
    print(f"使用新文件重新加载系统: {file_path}")
    return initialize_system(file_path, force_reload=True)

def get_realtime_option_data():
    """获取实时期权数据"""
    return realtime_manager.get_current_data()

def shutdown_system():
    """关闭系统"""
    realtime_manager.stop_realtime_subscription()
    print("系统已关闭")

# --- 主程序 ---
if __name__ == '__main__':
    try:
        # 初始化系统
        if initialize_system():
            print("系统初始化成功")
            
            # 测试获取实时数据
            for i in range(5):
                print(f"\n--- 第 {i+1} 次获取实时数据 ---")
                current_data = get_realtime_option_data()
                print(current_data.head())
                time.sleep(2)
        
        else:
            print("系统初始化失败")
            
    except KeyboardInterrupt:
        print("\n用户中断程序")
    finally:
        shutdown_system()