import pandas as pd
import re
import io
import os
import random
import threading
import time
from datetime import datetime
from WindPy import w
import config

# 全局变量存储实时价格数据
realtime_prices = {}
data_lock = threading.Lock()

# 启动Wind API
w.start()
w.isconnected()

def DemoCallback(indata):
    """Wind API实时数据回调函数"""
    global realtime_prices
    if indata.ErrorCode == 0:
        with data_lock:
            for i, code in enumerate(indata.Codes):
                if len(indata.Data) > 0 and len(indata.Data[0]) > i:
                    realtime_prices[code] = indata.Data[0][i]
    else:
        print(f"Wind API错误: {indata.ErrorCode}")

class StaticDataProcessor:
    """静态数据处理器 - 处理期权基础信息"""
    
    def __init__(self):
        self.static_data = None
        self.processed = False
        self.current_file_path = None
    
    def process_static_data(self, file_path, force_reload=False):
        """处理静态数据，支持重新加载"""
        # 如果是新文件或强制重新加载，则重新处理
        if force_reload or self.current_file_path != file_path or not self.processed:
            print(f"处理数据文件: {file_path}")
            self.current_file_path = file_path
            self.processed = False

        # 改进的文件读取，支持多种编码和错误处理
        df = self._read_csv_with_validation(file_path)
        if df is None or df.empty:
            print(f"错误: 无法读取或文件为空: {file_path}")
            return pd.DataFrame()

        # 数据预处理和验证
        df = self._preprocess_dataframe(df)
        df_options = self._filter_option_data(df)

        if df_options.empty:
            print("警告: 没有找到有效的期权数据")
            return pd.DataFrame()

        processed_data = []
        option_regex = re.compile(r"^(.*?)[-]?([cp])[-]?(\d+\.?\d*)$", re.IGNORECASE)

        for _, row in df_options.iterrows():
            symbol = str(row['symbol']).strip()  # 去除空格
            
            # 检查持仓数量，删除position为0的数据
            position_value = row['position'] * -1 if row['方向'] == '空' else row['position']
            if position_value == 0:
                print(f"跳过持仓为0的合约: {symbol}")
                continue
            
            # 更精确的期权代码解析
            match = option_regex.match(symbol)
            if not match:
                print(f"无法解析期权代码: {symbol}")
                continue

            underlying_code = match.group(1).upper()  # 标准化为大写
            option_type = match.group(2).lower()
            strike_price = match.group(3)

            exchange = row['交易所']
            suffix = '.' + self._get_wind_exchange_suffix(exchange)

            # 构建Wind格式的期权代码
            wind_symbol = self._build_wind_option_code(symbol, exchange)
            wind_underlying = underlying_code + suffix

            processed_data.append({
                'symbol': symbol,
                'underlying_code': underlying_code,
                'position': position_value,
                'k': float(strike_price) if strike_price else None,
                'sign': 1 if option_type == 'c' else -1,
                'exchange_suffix': suffix,
                'wind_symbol': wind_symbol,
                'wind_underlying': wind_underlying
            })

        self.static_data = pd.DataFrame(processed_data)
        
        # 获取静态数据（到期天数和合约乘数）
        self._fetch_static_market_data()
        self.processed = True
        print(f"数据处理完成，共处理 {len(self.static_data)} 个期权合约")
        return self.static_data
    
    def reset(self):
        """重置处理器状态"""
        self.static_data = None
        self.processed = False
        self.current_file_path = None

    def _read_csv_with_validation(self, file_path):
        """改进的CSV读取方法，支持多种编码和数据验证"""
        import os

        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在: {file_path}")
            return None

        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            print(f"错误: 文件为空: {file_path}")
            return None

        print(f"读取文件: {file_path} (大小: {file_size} 字节)")

        # 尝试多种编码读取
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                print(f"成功使用 {encoding} 编码读取文件，共 {len(df)} 行数据")
                return df
            except UnicodeDecodeError:
                print(f"使用 {encoding} 编码读取失败，尝试下一种编码...")
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取时发生错误: {str(e)}")
                continue

        print("错误: 所有编码都无法读取文件")
        return None

    def _preprocess_dataframe(self, df):
        """数据预处理"""
        # 清理列名
        df.columns = df.columns.str.strip()

        # 检查必需的列
        required_columns = ['代码', '数量', '方向', '交易所']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"警告: 缺少必需的列: {missing_columns}")
            print(f"当前列名: {list(df.columns)}")

        # 数据类型转换和清理
        if '数量' in df.columns:
            df['数量'] = pd.to_numeric(df['数量'], errors='coerce').fillna(0)

        return df

    def _filter_option_data(self, df):
        """过滤和处理期权数据"""
        if '代码' not in df.columns:
            print("错误: 数据中没有'代码'列")
            return pd.DataFrame()

        # 过滤期权数据（代码长度大于6）
        df_options = df[df['代码'].str.len() > 6].copy()
        print(f"过滤后的期权数据: {len(df_options)} 条")

        # 重命名列
        df_options.rename(columns={'代码': 'symbol', '数量': 'position'}, inplace=True)

        return df_options

    def _get_wind_exchange_suffix(self, exchange):
        """获取Wind格式的交易所后缀"""
        exchange_mapping = {
            'DCE': 'DCE',
            'SHFE': 'SHF',  # 注意SHFE在Wind中是SHF
            'CZCE': 'CZC',  # 注意CZCE在Wind中是CZC
            'INE': 'INE'
        }
        return exchange_mapping.get(exchange, exchange[:3].upper())

    def _build_wind_option_code(self, symbol, exchange):
        """构建Wind格式的期权代码"""
        suffix = '.' + self._get_wind_exchange_suffix(exchange)

        # 根据交易所调整代码格式
        if exchange == 'DCE':
            # DCE期权代码需要转换为大写，并可能需要调整格式
            # a2509-C-4200 -> A2509-C-4200.DCE
            wind_code = symbol.upper() + suffix
        elif exchange == 'SHFE':
            # SHFE期权代码格式
            # ag2509C9700 -> AG2509C9700.SHF
            wind_code = symbol.upper() + suffix
        elif exchange == 'CZCE':
            # CZCE期权代码格式
            wind_code = symbol.upper() + suffix
        elif exchange == 'INE':
            # INE期权代码格式
            wind_code = symbol.upper() + suffix
        else:
            # 默认格式
            wind_code = symbol.upper() + suffix

        return wind_code


    
    def _fetch_static_market_data(self):
        """获取静态市场数据"""
        if self.static_data.empty:
            return
            
        codes_for_wss = set(self.static_data['wind_symbol'].tolist())
        
        # 获取剩余到期天数和合约乘数
        today_str = datetime.now().strftime("%Y%m%d")
        trade_date_option = f"tradeDate={today_str};"
        wss_codes_str = ",".join(list(codes_for_wss))

        print(f"正在从Wind获取静态数据...")
        print(f"查询日期: {today_str}")
        print(f"查询合约数: {len(codes_for_wss)}")
        print(f"查询字段: ptmday(到期天数), exe_ratio(合约乘数)")

        wss_data = w.wss(wss_codes_str, "ptmday,exe_ratio", trade_date_option)
        
        if wss_data.ErrorCode == 0:
            maturity_days = {code: data for code, data in zip(wss_data.Codes, wss_data.Data[0])}
            contract_multipliers = {code: data for code, data in zip(wss_data.Codes, wss_data.Data[1])}

            print(f"Wind数据获取成功!")
            print(f"返回合约数: {len(wss_data.Codes)}")
            print(f"到期天数数据: {len(maturity_days)} 个合约")
            print(f"合约乘数数据: {len(contract_multipliers)} 个合约")

            # 分析数据质量
            valid_maturity_count = sum(1 for v in maturity_days.values() if v is not None and v > 0)
            invalid_maturity_count = len(maturity_days) - valid_maturity_count

            print(f"有效到期天数: {valid_maturity_count}")
            print(f"无效到期天数: {invalid_maturity_count}")

            if invalid_maturity_count > 0:
                print("无效到期天数的合约:")
                for code, days in maturity_days.items():
                    if days is None or days <= 0:
                        print(f"  {code}: {days}")

            # 处理到期天数，只使用Wind返回的数据
            def get_maturity_years(wind_symbol):
                days = maturity_days.get(wind_symbol)
                if days is None or days <= 0:
                    print(f"警告: {wind_symbol} 无法获取到期天数，将被排除")
                    return None  # 返回None，后续会被过滤掉
                return days / 365.0

            def get_multiplier(wind_symbol):
                mult = contract_multipliers.get(wind_symbol)
                if mult is None:
                    # 根据交易所设置默认乘数
                    if '.DCE' in wind_symbol:
                        return 10
                    elif '.SHF' in wind_symbol:
                        return 15 if 'AG' in wind_symbol else 5
                    elif '.CZC' in wind_symbol:
                        return 20
                    elif '.INE' in wind_symbol:
                        return 1000
                    else:
                        return 10
                return mult

            self.static_data['t'] = self.static_data['wind_symbol'].map(get_maturity_years)
            self.static_data['mult'] = self.static_data['wind_symbol'].map(get_multiplier)

            # 过滤掉无法获取到期天数的合约
            original_count = len(self.static_data)
            self.static_data = self.static_data.dropna(subset=['t'])
            filtered_count = len(self.static_data)

            if original_count > filtered_count:
                removed_count = original_count - filtered_count
                print(f"移除了 {removed_count} 个无法获取到期天数的合约")

            # 统计有效数据
            valid_maturity = (self.static_data['t'] > 0).sum()
            valid_mult = (self.static_data['mult'] > 0).sum()
            print(f"有效到期时间数据: {valid_maturity}/{len(self.static_data)}")
            print(f"有效合约乘数数据: {valid_mult}/{len(self.static_data)}")

            if len(self.static_data) == 0:
                print("警告: 所有合约都无法获取到期天数，请检查Wind数据权限或合约代码格式")

        else:
            print(f"Wind静态数据获取失败: 错误代码 {wss_data.ErrorCode}")
            print("可能的原因:")
            print("  1. Wind数据权限不足")
            print("  2. 期权合约代码格式错误")
            print("  3. Wind服务器连接问题")
            print("  4. 查询的交易日期无效")

            # 清空静态数据，因为无法获取关键信息
            self.static_data = pd.DataFrame()
            print("由于无法获取Wind静态数据，系统将无法进行希腊字母计算")

class RealTimeDataManager:
    """实时数据管理器"""
    
    def __init__(self, static_processor):
        self.static_processor = static_processor
        self.subscription_active = False
        
    def start_realtime_subscription(self):
        """启动实时数据订阅"""
        if self.subscription_active:
            return
            
        static_data = self.static_processor.static_data
        if static_data is None or static_data.empty:
            print("静态数据未准备好，无法启动实时订阅")
            return
            
        # 收集需要订阅的代码
        codes_for_wsq = set()
        codes_for_wsq.update(static_data['wind_symbol'].tolist())
        codes_for_wsq.update(static_data['wind_underlying'].tolist())
        
        # 启动实时订阅
        codes_str = ",".join(list(codes_for_wsq))
        result = w.wsq(codes_str, "rt_latest", func=DemoCallback)
        
        if result.ErrorCode == 0:
            self.subscription_active = True
            print(f"实时数据订阅成功，订阅了 {len(codes_for_wsq)} 个代码")
        else:
            print(f"实时数据订阅失败: {result.ErrorCode}")
    
    def stop_realtime_subscription(self):
        """停止实时数据订阅"""
        if self.subscription_active:
            w.cancelRequest(0)  # 取消所有订阅
            self.subscription_active = False
            print("实时数据订阅已停止")
    
    def get_current_data(self):
        """获取当前完整数据（静态+实时）"""
        static_data = self.static_processor.static_data.copy()
        
        with data_lock:
            # 添加实时价格数据
            static_data['S'] = static_data['wind_underlying'].map(
                lambda x: realtime_prices.get(x, 0)
            )
            static_data['op_price'] = static_data['wind_symbol'].map(
                lambda x: realtime_prices.get(x, 0)
            )
        
        # 返回最终格式的数据
        final_df = static_data[['symbol', 'underlying_code', 'position', 'k', 'sign', 'S', 'op_price', 't', 'mult']]
        return final_df


# 全局实例
static_processor = StaticDataProcessor()
realtime_manager = RealTimeDataManager(static_processor)

def initialize_system(file_path=None, force_reload=False):
    if file_path is None:
        file_path = config.DATA_FILE
    """初始化系统 - 支持重新初始化"""
    print("正在初始化期权风险监控系统...")
    
    # 如果是重新初始化，先停止现有订阅
    if force_reload:
        realtime_manager.stop_realtime_subscription()
        static_processor.reset()
    
    # 处理静态数据
    static_data = static_processor.process_static_data(file_path, force_reload)
    if static_data.empty:
        print("静态数据处理失败")
        return False
    
    # 启动实时数据订阅
    realtime_manager.start_realtime_subscription()
    
    # 等待一段时间让实时数据到达
    print("等待实时数据...")
    time.sleep(config.WIND_WAIT_TIME)
    
    return True

def reload_system_with_new_file(file_path):
    """使用新文件重新加载系统"""
    print(f"使用新文件重新加载系统: {file_path}")
    return initialize_system(file_path, force_reload=True)

def get_realtime_option_data():
    """获取实时期权数据"""
    return realtime_manager.get_current_data()

def shutdown_system():
    """关闭系统"""
    realtime_manager.stop_realtime_subscription()
    print("系统已关闭")

# --- 主程序 ---
if __name__ == '__main__':
    try:
        # 初始化系统
        if initialize_system():
            print("系统初始化成功")
            
            # 测试获取实时数据
            for i in range(5):
                print(f"\n--- 第 {i+1} 次获取实时数据 ---")
                current_data = get_realtime_option_data()
                print(current_data.head())
                time.sleep(2)
        
        else:
            print("系统初始化失败")
            
    except KeyboardInterrupt:
        print("\n用户中断程序")
    finally:
        shutdown_system()