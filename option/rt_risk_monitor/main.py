#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期权实时风险监控系统主程序

这个程序整合了以下组件：
1. data.py - 数据处理模块（静态数据和实时数据分离）
2. greeks_calculator.py - 希腊字母计算引擎（禁止修改）
3. app.py - Flask Web应用
4. option_greeks.py - 期权分析引擎

功能特点：
- 实时监控期权希腊字母
- 静态数据和实时数据分开处理
- Web界面实时显示风险变动
- 高效的时效性处理
"""

import sys
import os
import time
import threading
import signal
from data import initialize_system, get_realtime_option_data, shutdown_system
from app import app, initialize_app, cleanup
import webbrowser
import config

def signal_handler(signum, frame):
    """信号处理函数"""
    print(f"\n接收到信号 {signum}，正在关闭系统...")
    cleanup()
    sys.exit(0)

def open_browser():
    """延迟打开浏览器"""
    time.sleep(config.BROWSER_DELAY)  # 等待服务器启动
    webbrowser.open(f'http://localhost:{config.WEB_PORT}')

def main():
    """主函数"""
    print("=" * 60)
    print("期权实时风险监控系统")
    print("=" * 60)
    print("功能特点：")
    print("1. 实时监控期权希腊字母")
    print("2. 静态数据和实时数据分离处理")
    print("3. Web界面实时显示风险变动")
    print("4. 高效的时效性处理")
    print("=" * 60)
    
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 初始化应用
        print("正在初始化系统...")
        if initialize_app():
            print("系统初始化成功！")
            print(f"\n访问地址: http://localhost:{config.WEB_PORT}")
            print("按 Ctrl+C 退出程序")
            
            # 在后台线程中打开浏览器
            browser_thread = threading.Thread(target=open_browser, daemon=True)
            browser_thread.start()
            
            # 启动Web服务器
            app.run(debug=config.WEB_DEBUG, host=config.WEB_HOST, port=config.WEB_PORT, threaded=config.WEB_THREADED)
            
        else:
            print("系统初始化失败！")
            return 1
            
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        cleanup()
        print("程序已退出")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())