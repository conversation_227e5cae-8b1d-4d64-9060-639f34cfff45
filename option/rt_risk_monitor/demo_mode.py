#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期权实时风险监控系统 - 演示模式

当Wind API不可用时，使用模拟数据进行演示
"""

import pandas as pd
import numpy as np
import threading
import time
import random
from flask import Flask, jsonify, render_template, request
import os
from greeks_calculator import PyVollibNumbaGreeksCalculator
import config

# 初始化 Flask 应用
app = Flask(__name__)

# 初始化希腊字母计算器
greeks_calculator = PyVollibNumbaGreeksCalculator()

# 全局变量
demo_data = []
data_lock = threading.Lock()
system_running = False

# 常量从配置文件读取
INTEREST_RATE = config.INTEREST_RATE
PRICE_PCT = config.PRICE_PCT

# 模拟数据
DEMO_OPTIONS = [
    {'symbol': 'a2509-C-4200', 'underlying_code': 'A2509', 'position': -94, 'k': 4200, 'sign': 1, 't': 0.008, 'mult': 10, 'base_S': 4142, 'base_op': 1.0},
    {'symbol': 'a2509-P-4100', 'underlying_code': 'A2509', 'position': -6, 'k': 4100, 'sign': -1, 't': 0.008, 'mult': 10, 'base_S': 4142, 'base_op': 1.4},
    {'symbol': 'y2509-P-8000', 'underlying_code': 'Y2509', 'position': -64, 'k': 8000, 'sign': -1, 't': 0.008, 'mult': 10, 'base_S': 8630, 'base_op': 0.5},
    {'symbol': 'y2509-P-8200', 'underlying_code': 'Y2509', 'position': -185, 'k': 8200, 'sign': -1, 't': 0.008, 'mult': 10, 'base_S': 8630, 'base_op': 2.0},
    {'symbol': 'v2509-P-4500', 'underlying_code': 'V2509', 'position': -341, 'k': 4500, 'sign': -1, 't': 0.008, 'mult': 5, 'base_S': 4613, 'base_op': 1.0},
]

def generate_demo_data():
    """生成模拟的实时数据"""
    current_data = []
    
    for option in DEMO_OPTIONS:
        # 模拟价格波动（±2%随机波动）
        price_change = random.uniform(-0.02, 0.02)
        S = option['base_S'] * (1 + price_change)
        
        # 期权价格也有一定波动
        op_price_change = random.uniform(-0.1, 0.1)
        op_price = max(0.01, option['base_op'] * (1 + op_price_change))
        
        current_data.append({
            'symbol': option['symbol'],
            'underlying_code': option['underlying_code'],
            'position': option['position'],
            'k': option['k'],
            'sign': option['sign'],
            'S': S,
            'op_price': op_price,
            't': option['t'],
            'mult': option['mult']
        })
    
    return pd.DataFrame(current_data)

def calculate_option_greeks(option_data):
    """计算期权希腊字母"""
    if option_data.empty:
        return []
    
    try:
        # 提取数据
        symbols = option_data['symbol'].tolist()
        underlyings = option_data['underlying_code'].tolist()
        positions = option_data['position'].values.astype(np.float64)
        K_values = option_data['k'].values.astype(np.float64)
        t_values_years = option_data['t'].values.astype(np.float64)
        r_values = np.full(len(option_data), INTEREST_RATE, dtype=np.float64)
        flags = option_data['sign'].values.astype(np.int32)
        mults = option_data['mult'].values.astype(np.float64)
        S_values = option_data['S'].values.astype(np.float64)
        option_prices = option_data['op_price'].values.astype(np.float64)
        
        # 1. 计算隐含波动率
        iv_values = greeks_calculator.calculate_implied_volatility_batch(
            S_values, K_values, t_values_years, r_values, option_prices, flags
        )
        
        # 2. 计算标准希腊字母
        greeks_results = greeks_calculator.calculate_greeks_batch(
            S_values, K_values, t_values_years, r_values, iv_values, flags
        )
        
        # 3. 计算现金希腊字母
        cash_greeks_results = greeks_calculator.calculate_cash_greeks_batch(
            S_values, K_values, t_values_years, r_values, iv_values, 
            flags, positions, mults
        )
        
        # 4. 计算其他指标
        premium_totals_wan = option_prices * mults * np.abs(positions) / 10000.0
        gamma_effect = 0.5 * cash_greeks_results['cash_gamma'] / 100 * PRICE_PCT ** 2
        stPnl_up = cash_greeks_results['cash_delta'] * PRICE_PCT + gamma_effect
        stPnl_down = -cash_greeks_results['cash_delta'] * PRICE_PCT + gamma_effect
        
        # 5. 构建结果
        results = []
        for i in range(len(option_data)):
            results.append({
                'symbol': symbols[i],
                'underlying': underlyings[i],
                'position': float(positions[i]),
                'oprice': float(option_prices[i]),
                'k': float(K_values[i]),
                't': float(t_values_years[i] * 365),
                't_years': float(t_values_years[i]),
                'sign': int(flags[i]),
                'mult': float(mults[i]),
                'price': float(S_values[i]),
                'IV': float(iv_values[i]),
                'delta': float(greeks_results['delta'][i]),
                'gamma': float(greeks_results['gamma'][i]),
                'vega': float(greeks_results['vega'][i]),
                'theta': float(greeks_results['theta'][i]),
                'rho': float(greeks_results['rho'][i]),
                '$delta': float(cash_greeks_results['cash_delta'][i]),
                '$gamma': float(cash_greeks_results['cash_gamma'][i] / 100.0),
                '$vega': float(cash_greeks_results['cash_vega'][i]),
                '$theta': float(cash_greeks_results['cash_theta'][i]),
                '$rho': float(cash_greeks_results['cash_rho'][i]),
                'premium': float(premium_totals_wan[i]),
                'stPnl_up': float(stPnl_up[i]),
                'stPnl_down': float(stPnl_down[i])
            })
        
        return results
        
    except Exception as e:
        print(f"计算希腊字母时出错: {str(e)}")
        return []

def update_demo_data():
    """持续更新演示数据"""
    global demo_data, system_running
    
    while system_running:
        try:
            # 生成新的模拟数据
            option_data = generate_demo_data()
            
            # 计算希腊字母
            greeks_data = calculate_option_greeks(option_data)
            
            if greeks_data:
                with data_lock:
                    demo_data = greeks_data
                print(f"[演示模式] 更新了 {len(demo_data)} 个期权的数据")
            
        except Exception as e:
            print(f"更新演示数据时出错: {str(e)}")
        
        time.sleep(config.REALTIME_UPDATE_INTERVAL * 2)  # 每2秒更新一次

@app.route('/')
def index():
    """提供主页面"""
    return render_template('index.html')

@app.route('/api/data')
def get_data():
    """提供演示数据的API端点"""
    global demo_data
    
    with data_lock:
        return jsonify(demo_data)

@app.route('/api/status')
def get_status():
    """获取系统状态"""
    return jsonify({
        'initialized': system_running,
        'data_count': len(demo_data),
        'last_update': time.time(),
        'mode': 'demo'
    })

@app.route('/api/generate_report', methods=['POST'])
def generate_report():
    """生成Excel报告API（演示模式）"""
    try:
        # 获取前端发送的调整后数据
        adjusted_data = request.json.get('data', None)
        
        # 确保out目录存在
        out_dir = config.OUTPUT_DIR
        if not os.path.exists(out_dir):
            os.makedirs(out_dir)
        
        current_time = time.time()
        today_str = time.strftime("%Y%m%d", time.localtime(current_time))
        
        detail_file = os.path.join(out_dir, f'demo_single_contracts_detail_{today_str}.xlsx')
        summary_file = os.path.join(out_dir, f'demo_underlying_summary_{today_str}.xlsx')
        
        if adjusted_data:
            # 使用调整后的数据
            df = pd.DataFrame(adjusted_data)
            df.to_excel(detail_file, index=False)
            
            # 生成汇总数据
            summary = df.groupby('underlying').agg({
                'position': 'sum',
                '$delta': 'sum',
                '$gamma': 'sum',
                '$vega': 'sum',
                'premium': 'sum',
                'stPnl_up': 'sum',
                'stPnl_down': 'sum'
            })
            
            # 计算加权平均IV
            def weighted_iv(group):
                weights = np.abs(group['$vega'])
                if weights.sum() != 0:
                    return np.average(group['IV'], weights=weights)
                return np.nan
            
            iv_summary = df.groupby('underlying').apply(weighted_iv)
            summary['IV'] = iv_summary
            
            summary.to_excel(summary_file, index=True)
            
            return jsonify({'success': True, 'message': '演示模式报告生成成功'})
        else:
            return jsonify({'success': False, 'message': '没有数据可生成报告'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'生成报告时出错: {str(e)}'})

@app.route('/api/upload_csv', methods=['POST'])
def upload_csv():
    """上传CSV文件API（演示模式）"""
    return jsonify({'success': False, 'message': '演示模式不支持文件上传，请使用正常模式'})

def start_demo_system():
    """启动演示系统"""
    global system_running
    
    print("=" * 60)
    print("期权实时风险监控系统 - 演示模式")
    print("=" * 60)
    print("注意：当前运行在演示模式下，使用模拟数据")
    print("如需使用真实数据，请确保Wind API可用并运行main.py")
    print("=" * 60)
    
    system_running = True
    
    # 启动数据更新线程
    update_thread = threading.Thread(target=update_demo_data, daemon=True)
    update_thread.start()
    
    print("演示系统启动成功！")
    print(f"访问地址: http://localhost:{config.WEB_PORT_DEMO}")
    print("按 Ctrl+C 退出程序")
    
    return True

def stop_demo_system():
    """停止演示系统"""
    global system_running
    system_running = False
    print("演示系统已停止")

if __name__ == '__main__':
    try:
        if start_demo_system():
            # 启动Web服务器
            app.run(debug=config.WEB_DEBUG, host=config.WEB_HOST, port=config.WEB_PORT_DEMO, threaded=config.WEB_THREADED)
    except KeyboardInterrupt:
        print("\n用户中断程序")
    finally:
        stop_demo_system()
        print("程序已退出")