<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>期权风险监测</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .table-container {
            max-height: 75vh;
            overflow: auto;
        }
        .position-input, .ratio-input {
            width: 80px;
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            text-align: right;
            transition: border-color 0.2s;
        }
        .position-input:focus, .ratio-input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
        }
        .modal-overlay {
            transition: opacity 0.3s ease;
        }
        .modal-content {
            transition: transform 0.3s ease;
        }
        .color-input {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 40px;
            height: 40px;
            background-color: transparent;
            border: none;
            cursor: pointer;
        }
        .color-input::-webkit-color-swatch {
            border-radius: 50%;
            border: 2px solid #e5e7eb;
        }
        .color-input::-moz-color-swatch {
            border-radius: 50%;
            border: 2px solid #e5e7eb;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">

    <div class="p-4 sm:p-6 md:p-8">
        <header class="mb-6 text-center relative">
            <!-- 左上角文件导入区域 -->
            <div class="absolute top-0 left-0 flex items-center space-x-2">
                <input type="file" id="csv-file-input" accept=".csv" class="hidden">
                <button id="upload-csv-btn" class="bg-blue-600 text-white font-semibold px-3 py-2 text-sm rounded-lg shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 transition">
                    📁 导入CSV
                </button>
                <button id="generate-report-btn" class="bg-green-600 text-white font-semibold px-3 py-2 text-sm rounded-lg shadow-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-75 transition">
                    📊 生成报告
                </button>
            </div>
            
            <h1 id="main-title" class="text-3xl md:text-4xl font-bold text-gray-900">期权风险监测</h1>
            <p class="text-gray-600 mt-2">点击汇总行查看详细合约。</p>
            
            <!-- 右上角设置按钮 -->
            <button id="open-settings-btn" class="absolute top-0 right-0 bg-white text-indigo-600 font-semibold px-4 py-2 rounded-lg shadow-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-75 transition">
                设置
            </button>
        </header>

        <!-- 设置模态窗口 -->
        <div id="settings-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 modal-overlay opacity-0 pointer-events-none">
            <div class="bg-white rounded-xl shadow-2xl p-6 md:p-8 w-full max-w-3xl modal-content transform scale-95">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">设置</h2>
                    <button id="close-settings-btn" class="text-gray-400 hover:text-gray-600 text-3xl leading-none">&times;</button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- 风险限额设置 -->
                    <div class="space-y-6">
                        <h3 class="text-xl font-semibold text-gray-800 border-b pb-2">风险限额</h3>
                        <div class="p-4 border rounded-lg">
                            <label for="input-scale" class="block text-lg font-semibold text-gray-700 mb-2">投入规模 (万元)</label>
                            <input type="number" id="input-scale" value="5000" class="w-full p-2 border border-gray-300 rounded-md">
                        </div>
                        <div class="space-y-4">
                            <div id="single-limits" class="p-4 bg-gray-50 rounded-lg"></div>
                            <div id="total-limits" class="p-4 bg-gray-50 rounded-lg"></div>
                        </div>
                    </div>
                    <!-- 颜色配置 -->
                    <div class="space-y-6">
                        <h3 class="text-xl font-semibold text-gray-800 border-b pb-2">颜色配置</h3>
                        <div id="color-settings" class="p-4 border rounded-lg space-y-4"></div>
                    </div>
                </div>
            </div>
        </div>

        <main>
            <div id="back-button-container" class="mb-4 hidden">
                <button id="back-button" class="bg-indigo-600 text-white font-semibold px-4 py-2 rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-75 transition">&larr; 返回汇总</button>
            </div>
            <div id="summary-view-wrapper">
                <div id="summary-view" class="bg-white rounded-xl shadow-lg overflow-hidden"><div class="table-container"><table id="summary-table" class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50 sticky top-0 z-10"><tr id="summary-header"></tr></thead><tbody id="summary-body" class="bg-white divide-y divide-gray-200"></tbody></table></div></div>
                <div id="save-button-container" class="mt-4 flex justify-end">
                    <button id="save-summary-btn" class="bg-green-600 text-white font-semibold px-4 py-2 text-sm rounded-lg shadow-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-75 transition">
                        保存为主表图片
                    </button>
                </div>
            </div>
            <div id="detail-view" class="hidden bg-white rounded-xl shadow-lg overflow-hidden"><div class="table-container"><table id="detail-table" class="min-w-full divide-y divide-gray-200"><thead class="bg-gray-50 sticky top-0 z-10"><tr id="detail-header"></tr></thead><tbody id="detail-body" class="bg-white divide-y divide-gray-200"></tbody></table></div></div>
            <div id="loading" class="text-center p-8 text-gray-500"><p>数据加载中...</p></div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            let detailData = [];
            let currentDetailUnderlying = null;
            const perUnitGreeks = new Map();
            const riskCols = ['$delta', '$gamma', '$vega', 'premium', 'stPnl_up', 'stPnl_down'];
            let settings = {};

            const ui = {
                summaryTable: document.getElementById('summary-table'),
                detailTable: document.getElementById('detail-table'),
                summaryTableBody: document.getElementById('summary-body'),
                detailTableBody: document.getElementById('detail-body'),
                summaryHeader: document.getElementById('summary-header'),
                detailHeader: document.getElementById('detail-header'),
                summaryViewWrapper: document.getElementById('summary-view-wrapper'),
                detailView: document.getElementById('detail-view'),
                backButtonContainer: document.getElementById('back-button-container'),
                backButton: document.getElementById('back-button'),
                mainTitle: document.getElementById('main-title'),
                loading: document.getElementById('loading'),
                modal: document.getElementById('settings-modal'),
                openSettingsBtn: document.getElementById('open-settings-btn'),
                closeSettingsBtn: document.getElementById('close-settings-btn'),
                inputScale: document.getElementById('input-scale'),
                singleLimitsContainer: document.getElementById('single-limits'),
                totalLimitsContainer: document.getElementById('total-limits'),
                colorSettingsContainer: document.getElementById('color-settings'),
                saveSummaryBtn: document.getElementById('save-summary-btn'),
            };

            function initialize() {
                initSettings();
                addEventListeners();
                startDataFetching();
            }
            
            function startDataFetching() {
                // 首次加载数据
                fetchData();
                
                // 定期更新数据（间隔从后端配置）
                setInterval(fetchData, 2000);  // 2秒间隔
            }
            
            async function fetchData() {
                try {
                    const response = await fetch('/api/data');
                    const data = await response.json();
                    
                    if (data && data.length > 0) {
                        detailData = data;
                        storePerUnitGreeks();
                        ui.loading.style.display = 'none';
                        refreshDisplay();
                        
                        // 更新页面标题显示最后更新时间
                        const now = new Date();
                        const timeStr = now.toLocaleTimeString();
                        document.title = `期权风险监测 - ${timeStr}`;
                    } else {
                        console.log('暂无数据');
                    }
                } catch (error) {
                    console.error('获取数据失败:', error);
                    ui.loading.innerHTML = '<p class="text-red-500">数据获取失败，请检查服务器连接</p>';
                }
            }

            function initSettings() {
                settings = {
                    ratios: {
                        delta_single: 0.05, gamma_single: 0.10, vega_single: 0.001,
                        delta_total: 0.25, gamma_total: 0.50, vega_total: 0.005,
                    },
                    colors: {
                        warning: '#FFEB3B', // Yellow
                        danger: '#F44336',   // Red
                        gradient: '#FFA500', // Orange
                    },
                    thresholds: {}
                };
                renderSettingsModal();
                calculateThresholds();
            }

            function renderSettingsModal() {
                const createLimitHTML = (type) => `
                    <h4 class="font-bold text-gray-700 mb-3 text-center">${type === 'single' ? '单一标的限额' : '总体限额'} ( ${type.toUpperCase()} )</h4>
                    <div class="space-y-3">
                        ${['delta', 'gamma', 'vega'].map(greek => `
                            <div class="grid grid-cols-3 items-center gap-2">
                                <label for="ratio-${greek}-${type}" class="font-medium">$${greek}:</label>
                                <input type="number" id="ratio-${greek}-${type}" data-greek="${greek}" data-type="${type}" value="${settings.ratios[`${greek}_${type}`]}" step="0.001" class="ratio-input col-span-1">
                                <span id="limit-${greek}-${type}" class="font-mono text-right"></span>
                            </div>
                        `).join('')}
                    </div>`;
                
                ui.singleLimitsContainer.innerHTML = createLimitHTML('single');
                ui.totalLimitsContainer.innerHTML = createLimitHTML('total');

                ui.colorSettingsContainer.innerHTML = `
                    <div class="flex justify-between items-center">
                        <label for="color-warning" class="font-medium">警告颜色 (75%)</label>
                        <input type="color" id="color-warning" data-color="warning" value="${settings.colors.warning}" class="color-input">
                    </div>
                    <div class="flex justify-between items-center">
                        <label for="color-danger" class="font-medium">危险颜色 (100%)</label>
                        <input type="color" id="color-danger" data-color="danger" value="${settings.colors.danger}" class="color-input">
                    </div>
                    <div class="flex justify-between items-center">
                        <label for="color-gradient" class="font-medium">贡献度渐变色</label>
                        <input type="color" id="color-gradient" data-color="gradient" value="${settings.colors.gradient}" class="color-input">
                    </div>`;
            }

            function addEventListeners() {
                ui.backButton.addEventListener('click', showSummaryView);
                ui.openSettingsBtn.addEventListener('click', openModal);
                ui.closeSettingsBtn.addEventListener('click', closeModal);
                ui.modal.addEventListener('click', (e) => { if (e.target === ui.modal) closeModal(); });
                ui.inputScale.addEventListener('input', () => {
                    calculateThresholds();
                    refreshDisplay();
                });
                ui.modal.addEventListener('input', (e) => {
                    if (e.target.matches('.ratio-input')) {
                        const { greek, type } = e.target.dataset;
                        settings.ratios[`${greek}_${type}`] = parseFloat(e.target.value) || 0;
                        calculateThresholds();
                        refreshDisplay();
                    }
                    if (e.target.matches('.color-input')) {
                        settings.colors[e.target.dataset.color] = e.target.value;
                        refreshDisplay();
                    }
                });
                ui.saveSummaryBtn.addEventListener('click', saveSummaryAsImage);
                
                // 文件上传功能
                document.getElementById('upload-csv-btn').addEventListener('click', () => {
                    document.getElementById('csv-file-input').click();
                });
                
                document.getElementById('csv-file-input').addEventListener('change', uploadCSVFile);
                
                // 生成报告功能
                document.getElementById('generate-report-btn').addEventListener('click', generateExcelReport);
            }
            
            function saveSummaryAsImage() {
                const summaryEl = document.getElementById('summary-view');
                html2canvas(summaryEl, {
                    backgroundColor: '#f3f4f6', // Match body background
                    scale: 2, // Increase resolution
                    useCORS: true
                }).then(canvas => {
                    const link = document.createElement('a');
                    link.download = 'risk_summary.png';
                    link.href = canvas.toDataURL('image/png');
                    link.click();
                });
            }

            function openModal() {
                ui.modal.classList.remove('opacity-0', 'pointer-events-none');
                ui.modal.querySelector('.modal-content').classList.remove('scale-95');
            }

            function closeModal() {
                ui.modal.classList.add('opacity-0');
                ui.modal.querySelector('.modal-content').classList.add('scale-95');
                setTimeout(() => ui.modal.classList.add('pointer-events-none'), 300);
            }

            function calculateThresholds() {
                const scale = parseFloat(ui.inputScale.value) || 0;
                for (const key in settings.ratios) {
                    settings.thresholds[key] = scale * settings.ratios[key];
                    const [greek, type] = key.split('_');
                    const el = document.getElementById(`limit-${greek}-${type}`);
                    if (el) el.textContent = settings.thresholds[key].toFixed(2);
                }
            }

            // parseCSV函数已不需要，数据直接从API获取

            function storePerUnitGreeks() {
                detailData.forEach(item => {
                    const unitGreeks = {};
                    const position = parseFloat(item.position) || 1;
                    riskCols.forEach(col => unitGreeks[col] = (parseFloat(item[col]) || 0) / position);
                    perUnitGreeks.set(item.symbol, unitGreeks);
                });
            }

            function calculateSummaryData(details) {
                const summaryMap = new Map();
                details.forEach(row => {
                    const underlying = row.underlying;
                    if (!summaryMap.has(underlying)) {
                        summaryMap.set(underlying, { underlying, position: 0, $delta: 0, $gamma: 0, $vega: 0, premium: 0, stPnl_up: 0, stPnl_down: 0, ivProductSum: 0, vegaAbsSum: 0 });
                    }
                    const summaryRow = summaryMap.get(underlying);
                    ['position', ...riskCols].forEach(col => summaryRow[col] += parseFloat(row[col]) || 0);
                    summaryRow.ivProductSum += (parseFloat(row.IV) || 0) * Math.abs(parseFloat(row.$vega) || 0);
                    summaryRow.vegaAbsSum += Math.abs(parseFloat(row.$vega) || 0);
                });
                return Array.from(summaryMap.values()).map(row => {
                    row.IV = row.vegaAbsSum === 0 ? 0 : row.ivProductSum / row.vegaAbsSum;
                    return row;
                });
            }

            function updatePosition(symbol, newPosition) {
                const item = detailData.find(d => d.symbol === symbol);
                const unitGreeks = perUnitGreeks.get(symbol);
                if (item && unitGreeks) {
                    const newPos = parseFloat(newPosition) || 0;
                    item.position = newPos.toString();
                    riskCols.forEach(col => item[col] = (unitGreeks[col] * newPos).toString());
                    refreshDisplay();
                }
            }
            
            function refreshDisplay() {
                const summaryData = calculateSummaryData(detailData);
                renderTable(summaryData, ui.summaryHeader, ui.summaryTableBody, ui.summaryTable, true);
                if (!ui.detailView.classList.contains('hidden') && currentDetailUnderlying) {
                    const filteredDetails = detailData.filter(item => item.underlying === currentDetailUnderlying);
                    renderTable(filteredDetails, ui.detailHeader, ui.detailTableBody, ui.detailTable, false, summaryData);
                }
            }

            function renderTable(data, headerEl, bodyEl, tableEl, isSummary, summaryDataForGradients) {
                const headers = isSummary ? ['underlying', 'position', '$delta', '$gamma', '$vega', 'premium', 'stPnl_up', 'stPnl_down', 'IV'] : ['symbol', 'underlying', 'position', '$delta', '$gamma', '$vega', 'premium', 'stPnl_up', 'stPnl_down', 'IV'];
                headerEl.innerHTML = headers.map(h => `<th class="px-6 py-3 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">${h}</th>`).join('');
                bodyEl.innerHTML = data.map(row => `<tr ${isSummary ? `class="cursor-pointer hover:bg-gray-100"` : ''} onclick="${isSummary ? `window.showDetailView('${row.underlying}')` : ''}">${headers.map(h => renderCell(h, row, isSummary, 'body', summaryDataForGradients)).join('')}</tr>`).join('');
                if (!isSummary) {
                    bodyEl.querySelectorAll('.position-input').forEach(input => input.addEventListener('change', e => updatePosition(e.target.dataset.symbol, e.target.value)));
                }
                renderFooter(data, headers, tableEl, isSummary);
            }

            function getThresholdColor(value, limit) {
                if (limit > 0) {
                    const absValue = Math.abs(value);
                    if (absValue >= limit) return settings.colors.danger;
                    if (absValue >= limit * 0.75) return settings.colors.warning;
                }
                return null;
            }

            function hexToRgb(hex) {
                const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                return result ? { r: parseInt(result[1], 16), g: parseInt(result[2], 16), b: parseInt(result[3], 16) } : null;
            }
            
            function renderCell(header, row, isSummary, rowType, summaryDataForGradients) {
                const value = row[header];
                let cellStyle = '';
                const numValue = parseFloat(value) || 0;

                const riskHeaderMap = { '$delta': 'delta', '$gamma': 'gamma', '$vega': 'vega' };
                if (riskHeaderMap[header]) {
                    const type = riskHeaderMap[header];
                    let bgColor = null;
                    if (isSummary && rowType === 'body') {
                        bgColor = getThresholdColor(numValue, settings.thresholds[`${type}_single`]);
                    } else if (!isSummary && rowType === 'body') {
                        const summaryParent = summaryDataForGradients.find(s => s.underlying === row.underlying);
                        const totalValue = Math.abs(parseFloat(summaryParent?.[header]));
                        if (totalValue > 0) {
                            const contribution = Math.abs(numValue) / totalValue;
                            const rgb = hexToRgb(settings.colors.gradient);
                            if (rgb) bgColor = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${Math.min(contribution, 1)})`;
                        }
                    }
                    if (bgColor) cellStyle = `background-color: ${bgColor};`;
                }

                let content;
                if (header === 'position' && !isSummary) {
                    content = `<input type="number" class="position-input" data-symbol="${row.symbol}" value="${value}">`;
                } else {
                    content = (!isNaN(parseFloat(value)) && String(value).includes('.')) ? numValue.toFixed(4) : value;
                }
                return `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" style="${cellStyle}">${content}</td>`;
            }

            function renderFooter(data, headers, tableEl, isSummary) {
                tableEl.querySelector('tfoot')?.remove();
                const tfoot = document.createElement('tfoot');
                tfoot.className = 'bg-gray-200 font-semibold';
                const summaryRowTr = document.createElement('tr');
                summaryRowTr.className = 'border-t-2 border-gray-300';

                const totals = {};
                ['position', ...riskCols].forEach(col => totals[col] = 0);
                let totalIvProductSum = 0, totalVegaAbsSum = 0;

                data.forEach(row => {
                    Object.keys(totals).forEach(col => totals[col] += parseFloat(row[col]) || 0);
                    totalIvProductSum += (parseFloat(row.IV) || 0) * Math.abs(parseFloat(row.$vega) || 0);
                    totalVegaAbsSum += Math.abs(parseFloat(row.$vega) || 0);
                });
                const totalIV = totalVegaAbsSum === 0 ? 0 : totalIvProductSum / totalVegaAbsSum;

                summaryRowTr.innerHTML = headers.map((header, index) => {
                    let content = '', cellStyle = '';
                    if (index === 0) content = '汇总';
                    else if (header === 'IV') content = totalIV.toFixed(4);
                    else if (totals[header] !== undefined) {
                        const totalValue = totals[header];
                        content = totalValue.toFixed(4);
                        const riskHeaderMap = { '$delta': 'delta', '$gamma': 'gamma', '$vega': 'vega' };
                        if (riskHeaderMap[header]) {
                            const type = riskHeaderMap[header];
                            const limit = isSummary ? settings.thresholds[`${type}_total`] : settings.thresholds[`${type}_single`];
                            const bgColor = getThresholdColor(totalValue, limit);
                            if (bgColor) cellStyle = `style="background-color: ${bgColor};"`;
                        }
                    }
                    return `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" ${cellStyle}>${content}</td>`;
                }).join('');

                tfoot.appendChild(summaryRowTr);
                tableEl.appendChild(tfoot);
            }

            window.showDetailView = (underlyingId) => {
                currentDetailUnderlying = underlyingId;
                ui.summaryViewWrapper.classList.add('hidden');
                ui.detailView.classList.remove('hidden');
                ui.backButtonContainer.classList.remove('hidden');
                ui.mainTitle.textContent = `详细合约: ${underlyingId}`;
                refreshDisplay();
            }

            function showSummaryView() {
                currentDetailUnderlying = null;
                ui.detailView.classList.add('hidden');
                ui.backButtonContainer.classList.add('hidden');
                ui.summaryViewWrapper.classList.remove('hidden');
                ui.mainTitle.textContent = '期权风险监测';
                refreshDisplay();
            }

            async function uploadCSVFile(event) {
                const file = event.target.files[0];
                if (!file) return;
                
                // 验证文件类型
                if (!file.name.toLowerCase().endsWith('.csv')) {
                    alert('请选择CSV文件！');
                    event.target.value = '';
                    return;
                }
                
                const formData = new FormData();
                formData.append('file', file);
                
                try {
                    // 显示上传状态
                    ui.loading.style.display = 'block';
                    ui.loading.innerHTML = '<p class="text-blue-500">正在上传并处理文件，请稍候...</p>';
                    
                    // 禁用上传按钮
                    const uploadBtn = document.getElementById('upload-csv-btn');
                    const originalText = uploadBtn.textContent;
                    uploadBtn.textContent = '上传中...';
                    uploadBtn.disabled = true;
                    
                    const response = await fetch('/api/upload_csv', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        ui.loading.innerHTML = '<p class="text-green-500">文件上传成功！系统正在重新加载数据...</p>';
                        
                        // 清空当前数据
                        detailData = [];
                        
                        // 等待系统重新初始化
                        setTimeout(() => {
                            ui.loading.style.display = 'none';
                            // 重新开始数据获取
                            fetchData();
                        }, 3000);
                        
                        alert('文件上传成功！系统已切换到新的数据文件。');
                    } else {
                        alert('文件上传失败: ' + result.message);
                        ui.loading.style.display = 'none';
                    }
                    
                    // 恢复按钮状态
                    uploadBtn.textContent = originalText;
                    uploadBtn.disabled = false;
                    
                } catch (error) {
                    console.error('上传文件失败:', error);
                    alert('上传文件失败，请检查网络连接');
                    ui.loading.style.display = 'none';
                    
                    // 恢复按钮状态
                    const uploadBtn = document.getElementById('upload-csv-btn');
                    uploadBtn.textContent = '📁 导入CSV';
                    uploadBtn.disabled = false;
                }
                
                // 清空文件输入
                event.target.value = '';
            }
            
            async function generateExcelReport() {
                try {
                    // 显示加载状态
                    const originalText = document.getElementById('generate-report-btn').textContent;
                    document.getElementById('generate-report-btn').textContent = '生成中...';
                    document.getElementById('generate-report-btn').disabled = true;
                    
                    // 发送当前调整后的数据到后端
                    const response = await fetch('/api/generate_report', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            data: detailData  // 发送当前的详细数据（包含用户调整的position）
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        alert('Excel报告生成成功！文件已保存到out文件夹中。');
                    } else {
                        alert('报告生成失败: ' + result.message);
                    }
                } catch (error) {
                    console.error('生成报告失败:', error);
                    alert('生成报告失败，请检查网络连接');
                } finally {
                    // 恢复按钮状态
                    document.getElementById('generate-report-btn').textContent = originalText;
                    document.getElementById('generate-report-btn').disabled = false;
                }
            }

            initialize();
        });
    </script>
</body>
</html>
