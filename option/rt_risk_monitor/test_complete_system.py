#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期权实时风险监控系统 - 完整功能测试
"""

import os
import time
import requests
import threading
from datetime import datetime

def test_file_upload_logic():
    """测试文件上传逻辑"""
    print("=" * 60)
    print("测试1: 文件上传和数据处理逻辑")
    print("=" * 60)
    
    try:
        from data import StaticDataProcessor, reload_system_with_new_file
        
        # 创建测试文件
        test_data = """代码,数量,方向,交易所
test1-C-1000,100,多,DCE
test2-P-2000,50,空,DCE
test3-C-3000,0,多,DCE"""
        
        test_file = 'test_dynamic.csv'
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_data)
        
        processor = StaticDataProcessor()
        
        # 测试处理原始文件
        print("处理原始文件...")
        result1 = processor.process_static_data('position_20250812.csv')
        print(f"原始文件处理结果: {len(result1)} 个合约")
        
        # 测试处理新文件
        print("\\n处理新上传的文件...")
        result2 = processor.process_static_data(test_file, force_reload=True)
        print(f"新文件处理结果: {len(result2)} 个合约")
        
        if not result2.empty:
            print("新文件处理的合约:")
            print(result2[['symbol', 'underlying_code', 'position']])
        
        # 清理测试文件
        os.remove(test_file)
        
        print("✅ 文件上传逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 文件上传逻辑测试失败: {e}")
        return False

def test_excel_generation():
    """测试Excel文件生成"""
    print("\\n" + "=" * 60)
    print("测试2: Excel文件生成功能")
    print("=" * 60)
    
    try:
        from app import generate_excel_reports
        import pandas as pd
        
        # 创建测试数据
        test_data = [
            {
                'symbol': 'test-C-1000',
                'underlying': 'TEST',
                'position': 100,
                '$delta': 5.0,
                '$gamma': 2.0,
                '$vega': 0.5,
                'premium': 1.0,
                'stPnl_up': 0.5,
                'stPnl_down': -0.3,
                'IV': 0.2
            }
        ]
        
        # 测试生成报告
        success = generate_excel_reports(test_data)
        
        if success:
            # 检查文件是否生成
            today_str = datetime.now().strftime("%Y%m%d")
            detail_file = os.path.join('out', f'single_contracts_detail_{today_str}.xlsx')
            summary_file = os.path.join('out', f'underlying_summary_{today_str}.xlsx')
            
            if os.path.exists(detail_file) and os.path.exists(summary_file):
                print(f"✅ Excel文件生成成功:")
                print(f"  - 详细报告: {detail_file}")
                print(f"  - 汇总报告: {summary_file}")
                return True
            else:
                print("❌ Excel文件未找到")
                return False
        else:
            print("❌ Excel文件生成失败")
            return False
            
    except Exception as e:
        print(f"❌ Excel文件生成测试失败: {e}")
        return False

def test_web_api():
    """测试Web API功能"""
    print("\\n" + "=" * 60)
    print("测试3: Web API功能")
    print("=" * 60)
    
    try:
        # 启动演示模式服务器
        import demo_mode
        
        def run_demo():
            demo_mode.start_demo_system()
            demo_mode.app.run(debug=False, host='127.0.0.1', port=5002, threaded=True, use_reloader=False)
        
        server_thread = threading.Thread(target=run_demo, daemon=True)
        server_thread.start()
        
        # 等待服务器启动
        time.sleep(3)
        
        # 测试API端点
        base_url = 'http://127.0.0.1:5002'
        
        # 测试状态API
        response = requests.get(f'{base_url}/api/status', timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ 状态API测试通过: {status}")
        else:
            print(f"❌ 状态API测试失败: {response.status_code}")
            return False
        
        # 测试数据API
        response = requests.get(f'{base_url}/api/data', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 数据API测试通过，返回 {len(data)} 条数据")
        else:
            print(f"❌ 数据API测试失败: {response.status_code}")
            return False
        
        # 测试报告生成API
        if data:
            response = requests.post(f'{base_url}/api/generate_report', 
                                   json={'data': data}, timeout=10)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 报告生成API测试通过: {result}")
            else:
                print(f"❌ 报告生成API测试失败: {response.status_code}")
                return False
        
        demo_mode.stop_demo_system()
        return True
        
    except Exception as e:
        print(f"❌ Web API测试失败: {e}")
        return False

def test_out_directory():
    """测试out目录功能"""
    print("\\n" + "=" * 60)
    print("测试4: out目录文件管理")
    print("=" * 60)
    
    try:
        # 确保out目录存在
        out_dir = 'out'
        if not os.path.exists(out_dir):
            os.makedirs(out_dir)
            print("✅ out目录创建成功")
        else:
            print("✅ out目录已存在")
        
        # 检查目录权限
        test_file = os.path.join(out_dir, 'test_permission.txt')
        with open(test_file, 'w') as f:
            f.write('test')
        
        if os.path.exists(test_file):
            os.remove(test_file)
            print("✅ out目录读写权限正常")
            return True
        else:
            print("❌ out目录写入失败")
            return False
            
    except Exception as e:
        print(f"❌ out目录测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("期权实时风险监控系统 - 完整功能测试")
    print("=" * 80)
    print("测试新增功能:")
    print("1. HTML导入文件后，data.py处理导入的文件")
    print("2. 生成的Excel文件放在out文件夹中")
    print("3. 根据HTML调整的position生成Excel")
    print("4. Web API功能完整性")
    print("=" * 80)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(test_file_upload_logic())
    test_results.append(test_excel_generation())
    test_results.append(test_web_api())
    test_results.append(test_out_directory())
    
    # 汇总测试结果
    print("\\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = sum(test_results)
    total = len(test_results)
    
    test_names = [
        "文件上传和数据处理逻辑",
        "Excel文件生成功能", 
        "Web API功能",
        "out目录文件管理"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！系统功能完整，可以投入使用。")
        print("\\n📋 使用说明:")
        print("1. 运行 python demo_mode.py 启动演示模式")
        print("2. 运行 python app.py 启动正常模式")
        print("3. 在Web界面左上角点击'导入CSV'上传新的持仓文件")
        print("4. 调整position后点击'生成报告'创建Excel文件")
        print("5. 生成的文件保存在out文件夹中")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return 1

if __name__ == '__main__':
    exit(main())