#!/bin/bash

echo "========================================"
echo "期权实时风险监控系统启动脚本"
echo "========================================"
echo
echo "请选择启动模式："
echo "1. 正常模式（需要Wind API）"
echo "2. 演示模式（使用模拟数据）"
echo "3. 退出"
echo
read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo
        echo "启动正常模式..."
        python3 main.py
        ;;
    2)
        echo
        echo "启动演示模式..."
        python3 demo_mode.py
        ;;
    3)
        echo
        echo "退出程序"
        exit 0
        ;;
    *)
        echo
        echo "无效选择，请重新运行脚本"
        ;;
esac

echo
read -p "按回车键退出..."