import pandas as pd
import numpy as np
import threading
import time
import os
from datetime import datetime
from flask import Flask, jsonify, render_template, request
from data import initialize_system, get_realtime_option_data, shutdown_system, reload_system_with_new_file
from greeks_calculator import PyVollibNumbaGreeksCalculator
from option_greeks import op_porf, op_greeks_sum
import atexit
import config

# 初始化 Flask 应用
app = Flask(__name__)

# 初始化希腊字母计算器
greeks_calculator = PyVollibNumbaGreeksCalculator()

# 全局变量存储最新计算结果
latest_greeks_data = []
data_lock = threading.Lock()
system_initialized = False
current_data_file = config.DATA_FILE  # 当前使用的数据文件

# 常量从配置文件读取
INTEREST_RATE = config.INTEREST_RATE
PRICE_PCT = config.PRICE_PCT

def calculate_option_greeks(option_data):
    """计算期权希腊字母"""
    if option_data.empty:
        return pd.DataFrame()
    
    try:
        # 提取数据
        symbols = option_data['symbol'].tolist()
        underlyings = option_data['underlying_code'].tolist()
        positions = option_data['position'].values.astype(np.float64)
        K_values = option_data['k'].values.astype(np.float64)
        t_values_years = option_data['t'].values.astype(np.float64)
        r_values = np.full(len(option_data), INTEREST_RATE, dtype=np.float64)
        flags = option_data['sign'].values.astype(np.int32)
        mults = option_data['mult'].values.astype(np.float64)
        S_values = option_data['S'].values.astype(np.float64)
        option_prices = option_data['op_price'].values.astype(np.float64)
        
        # 过滤无效数据
        valid_mask = (S_values > 0) & (option_prices > 0) & (t_values_years > 0)
        if not valid_mask.any():
            print("没有有效的期权数据")
            return pd.DataFrame()
        
        # 只处理有效数据
        valid_indices = np.where(valid_mask)[0]
        
        # 1. 计算隐含波动率
        iv_values = np.full(len(option_data), 0.2)  # 默认值
        if len(valid_indices) > 0:
            valid_iv = greeks_calculator.calculate_implied_volatility_batch(
                S_values[valid_indices], K_values[valid_indices], 
                t_values_years[valid_indices], r_values[valid_indices],
                option_prices[valid_indices], flags[valid_indices]
            )
            iv_values[valid_indices] = valid_iv
        
        # 2. 计算标准希腊字母
        greeks_results = greeks_calculator.calculate_greeks_batch(
            S_values, K_values, t_values_years, r_values, iv_values, flags
        )
        
        # 3. 计算现金希腊字母
        cash_greeks_results = greeks_calculator.calculate_cash_greeks_batch(
            S_values, K_values, t_values_years, r_values, iv_values, 
            flags, positions, mults
        )
        
        # 4. 计算其他指标
        premium_totals_wan = option_prices * mults * np.abs(positions) / 10000.0
        gamma_effect = 0.5 * cash_greeks_results['cash_gamma'] / 100 * PRICE_PCT ** 2
        stPnl_up = cash_greeks_results['cash_delta'] * PRICE_PCT + gamma_effect
        stPnl_down = -cash_greeks_results['cash_delta'] * PRICE_PCT + gamma_effect
        
        # 5. 构建结果DataFrame，确保数据类型兼容JSON序列化
        result_df = pd.DataFrame({
            'symbol': symbols,
            'underlying': underlyings,
            'position': positions.astype(float),
            'oprice': option_prices.astype(float),
            'k': K_values.astype(float),
            't': (t_values_years * 365).astype(float),  # 转换回天数
            't_years': t_values_years.astype(float),
            'sign': flags.astype(int),
            'mult': mults.astype(float),
            'price': S_values.astype(float),
            'IV': iv_values.astype(float),
            'delta': greeks_results['delta'].astype(float),
            'gamma': greeks_results['gamma'].astype(float),
            'vega': greeks_results['vega'].astype(float),
            'theta': greeks_results['theta'].astype(float),
            'rho': greeks_results['rho'].astype(float),
            '$delta': cash_greeks_results['cash_delta'].astype(float),
            '$gamma': (cash_greeks_results['cash_gamma'] / 100.0).astype(float),  # 转换为万元
            '$vega': cash_greeks_results['cash_vega'].astype(float),
            '$theta': cash_greeks_results['cash_theta'].astype(float),
            '$rho': cash_greeks_results['cash_rho'].astype(float),
            'premium': premium_totals_wan.astype(float),
            'stPnl_up': stPnl_up.astype(float),
            'stPnl_down': stPnl_down.astype(float)
        })

        # 6. 清理无效值，确保JSON序列化兼容性
        # 将NaN、Infinity等无效值替换为0或合理的默认值
        numeric_columns = result_df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            # 替换NaN和无穷大值
            result_df[col] = result_df[col].replace([np.inf, -np.inf], 0)
            result_df[col] = result_df[col].fillna(0)

        return result_df
        
    except Exception as e:
        print(f"计算希腊字母时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def generate_excel_reports(adjusted_data=None):
    """生成Excel报告，使用调整后的position数据"""
    try:
        current_time = datetime.now()
        today_str = current_time.strftime("%Y%m%d")
        
        # 确保out目录存在
        out_dir = config.OUTPUT_DIR
        if not os.path.exists(out_dir):
            os.makedirs(out_dir)
        
        detail_file = os.path.join(out_dir, f'single_contracts_detail_{today_str}.xlsx')
        summary_file = os.path.join(out_dir, f'underlying_summary_{today_str}.xlsx')
        
        print(f"开始生成 {today_str} 的报告...")
        
        # 使用调整后的数据或当前数据
        if adjusted_data is not None:
            greeks_data = pd.DataFrame(adjusted_data)
            print("使用HTML界面调整后的position数据生成报告")
        else:
            # 获取当前数据并计算希腊字母
            option_data = get_realtime_option_data()
            if not option_data.empty:
                greeks_data = calculate_option_greeks(option_data)
            else:
                print("没有可用的期权数据")
                return False
        
        if not greeks_data.empty:
            # 生成详细报告
            greeks_data.to_excel(detail_file, index=False)
            print(f"已生成详细合约报告: {detail_file}")
            
            # 生成汇总报告
            summary_data = calculate_summary_for_report(greeks_data)
            if summary_data is not None:
                summary_data.to_excel(summary_file, index=True)
                print(f"已生成汇总报告: {summary_file}")
                return True
        
        return False
        
    except Exception as e:
        print(f"生成报告时出错: {str(e)}")
        return False

def check_and_generate_daily_reports():
    """检查是否需要生成每日报告（15点后）"""
    current_time = datetime.now()
    current_hour = current_time.hour
    
    # 检查是否是15点后且还没有生成今日报告
    if current_hour >= 15:
        today_str = current_time.strftime("%Y%m%d")
        out_dir = config.OUTPUT_DIR
        detail_file = os.path.join(out_dir, f'single_contracts_detail_{today_str}.xlsx')
        summary_file = os.path.join(out_dir, f'underlying_summary_{today_str}.xlsx')
        
        # 检查文件是否已存在
        if not os.path.exists(detail_file) or not os.path.exists(summary_file):
            generate_excel_reports()

def calculate_summary_for_report(greeks_data):
    """计算汇总数据用于报告"""
    try:
        # 按标的分组汇总
        agg_funcs = {
            'position': 'sum',
            '$delta': 'sum',
            '$gamma': 'sum',
            '$vega': 'sum',
            'premium': 'sum',
            'stPnl_up': 'sum',
            'stPnl_down': 'sum'
        }
        
        summary = greeks_data.groupby('underlying').agg(agg_funcs)
        
        # 计算加权平均IV
        def weighted_iv(group):
            weights = np.abs(group['$vega'])
            if weights.sum() != 0:
                return np.average(group['IV'], weights=weights)
            return np.nan
        
        iv_summary = greeks_data.groupby('underlying').apply(weighted_iv)
        summary['IV'] = iv_summary
        
        # 按$gamma排序
        summary = summary.sort_values(by='$gamma', ascending=True)
        
        # 添加总计行
        total_row = pd.DataFrame({
            'position': [summary['position'].sum()],
            '$delta': [summary['$delta'].sum()],
            '$gamma': [summary['$gamma'].sum()],
            '$vega': [summary['$vega'].sum()],
            'premium': [summary['premium'].sum()],
            'stPnl_up': [summary['stPnl_up'].sum()],
            'stPnl_down': [summary['stPnl_down'].sum()],
            'IV': [np.average(greeks_data['IV'], weights=np.abs(greeks_data['$vega'])) 
                   if np.sum(np.abs(greeks_data['$vega'])) != 0 else np.nan]
        }, index=['Total'])
        
        final_summary = pd.concat([summary, total_row])
        return final_summary
        
    except Exception as e:
        print(f"计算汇总数据时出错: {str(e)}")
        return None

def update_greeks_continuously():
    """持续更新希腊字母计算"""
    global latest_greeks_data
    last_report_check = datetime.now().date()
    update_count = 0
    
    print("开始持续更新希腊字母计算线程")
    
    while system_initialized:
        try:
            update_count += 1
            # 获取实时数据
            option_data = get_realtime_option_data()
            
            if not option_data.empty:
                print(f"第{update_count}次更新: 获取到 {len(option_data)} 条实时数据")
                
                # 计算希腊字母
                greeks_data = calculate_option_greeks(option_data)
                
                if not greeks_data.empty:
                    with data_lock:
                        latest_greeks_data = greeks_data.to_dict('records')
                    print(f"第{update_count}次更新: 成功计算 {len(latest_greeks_data)} 个期权的希腊字母数据")
                    
                    # 检查是否需要生成每日报告（每天只检查一次）
                    current_date = datetime.now().date()
                    if current_date != last_report_check:
                        check_and_generate_daily_reports()
                        last_report_check = current_date
                else:
                    print(f"第{update_count}次更新: 希腊字母计算结果为空")
            else:
                print(f"第{update_count}次更新: 实时数据为空")
                
        except Exception as e:
            print(f"第{update_count}次更新希腊字母时出错: {str(e)}")
            import traceback
            traceback.print_exc()
        
        time.sleep(config.REALTIME_UPDATE_INTERVAL)  # 每秒更新一次

def initialize_app(data_file=None):
    """初始化应用"""
    global system_initialized, current_data_file
    
    if data_file:
        current_data_file = data_file
    
    print("正在初始化期权风险监控系统...")
    
    # 初始化数据系统
    if initialize_system(current_data_file):
        system_initialized = True
        print("数据系统初始化成功")
        
        # 启动后台线程持续更新希腊字母
        update_thread = threading.Thread(target=update_greeks_continuously, daemon=True)
        update_thread.start()
        print("希腊字母更新线程已启动")
        
        return True
    else:
        print("数据系统初始化失败")
        return False

def reinitialize_with_new_file(file_path):
    """使用新文件重新初始化系统"""
    global system_initialized, current_data_file, latest_greeks_data
    
    print(f"使用新文件重新初始化系统: {file_path}")
    
    # 停止当前系统
    system_initialized = False
    time.sleep(config.SYSTEM_REINIT_WAIT_TIME)  # 等待更新线程停止
    
    # 清空当前数据
    with data_lock:
        latest_greeks_data = []
    
    # 使用新文件重新加载
    if reload_system_with_new_file(file_path):
        current_data_file = file_path
        system_initialized = True
        
        # 重新启动更新线程
        update_thread = threading.Thread(target=update_greeks_continuously, daemon=True)
        update_thread.start()
        
        print("系统重新初始化成功")
        return True
    else:
        print("系统重新初始化失败")
        return False

@app.route('/')
def index():
    """提供主页面"""
    return render_template('index.html')

def clean_data_for_json(data):
    """清理数据以确保JSON序列化兼容性"""
    import json
    import math

    def clean_value(value):
        if isinstance(value, (int, float)):
            if math.isnan(value) or math.isinf(value):
                return 0.0
            return float(value)
        return value

    if isinstance(data, list):
        return [clean_data_for_json(item) for item in data]
    elif isinstance(data, dict):
        return {key: clean_data_for_json(value) for key, value in data.items()}
    else:
        return clean_value(data)

@app.route('/api/data')
def get_data():
    """提供实时期权希腊字母数据的API端点"""
    global latest_greeks_data

    try:
        with data_lock:
            if latest_greeks_data:
                print(f"API返回数据: {len(latest_greeks_data)} 条记录")
                # 清理数据确保JSON兼容性
                cleaned_data = clean_data_for_json(latest_greeks_data)
                return jsonify(cleaned_data)
            else:
                print("API返回空数据")
                # 如果没有数据，尝试立即获取一次
                if system_initialized:
                    option_data = get_realtime_option_data()
                    if not option_data.empty:
                        greeks_data = calculate_option_greeks(option_data)
                        if not greeks_data.empty:
                            latest_greeks_data = greeks_data.to_dict('records')
                            print(f"即时计算返回数据: {len(latest_greeks_data)} 条记录")
                            # 清理数据确保JSON兼容性
                            cleaned_data = clean_data_for_json(latest_greeks_data)
                            return jsonify(cleaned_data)

                return jsonify([])
    except Exception as e:
        print(f"API数据获取错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def get_status():
    """获取系统状态"""
    return jsonify({
        'initialized': system_initialized,
        'data_count': len(latest_greeks_data) if latest_greeks_data else 0,
        'last_update': time.time()
    })

@app.route('/api/generate_report', methods=['POST'])
def generate_report():
    """生成Excel报告API"""
    try:
        # 获取前端发送的调整后数据
        adjusted_data = request.json.get('data', None)
        
        # 生成报告
        success = generate_excel_reports(adjusted_data)
        
        if success:
            return jsonify({'success': True, 'message': '报告生成成功'})
        else:
            return jsonify({'success': False, 'message': '报告生成失败'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'生成报告时出错: {str(e)}'})

@app.route('/api/upload_csv', methods=['POST'])
def upload_csv():
    """上传CSV文件API"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有文件被上传'})
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'})
        
        if file and file.filename.endswith('.csv'):
            # 确保out目录存在
            out_dir = config.OUTPUT_DIR
            if not os.path.exists(out_dir):
                os.makedirs(out_dir)
            
            # 保存文件
            filename = f"uploaded_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            filepath = os.path.join(out_dir, filename)
            file.save(filepath)
            
            print(f"文件已保存到: {filepath}")
            
            # 使用新文件重新初始化系统
            if reinitialize_with_new_file(filepath):
                return jsonify({'success': True, 'message': f'文件上传成功，系统已切换到新数据: {filename}'})
            else:
                return jsonify({'success': False, 'message': '文件处理失败，请检查文件格式'})
        else:
            return jsonify({'success': False, 'message': '请上传CSV文件'})
            
    except Exception as e:
        print(f"上传文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'上传文件时出错: {str(e)}'})

def cleanup():
    """清理函数"""
    global system_initialized
    system_initialized = False
    shutdown_system()
    print("应用清理完成")

# 注册清理函数
atexit.register(cleanup)

if __name__ == '__main__':
    try:
        # 初始化应用
        if initialize_app():
            print("应用初始化成功，启动Web服务器...")
            # 启动 Flask 开发服务器
            app.run(debug=config.WEB_DEBUG, host=config.WEB_HOST, port=config.WEB_PORT, threaded=config.WEB_THREADED)
        else:
            print("应用初始化失败")
    except KeyboardInterrupt:
        print("\n用户中断程序")
    finally:
        cleanup()