from datetime import datetime
import glob
import numpy as np
import pandas as pd
import os
import dataframe_image as dfi
import matplotlib as mpl
from typing import Dict, List

# 设置字体
mpl.rcParams['font.family'] = 'Arial Unicode MS'

# 常量定义
MARKET_CONFIG = {
    '交易市场': ['上海证券交易所', '深圳证券交易所', '北京证券交易所'],
    '市场代码': ['1', '2', 'r']
}

ALERT_RULES = {
    '特别严重_股票': {
        'pattern': lambda x: '特别严重' in x and '股票' in x,
        'condition': '单日累计委托金额>60万元或单日累计委托数量>6万股'
    },
    '风险警示股票': {
        'pattern': lambda x: '风险警示股票' in x,
        'condition': '禁止交易'
    },
    '普通股票': {
        'pattern': lambda x: '股票' in x,
        'condition': '单日累计委托金额>150万元或单日累计委托数量>15万股'
    },
    '可转债': {
        'pattern': lambda x: '可转债' in x,
        'condition': '单日累计委托金额>100万元或单日累计委托数量>1万股'
    },
    '债券': {
        'pattern': lambda x: '债券' in x,
        'condition': '风险提示'
    },
    '严重异常波动': {
        'pattern': lambda x: '严重异常波动证券' in x,
        'condition': '单日累计委托金额>1000万元或单日累计委托数量>100万股'
    },
    'ETF': {
        'pattern': lambda x: 'ETF' in x,
        'condition': '单日累计委托金额>150万元或单日累计委托数量>150万股'
    },
    'REITs': {
        'pattern': lambda x: 'REITs' in x,
        'condition': '单日累计委托金额>210万元或单日累计委托数量>105万股'
    }
}

def process_market_data(data: pd.DataFrame) -> pd.DataFrame:
    """处理市场数据，添加市场代码"""
    market_df = pd.DataFrame(MARKET_CONFIG)
    processed_data = pd.merge(data, market_df, on='交易市场')
    return processed_data

def generate_o32_format(data: pd.DataFrame) -> None:
    """生成O32系统所需的文件格式"""
    columns = ['证券名称', '证券代码', '', '市场名称', '备注', '有效开始日期', '有效截止日期']
    selected_data = data.loc[:, ('证券名称', '证券代码', '证券代码', '市场代码', '备注', '起始日期', '截止日期')].copy()
    
    selected_data['备注'] = np.nan
    selected_data.iloc[:, 2] = np.nan
    selected_data.columns = columns
    
    # 处理日期格式
    for date_col in ['有效开始日期', '有效截止日期']:
        selected_data[date_col] = selected_data[date_col].apply(lambda x: str(x).replace('-', ''))
    
    selected_data.to_excel('O32_postfile.xlsx', index=False)

def process_monitoring_securities(file_path: str) -> pd.DataFrame:
    """处理监控证券数据"""
    data = pd.read_excel(file_path, skiprows=2, converters={'证券代码': str})
    # 创建数据副本避免 SettingWithCopyWarning
    monitored_data = data[data['证券组'].str.contains(
        '重点监控证券组|严重异常波动证券组|可转债重点监控证券组|重点债券证券组|重点公募REITs基金证券组')].copy()
    
    alert_groups = monitored_data['证券组'].unique()
    
    for group in alert_groups:
        for rule in ALERT_RULES.values():
            if rule['pattern'](group):
                monitored_data.loc[monitored_data['证券组'] == group, '严禁触发情形（五级预警）'] = rule['condition']
                break
    
    return monitored_data

def export_monitoring_report(data: pd.DataFrame, date: str) -> None:
    """导出监控报告"""
    report_columns = ['证券组', '证券名称', '证券代码', '备注', '起始日期', '截止日期', '严禁触发情形（五级预警）']
    report_data = data.loc[:, report_columns].copy()
    
    # 重置索引并添加序号列
    report_data = report_data.reset_index(drop=True)
    report_data.index = report_data.index + 1
    report_data = report_data.rename_axis('序号')
    
    # 导出文件
    report_data.to_excel(f'重点监控证券名单{date}.xlsx', index=True)
    dfi.export(report_data, '重点监控证券名单.jpg', fontsize=12, table_conversion="matplotlib")

def main():
    try:
        today = datetime.strftime(datetime.today(), '%Y%m%d')
        file_pattern = f'证券组证券设置_{today}*'
        file_list = glob.glob(file_pattern)
        
        if not file_list:
            raise FileNotFoundError(f"未找到{today}的证券组设置文件")
        
        monitored_data = process_monitoring_securities(file_list[0])
        processed_data = process_market_data(monitored_data)
        generate_o32_format(processed_data)
        export_monitoring_report(monitored_data, today)
        
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
    finally:
        os.system('pause')

if __name__ == '__main__':
    main()