import pandas as pd
import os

folder_path = 'D:/OneDrive/工作/Python测试数据/trend_strategy_analyzer/20250401_20250523趋势策略信号开平仓/'
output_file = 'D:/OneDrive/工作/Python测试数据/trend_strategy_analyzer/20250401_20250523趋势策略信号开平仓/merged_trades.csv'

all_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.csv')]

# Read the first file to get the header
df = pd.read_csv(all_files[0])

# Read the rest of the files and append, skipping the header
for f in all_files[1:]:
    df = pd.concat([df, pd.read_csv(f)])

df = df.loc[df['strategy_name'].str.contains('simple'),:]
# Write the merged dataframe to a new CSV file
df.to_csv(output_file, index=False)

print(f"Merged {len(all_files)} files into {output_file}")
