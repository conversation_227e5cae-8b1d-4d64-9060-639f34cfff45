#!/usr/bin/env python3
"""趋势策略交易分析系统主程序

这是系统的主入口文件，整合所有模块并提供完整的分析流程。
"""

import sys
import traceback
from pathlib import Path
from typing import Optional, Dict, Any
import argparse

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.config_manager import ConfigManager
from src.data_adapter import DataAdapter
from src.data_preprocessor import DataPreprocessor
from src.position_matcher import PositionMatcher
from src.performance_calculator import PerformanceCalculator
from src.time_series_analyzer import TimeSeriesAnalyzer
from src.report_generator import ReportGenerator
from src.logger_config import setup_logger, get_logger


class TrendStrategyAnalyzer:
    """趋势策略分析系统主类
    
    整合所有模块，提供完整的分析流程。
    """
    
    def __init__(self, config_path: str = 'config/config.yaml'):
        """初始化分析系统
        
        Args:
            config_path: 配置文件路径
        """
        # 初始化配置管理器
        self.config_manager = ConfigManager(config_path)
        
        # 设置日志
        log_config = self.config_manager.get_logging_config()
        setup_logger(log_config)
        
        self.logger = get_logger(self.__class__.__name__)
        
        # 初始化各个模块
        self._initialize_modules()
        
        self.logger.info("趋势策略分析系统初始化完成")
    
    def _initialize_modules(self):
        """初始化所有模块"""
        try:
            self.data_adapter = DataAdapter(self.config_manager)
            self.data_preprocessor = DataPreprocessor(self.config_manager)
            self.position_matcher = PositionMatcher(self.config_manager)
            self.performance_calculator = PerformanceCalculator(self.config_manager)
            self.time_series_analyzer = TimeSeriesAnalyzer(self.config_manager)
            self.report_generator = ReportGenerator(self.config_manager)
            
            self.logger.info("所有模块初始化成功")
            
        except Exception as e:
            self.logger.error(f"模块初始化失败: {e}")
            raise
    
    def run_analysis(self, input_file: Optional[str] = None, 
                    output_dir: Optional[str] = None,
                    analysis_options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """运行完整的分析流程
        
        Args:
            input_file: 输入文件路径（可选，默认使用配置文件中的路径）
            output_dir: 输出目录路径（可选，默认使用配置文件中的路径）
            analysis_options: 分析选项（可选）
            
        Returns:
            分析结果字典
        """
        self.logger.info("开始执行趋势策略分析")
        
        try:
            # 1. 数据加载
            self.logger.info("步骤 1/6: 数据加载")
            raw_data = self._load_data(input_file)
            
            # 2. 数据预处理
            self.logger.info("步骤 2/6: 数据预处理")
            processed_data = self._preprocess_data(raw_data)
            
            # 3. 头寸匹配与盈亏计算
            self.logger.info("步骤 3/6: 头寸匹配与盈亏计算")
            completed_signals, uncompleted_signals, unmatched_trades = self._match_positions(processed_data)
            
            # 4. 绩效指标计算
            self.logger.info("步骤 4/6: 绩效指标计算")
            performance_metrics = self._calculate_performance(completed_signals, analysis_options)
            
            # 5. 时序分析
            self.logger.info("步骤 5/6: 时序分析")
            time_series_results = self._analyze_time_series(completed_signals, analysis_options)
            
            # 6. 报告生成
            self.logger.info("步骤 6/6: 报告生成")
            report_files = self._generate_reports(
                completed_signals, uncompleted_signals, unmatched_trades,
                performance_metrics, time_series_results, output_dir, processed_data
            )
            
            # 整理分析结果
            analysis_results = {
                'completed_signals_count': len(completed_signals),
                'uncompleted_signals_count': len(uncompleted_signals),
                'performance_metrics': performance_metrics,
                'time_series_results': time_series_results,
                'report_files': report_files,
                'total_pnl': sum(signal.pnl_rate for signal in completed_signals),
                'analysis_summary': self._create_analysis_summary(
                    completed_signals, uncompleted_signals, performance_metrics
                )
            }
            
            self.logger.info("趋势策略分析完成")
            return analysis_results
            
        except Exception as e:
            self.logger.error(f"分析过程中出现错误: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            raise
    
    def _load_data(self, input_file: Optional[str] = None):
        """加载数据
        
        Args:
            input_file: 输入文件路径
            
        Returns:
            原始数据DataFrame
        """
        if input_file:
            # 临时更新配置
            self.config_manager.update_config('data_source.input_file', input_file)
        
        raw_data = self.data_adapter.load_data()
        
        if raw_data.empty:
            raise ValueError("加载的数据为空")
        
        self.logger.info(f"成功加载 {len(raw_data)} 条交易记录")
        return raw_data
    
    def _preprocess_data(self, raw_data):
        """预处理数据
        
        Args:
            raw_data: 原始数据DataFrame
            
        Returns:
            预处理后的数据DataFrame
        """
        processed_data = self.data_preprocessor.preprocess(raw_data)
        
        # 获取预处理摘要
        summary = self.data_preprocessor.get_preprocessing_summary(raw_data, processed_data)
        self.logger.info(f"数据预处理完成: {summary}")
        
        return processed_data
    
    def _match_positions(self, processed_data):
        """匹配头寸
        
        Args:
            processed_data: 预处理后的数据DataFrame
            
        Returns:
            已完成信号列表、未完成信号列表和未匹配交易列表
        """
        completed_signals, uncompleted_signals = self.position_matcher.match_positions(processed_data)
        
        # 获取未匹配交易
        unmatched_trades = self.position_matcher.get_unmatched_trades()
        
        # 获取匹配统计
        stats = self.position_matcher.get_matching_statistics()
        self.logger.info(f"头寸匹配完成: {stats}")
        
        return completed_signals, uncompleted_signals, unmatched_trades
    
    def _calculate_performance(self, completed_signals, analysis_options):
        """计算绩效指标
        
        Args:
            completed_signals: 已完成信号列表
            analysis_options: 分析选项
            
        Returns:
            绩效指标字典
        """
        # 确定分组字段
        group_by_fields = ['strategy_name']  # 默认按策略名称分组
        
        if analysis_options and 'group_by' in analysis_options:
            group_by_fields = analysis_options['group_by']
        
        performance_metrics = self.performance_calculator.calculate_performance_metrics(
            completed_signals, group_by_fields
        )
        
        self.logger.info(f"绩效指标计算完成，共 {len(performance_metrics)} 个分组")
        return performance_metrics
    
    def _analyze_time_series(self, completed_signals, analysis_options):
        """时序分析
        
        Args:
            completed_signals: 已完成信号列表
            analysis_options: 分析选项
            
        Returns:
            时序分析结果字典
        """
        # 确定分析类型
        analysis_types = ['daily', 'weekly', 'monthly']
        
        if analysis_options and 'time_series_types' in analysis_options:
            analysis_types = analysis_options['time_series_types']
        
        time_series_results = self.time_series_analyzer.analyze_time_series(
            completed_signals, analysis_types
        )
        
        self.logger.info(f"时序分析完成，共 {len(time_series_results)} 种分析类型")
        return time_series_results
    
    def _generate_reports(self, completed_signals, uncompleted_signals, unmatched_trades,
                         performance_metrics, time_series_results, output_dir, processed_data):
        """生成报告
        
        Args:
            completed_signals: 已完成信号列表
            uncompleted_signals: 未完成信号列表
            unmatched_trades: 未匹配交易列表
            performance_metrics: 绩效指标字典
            time_series_results: 时序分析结果
            output_dir: 输出目录
            processed_data: 预处理后的数据DataFrame
            
        Returns:
            报告文件路径字典
        """
        # 生成基础报告
        report_files = self.report_generator.generate_comprehensive_report(
            completed_signals, uncompleted_signals, unmatched_trades,
            performance_metrics, time_series_results, output_dir
        )
        
        # 生成 pnl_rate 汇总报告
        try:
            completed_signals_df = self.performance_calculator._signals_to_dataframe(completed_signals)
            pnl_summary = self.performance_calculator.calculate_pnl_rate_summary(completed_signals_df)
            pnl_summary_file = self.report_generator.export_pnl_rate_summary(pnl_summary, Path(output_dir))
            report_files['pnl_rate_summary'] = pnl_summary_file
            self.logger.info("PnL Rate 汇总报告生成完成")
        except Exception as e:
            self.logger.warning(f"PnL Rate 汇总报告生成失败: {e}")
        
        # 生成每日信号占用图表（改进版）
        try:
            occupancy_chart_file = self.report_generator.create_daily_signal_occupancy_chart(
                completed_signals, Path(output_dir)
            )
            if occupancy_chart_file:
                report_files['daily_signal_occupancy_chart'] = occupancy_chart_file
                self.logger.info("每日信号占用图表生成完成")
        except Exception as e:
            self.logger.warning(f"每日信号占用图表生成失败: {e}")
        
        # 生成多维度PnL汇总Excel报告
        try:
            completed_signals_df = self.performance_calculator._signals_to_dataframe(completed_signals)
            multidimensional_pnl_summary = self.performance_calculator.calculate_multidimensional_pnl_summary(completed_signals_df)
            
            if multidimensional_pnl_summary:
                excel_file = self.report_generator.export_multidimensional_pnl_to_excel(
                    multidimensional_pnl_summary, Path(output_dir)
                )
                if excel_file:
                    report_files['multidimensional_pnl_excel'] = excel_file
                    self.logger.info(f"多维度PnL汇总Excel报告生成完成: {excel_file}")
                else:
                    self.logger.warning("多维度PnL汇总Excel报告生成失败：返回空路径")
            else:
                self.logger.warning("多维度PnL汇总数据为空，跳过Excel报告生成")
        except Exception as e:
            self.logger.warning(f"多维度PnL汇总Excel报告生成失败: {e}")
        
        self.logger.info(f"报告生成完成，共生成 {len(report_files)} 个文件")
        return report_files
    
    def _create_analysis_summary(self, completed_signals, uncompleted_signals, performance_metrics):
        """创建分析摘要
        
        Args:
            completed_signals: 已完成信号列表
            uncompleted_signals: 未完成信号列表
            performance_metrics: 绩效指标字典
            
        Returns:
            分析摘要字典
        """
        total_pnl = sum(signal.pnl_rate for signal in completed_signals)
        winning_signals = len([s for s in completed_signals if s.pnl_rate > 0])
        losing_signals = len([s for s in completed_signals if s.pnl_rate < 0])
        
        # 找出最佳和最差表现的策略
        best_strategy = None
        worst_strategy = None
        
        if performance_metrics:
            best_strategy = max(performance_metrics.items(), key=lambda x: x[1].total_pnl_rate)
            worst_strategy = min(performance_metrics.items(), key=lambda x: x[1].total_pnl_rate)
        
        summary = {
            'total_completed_signals': len(completed_signals),
            'total_uncompleted_signals': len(uncompleted_signals),
            'total_pnl': total_pnl,
            'winning_signals': winning_signals,
            'losing_signals': losing_signals,
            'overall_win_rate': winning_signals / len(completed_signals) if completed_signals else 0,
            'best_strategy': best_strategy[0] if best_strategy else None,
            'best_strategy_pnl': best_strategy[1].total_pnl_rate if best_strategy else 0,
            'worst_strategy': worst_strategy[0] if worst_strategy else None,
            'worst_strategy_pnl': worst_strategy[1].total_pnl_rate if worst_strategy else 0
        }
        
        return summary
    
    def run_quick_analysis(self, input_file_or_filters=None, output_dir_or_options=None) -> Dict[str, Any]:
        """运行快速分析（仅基本指标）
        
        Args:
            input_file_or_filters: 输入文件路径(str) 或 过滤条件(dict)
            output_dir_or_options: 输出目录路径(str) 或 选项配置(dict)
            
        Returns:
            快速分析结果字典
        """
        self.logger.info("开始执行快速分析")
        
        # 判断调用方式
        if isinstance(input_file_or_filters, dict):
            # 新接口：run_quick_analysis(filters, options)
            filters = input_file_or_filters
            options = output_dir_or_options or {}
            
            # 从配置中获取输入文件路径
            input_file = self.config_manager.get_input_file_path()
            output_config = self.config_manager.get_output_config()
            output_dir = output_config.get('base_dir', 'output')
            
            analysis_options = {
                'group_by': ['strategy_name'],
                'time_series_types': ['daily'],
                'filters': filters,
                'options': options
            }
            
            return self.run_analysis(input_file, output_dir, analysis_options)
        else:
            # 旧接口：run_quick_analysis(input_file, output_dir)
            input_file = input_file_or_filters
            output_dir = output_dir_or_options
            
            analysis_options = {
                'group_by': ['strategy_name'],
                'time_series_types': ['daily']
            }
            
            return self.run_analysis(input_file, output_dir, analysis_options)
    
    def run_detailed_analysis(self, input_file_or_filters=None, output_dir_or_options=None) -> Dict[str, Any]:
        """运行详细分析（所有指标和图表）
        
        Args:
            input_file_or_filters: 输入文件路径(str) 或 过滤条件(dict)
            output_dir_or_options: 输出目录路径(str) 或 选项配置(dict)
            
        Returns:
            详细分析结果字典
        """
        self.logger.info("开始执行详细分析")
        
        # 判断调用方式
        if isinstance(input_file_or_filters, dict):
            # 新接口：run_detailed_analysis(filters, options)
            filters = input_file_or_filters
            options = output_dir_or_options or {}
            
            # 从配置中获取输入文件路径
            input_file = self.config_manager.get_input_file_path()
            output_config = self.config_manager.get_output_config()
            output_dir = output_config.get('base_dir', 'output')
            
            analysis_options = {
                'group_by': ['strategy_name', 'symbol', 'strategy_signal'],
                'time_series_types': ['daily', 'weekly', 'monthly', 'rolling'],
                'filters': filters,
                'options': options
            }
            
            return self.run_analysis(input_file, output_dir, analysis_options)
        else:
            # 旧接口：run_detailed_analysis(input_file, output_dir)
            input_file = input_file_or_filters
            output_dir = output_dir_or_options
            
            analysis_options = {
                'group_by': ['strategy_name', 'symbol', 'strategy_signal'],
                'time_series_types': ['daily', 'weekly', 'monthly', 'rolling']
            }
            
            return self.run_analysis(input_file, output_dir, analysis_options)
    
    def run_full_analysis(self, input_file_or_filters=None, output_dir_or_options=None) -> Dict[str, Any]:
        """运行完整分析（所有功能）
        
        Args:
            input_file_or_filters: 输入文件路径(str) 或 过滤条件(dict)
            output_dir_or_options: 输出目录路径(str) 或 选项配置(dict)
            
        Returns:
            完整分析结果字典
        """
        self.logger.info("开始执行完整分析")
        
        # 判断调用方式
        if isinstance(input_file_or_filters, dict):
            # 新接口：run_full_analysis(filters, options)
            filters = input_file_or_filters
            options = output_dir_or_options or {}
            
            # 从配置中获取输入文件路径
            input_file = self.config_manager.get_input_file_path()
            output_config = self.config_manager.get_output_config()
            output_dir = output_config.get('base_dir', 'output')
            
            analysis_options = {
                'group_by': ['strategy_name', 'symbol', 'strategy_signal', 'direction'],
                'time_series_types': ['daily', 'weekly', 'monthly', 'rolling'],
                'filters': filters,
                'options': options
            }
            
            return self.run_analysis(input_file, output_dir, analysis_options)
        else:
            # 旧接口：run_full_analysis(input_file, output_dir)
            input_file = input_file_or_filters
            output_dir = output_dir_or_options
            
            analysis_options = {
                'group_by': ['strategy_name', 'symbol', 'strategy_signal', 'direction'],
                'time_series_types': ['daily', 'weekly', 'monthly', 'rolling']
            }
            
            return self.run_analysis(input_file, output_dir, analysis_options)
    
    def _load_and_validate_data(self, filters: Optional[Dict[str, Any]] = None) -> None:
        """加载和验证数据（用于dry_run）
        
        Args:
            filters: 数据过滤条件
        """
        self.logger.info("加载和验证数据...")
        
        try:
            # 加载原始数据
            raw_data = self._load_data()
            self.logger.info(f"成功加载 {len(raw_data)} 条原始交易记录")
            
            # 应用过滤条件
            if filters:
                filtered_data = raw_data.copy()
                
                if 'strategies' in filters:
                    filtered_data = filtered_data[filtered_data['strategy_name'].isin(filters['strategies'])]
                    self.logger.info(f"策略过滤后剩余 {len(filtered_data)} 条记录")
                
                if 'symbols' in filters:
                    filtered_data = filtered_data[filtered_data['symbol'].isin(filters['symbols'])]
                    self.logger.info(f"合约过滤后剩余 {len(filtered_data)} 条记录")
                
                if 'start_date' in filters:
                    filtered_data = filtered_data[filtered_data['datetime'] >= filters['start_date']]
                    self.logger.info(f"开始日期过滤后剩余 {len(filtered_data)} 条记录")
                
                if 'end_date' in filters:
                    filtered_data = filtered_data[filtered_data['datetime'] <= filters['end_date']]
                    self.logger.info(f"结束日期过滤后剩余 {len(filtered_data)} 条记录")
            
            self.logger.info("数据验证完成")
            
        except Exception as e:
            self.logger.error(f"数据加载或验证失败: {e}")
            raise


def create_argument_parser():
    """创建命令行参数解析器
    
    Returns:
        ArgumentParser对象
    """
    parser = argparse.ArgumentParser(
        description='趋势策略交易分析系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py --input input/merged_trades.csv --output output/
  python main.py --input input/merged_trades.csv --mode quick
  python main.py --input input/merged_trades.csv --mode detailed --config custom_config.yaml
        """
    )
    
    parser.add_argument(
        '--input', '-i',
        type=str,
        default='input/merged_trades.csv',
        help='输入CSV文件路径 (默认: input/merged_trades.csv)'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=str,
        default='output',
        help='输出目录路径 (默认: output)'
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config/config.yaml',
        help='配置文件路径 (默认: config/config.yaml)'
    )
    
    parser.add_argument(
        '--mode', '-m',
        type=str,
        choices=['quick', 'detailed', 'full'],
        default='full',
        help='分析模式: quick(快速), detailed(详细), full(完整) (默认: full)'
    )
    
    parser.add_argument(
        '--log-level',
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    parser.add_argument(
        '--version', '-v',
        action='version',
        version='趋势策略交易分析系统 v1.0.0'
    )
    
    return parser


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    try:
        # 检查输入文件是否存在
        input_path = Path(args.input)
        if not input_path.exists():
            print(f"错误: 输入文件不存在: {args.input}")
            sys.exit(1)
        
        # 检查配置文件是否存在
        config_path = Path(args.config)
        if not config_path.exists():
            print(f"错误: 配置文件不存在: {args.config}")
            sys.exit(1)
        
        # 创建输出目录
        output_path = Path(args.output)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化分析系统
        print("正在初始化趋势策略分析系统...")
        analyzer = TrendStrategyAnalyzer(args.config)
        
        # 根据模式运行分析
        print(f"开始执行 {args.mode} 模式分析...")
        
        if args.mode == 'quick':
            results = analyzer.run_quick_analysis(args.input, args.output)
        elif args.mode == 'detailed':
            results = analyzer.run_detailed_analysis(args.input, args.output)
        else:  # full mode
            results = analyzer.run_analysis(args.input, args.output)
        
        # 打印分析摘要
        print("\n" + "="*60)
        print("分析完成！摘要信息:")
        print("="*60)
        
        summary = results['analysis_summary']
        print(f"已完成信号数量: {summary['total_completed_signals']}")
        print(f"未完成信号数量: {summary['total_uncompleted_signals']}")
        print(f"总盈亏: {summary['total_pnl']:.2f}")
        print(f"整体胜率: {summary['overall_win_rate']*100:.2f}%")
        
        if summary['best_strategy']:
            print(f"最佳策略: {summary['best_strategy']} (盈亏: {summary['best_strategy_pnl']:.2f})")
        
        if summary['worst_strategy']:
            print(f"最差策略: {summary['worst_strategy']} (盈亏: {summary['worst_strategy_pnl']:.2f})")
        
        print(f"\n报告文件已生成到: {args.output}")
        print("主要文件:")
        for file_type, file_path in results['report_files'].items():
            print(f"  - {file_type}: {file_path}")
        
        print("\n分析完成！")
        
    except KeyboardInterrupt:
        print("\n分析被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n分析过程中出现错误: {e}")
        print(f"详细错误信息: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == '__main__':
    main()