# 趋势策略分析系统增强功能说明

## 概述

本次增强为 `pnl_multidimensional_analysis.xlsx` 文件添加了两个重要功能：

1. **行业分析工作表** - 为每个信号类型（如simple_boll_day）增加按行业维度的分析
2. **平均持仓时间指标** - 在所有分析中添加持仓时间相关的指标

## 新增功能详细说明

### 1. 行业分析工作表

#### 功能描述
- 为每个信号组合（strategy_signal + signal_freq）自动生成对应的行业分析工作表
- 工作表命名规则：`{strategy_signal}_{signal_freq}_行业分析`
- 例如：`simple_boll_day_行业分析`、`simple_rsi_hour_行业分析`

#### 包含指标
每个行业分析工作表包含以下指标：

| 指标名称 | 说明 | 计算方式 |
|---------|------|---------|
| industry | 行业名称 | 从合约配置中获取 |
| 信号数量 | 该行业的信号总数 | 计数统计 |
| 胜率 | 盈利信号占比 | 盈利信号数 / 总有效信号数 |
| 盈亏比 | 平均盈利与平均亏损的比值 | 平均盈利价差 / 平均亏损价差 |
| 平均盈亏率 | 所有信号的平均盈亏率 | pnl_rate的均值 |
| 平均持仓天数 | 平均持仓时间 | duration_days的均值 |
| 总盈亏金额 | 该行业总盈亏金额 | signal_pnl_amount的总和 |
| 平均盈亏金额 | 平均每个信号的盈亏金额 | signal_pnl_amount的均值 |
| 最大盈亏率 | 最大单次盈亏率 | pnl_rate的最大值 |
| 最小盈亏率 | 最小单次盈亏率 | pnl_rate的最小值 |
| 盈亏率标准差 | 盈亏率的波动性 | pnl_rate的标准差 |
| 夏普比率 | 风险调整后收益 | 平均盈亏率 / 盈亏率标准差 |
| 主要合约类别 | 该行业中信号数量最多的合约类别 | 统计分析 |
| 涉及合约类别数 | 该行业涉及的合约类别总数 | 去重计数 |

### 2. 平均持仓时间指标

#### 功能描述
在原有的合约类别分析工作表中新增持仓时间相关指标

#### 新增指标

| 指标名称 | 说明 | 计算方式 |
|---------|------|---------|
| 平均持仓天数 | 平均持仓时间 | duration_days的均值 |
| 最长持仓天数 | 最长持仓时间 | duration_days的最大值 |
| 最短持仓天数 | 最短持仓时间 | duration_days的最小值 |
| 持仓天数标准差 | 持仓时间的波动性 | duration_days的标准差 |
| 中位数持仓天数 | 持仓时间的中位数 | duration_days的中位数 |

## Excel文件结构示例

假设有以下信号类型：
- simple_boll_day
- simple_rsi_hour

生成的Excel文件将包含以下工作表：

```
pnl_multidimensional_analysis.xlsx
├── 汇总                           # 总体汇总工作表
├── simple_boll_day               # 按合约类别分析（原有功能+新增持仓时间指标）
├── simple_boll_day_行业分析       # 按行业分析（新增功能）
├── simple_rsi_hour               # 按合约类别分析（原有功能+新增持仓时间指标）
└── simple_rsi_hour_行业分析       # 按行业分析（新增功能）
```

## 使用方法

### 运行分析
```bash
# 运行完整分析
python main.py --input input/merged_trades.csv --output output --analysis-type full

# 或使用快速分析
python main.py --input input/merged_trades.csv --output output --analysis-type quick
```

### 查看结果
1. 打开生成的 `output/data/pnl_multidimensional_analysis.xlsx` 文件
2. 查看各个工作表：
   - 原有工作表现在包含了持仓时间指标
   - 新增的"行业分析"工作表提供行业维度的深入分析

## 数据要求

为了使用新功能，输入数据必须包含以下字段：
- `industry`: 行业信息（从config.yaml的contracts配置中自动获取）
- `duration_days`: 持仓天数（系统自动计算）

这些字段在数据预处理阶段会自动添加，无需手动处理。

## 配置说明

行业信息来源于 `config/config.yaml` 文件中的 `contracts` 配置：

```yaml
contracts:
  CU:
    name: "铜"
    industry: "有色金属"
    mult: 5
  RB:
    name: "螺纹钢"
    industry: "黑色金属"
    mult: 10
  # ... 更多合约配置
```

## 示例输出

### 行业分析工作表示例
| industry | 信号数量 | 胜率 | 盈亏比 | 平均盈亏率 | 平均持仓天数 | 总盈亏金额 | 夏普比率 |
|----------|---------|------|--------|------------|-------------|------------|----------|
| 有色金属 | 15 | 0.6667 | 1.75 | 0.0234 | 2.3 | 125000 | 0.8542 |
| 黑色金属 | 12 | 0.5833 | 1.42 | 0.0156 | 1.8 | 89000 | 0.7231 |

### 合约类别分析工作表示例（新增持仓时间指标）
| symbol_category | 信号数量 | 胜率 | 盈亏比 | 平均持仓天数 | 最长持仓天数 | 最短持仓天数 |
|-----------------|---------|------|--------|-------------|-------------|-------------|
| CU | 8 | 0.75 | 1.85 | 2.5 | 5.2 | 0.8 |
| AL | 7 | 0.57 | 1.65 | 2.1 | 4.8 | 1.2 |

## 技术实现

### 核心修改文件
1. `src/performance_calculator.py`
   - 增强 `calculate_multidimensional_pnl_summary()` 方法
   - 新增 `_calculate_industry_metrics()` 方法
   - 在 `_calculate_detailed_pnl_metrics()` 中添加持仓时间指标

2. `src/report_generator.py`
   - Excel导出功能自动支持新的工作表结构

### 关键代码逻辑
```python
# 按行业分组分析
if 'industry' in group_df.columns:
    industry_results = []
    industry_groups = group_df.groupby('industry')
    
    for industry, industry_df in industry_groups:
        industry_metrics = self._calculate_industry_metrics(industry_df, industry)
        industry_results.append(industry_metrics)
    
    if industry_results:
        industry_summary_df = pd.DataFrame(industry_results)
        industry_key_name = f"{strategy_signal}_{signal_freq}_行业分析"
        result_dict[industry_key_name] = industry_summary_df
```

## 注意事项

1. **数据完整性**: 确保输入数据包含完整的合约信息，以便正确映射到行业
2. **配置文件**: 新增合约时需要在config.yaml中添加对应的行业信息
3. **Excel限制**: 工作表名称会自动处理以符合Excel的命名规范（最大31字符）
4. **性能考虑**: 大数据量时，行业分析会增加一定的计算时间

## 版本兼容性

- 新功能向后兼容，不影响现有功能
- 如果数据中缺少industry或duration_days字段，相关指标会显示为0或默认值
- Excel文件格式保持不变，只是增加了新的工作表和指标列