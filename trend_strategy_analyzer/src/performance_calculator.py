"""绩效指标计算模块

基于已完成信号列表，计算核心绩效指标。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass
from .logger_config import LoggerMixin
from .config_manager import ConfigManager
from .position_matcher import CompletedSignal


@dataclass
class PerformanceMetrics:
    """绩效指标数据结构"""
    group_key: str
    total_signals: int
    winning_signals: int
    losing_signals: int
    zero_pnl_signals: int
    win_rate: float
    total_pnl_rate: float
    average_pnl: float
    average_winning_pnl: float
    average_losing_pnl: float
    profit_loss_ratio: float
    max_pnl: float
    min_pnl: float
    total_volume: float
    total_commission_incurred: float
    average_duration_days: float
    max_duration_days: float
    min_duration_days: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'group_key': self.group_key,
            'total_signals': self.total_signals,
            'winning_signals': self.winning_signals,
            'losing_signals': self.losing_signals,
            'zero_pnl_signals': self.zero_pnl_signals,
            'win_rate': self.win_rate,
            'total_pnl_rate': self.total_pnl_rate,
            'average_pnl': self.average_pnl,
            'average_winning_pnl': self.average_winning_pnl,
            'average_losing_pnl': self.average_losing_pnl,
            'profit_loss_ratio': self.profit_loss_ratio,
            'max_pnl': self.max_pnl,
            'min_pnl': self.min_pnl,
            'total_volume': self.total_volume,
            'total_commission_incurred': self.total_commission_incurred,
            'average_duration_days': self.average_duration_days,
            'max_duration_days': self.max_duration_days,
            'min_duration_days': self.min_duration_days
        }


class PerformanceCalculator(LoggerMixin):
    """绩效指标计算器
    
    负责计算各种绩效指标。
    """
    
    def __init__(self, config_manager: ConfigManager):
        """初始化绩效计算器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.exclude_zero_pnl = config_manager.should_exclude_zero_pnl()
        self.min_trades_for_ratio = config_manager.get_min_trades_for_ratio()
        self.groupby_fields = config_manager.get_groupby_fields()
    
    def calculate_performance_metrics(self, completed_signals: List[CompletedSignal],
                                    groupby_fields: Optional[List[str]] = None) -> Dict[str, PerformanceMetrics]:
        """计算绩效指标
        
        Args:
            completed_signals: 已完成信号列表
            groupby_fields: 分组字段列表
            
        Returns:
            分组绩效指标字典
        """
        self.log_info("开始计算绩效指标")
        
        if not completed_signals:
            self.log_warning("没有已完成信号，无法计算绩效指标")
            return {}
        
        # 转换为DataFrame便于处理
        df = self._signals_to_dataframe(completed_signals)
        
        # 使用提供的分组字段或配置中的默认字段
        if groupby_fields is None:
            groupby_fields = self.groupby_fields or ['overall']
        
        # 如果没有指定分组字段，计算整体指标
        if not groupby_fields or groupby_fields == ['overall']:
            overall_metrics = self._calculate_group_metrics(df, 'overall')
            return {'overall': overall_metrics}
        
        # 按指定字段分组计算
        performance_results = {}
        
        # 验证分组字段是否存在
        valid_groupby_fields = [field for field in groupby_fields if field in df.columns]
        if not valid_groupby_fields:
            self.log_warning(f"指定的分组字段不存在: {groupby_fields}，使用整体计算")
            overall_metrics = self._calculate_group_metrics(df, 'overall')
            return {'overall': overall_metrics}
        
        # 按字段分组
        if len(valid_groupby_fields) == 1:
            # 单字段分组
            field = valid_groupby_fields[0]
            for group_value in df[field].unique():
                group_df = df[df[field] == group_value]
                group_key = f"{field}={group_value}"
                performance_results[group_key] = self._calculate_group_metrics(group_df, group_key)
        else:
            # 多字段分组
            grouped = df.groupby(valid_groupby_fields)
            for group_values, group_df in grouped:
                if isinstance(group_values, tuple):
                    group_key = '_'.join([f"{field}={value}" for field, value in zip(valid_groupby_fields, group_values)])
                else:
                    group_key = f"{valid_groupby_fields[0]}={group_values}"
                performance_results[group_key] = self._calculate_group_metrics(group_df, group_key)
        
        # 添加整体指标
        performance_results['overall'] = self._calculate_group_metrics(df, 'overall')
        
        self.log_info(f"绩效指标计算完成，共 {len(performance_results)} 个分组")
        return performance_results
    
    def _signals_to_dataframe(self, completed_signals: List[CompletedSignal]) -> pd.DataFrame:
        """将已完成信号转换为DataFrame
        
        Args:
            completed_signals: 已完成信号列表
            
        Returns:
            信号DataFrame
        """
        data = []
        for signal in completed_signals:
            # 从策略名称中提取特征（如果预处理模块已添加）
            strategy_signal = self._extract_strategy_signal(signal.strategy_name)
            signal_freq = self._extract_signal_frequency(signal.strategy_name)
            symbol_category = self._extract_symbol_category(signal.symbol)
            
            data.append({
                'signal_id': signal.signal_id,
                'strategy_name': signal.strategy_name,
                'strategy_signal': strategy_signal,
                'signal_freq': signal_freq,
                'symbol': signal.symbol,
                'symbol_category': symbol_category,
                'direction': signal.direction,
                'open_datetime': signal.first_open_datetime,
                'close_datetime': signal.close_datetime,
                'open_price': signal.weighted_avg_open_price,
                'close_price': signal.weighted_avg_close_price,
                'original_open_volume': signal.total_open_volume,
                'pnl_rate': signal.pnl_rate,
                'total_price_difference': signal.total_price_difference,
                'total_commission_incurred': signal.total_commission_incurred,
                'duration_days': signal.duration_days,
                'num_close_transactions': len(signal.close_transactions),
                'num_open_transactions': len(signal.open_transactions),
                # 新增字段
                'contract_multiplier': signal.contract_multiplier,
                'industry': signal.industry,
                'contract_name': signal.contract_name,
                'position_market_value': signal.position_market_value,
                'signal_pnl_amount': signal.signal_pnl_amount
            })
        
        return pd.DataFrame(data)
    
    def _extract_strategy_signal(self, strategy_name: str) -> str:
        """从策略名称提取信号类型"""
        strategy_signal_mapping = self.config_manager.get_strategy_signal_mapping()
        strategy_name_lower = str(strategy_name).lower()
        
        for signal_type, keywords in strategy_signal_mapping.items():
            for keyword in keywords:
                if keyword.lower() in strategy_name_lower:
                    return signal_type
        
        return 'unknown_signal'
    
    def _extract_signal_frequency(self, strategy_name: str) -> str:
        """从策略名称提取信号频率"""
        signal_frequency_mapping = self.config_manager.get_signal_frequency_mapping()
        strategy_name_lower = str(strategy_name).lower()
        
        for freq_type, keywords in signal_frequency_mapping.items():
            for keyword in keywords:
                if keyword.lower() in strategy_name_lower:
                    return freq_type
        
        return 'unknown_freq'
    
    def _extract_symbol_category(self, symbol: str) -> str:
        """从合约代码提取类别"""
        import re
        english_chars = re.findall(r'[A-Za-z]+', str(symbol))
        if english_chars:
            return ''.join(english_chars).upper()
        else:
            return 'unknown_category'
    
    def _calculate_group_metrics(self, df: pd.DataFrame, group_key: str) -> PerformanceMetrics:
        """计算单个分组的绩效指标
        
        Args:
            df: 分组数据DataFrame
            group_key: 分组键
            
        Returns:
            绩效指标
        """
        if df.empty:
            return self._create_empty_metrics(group_key)
        
        # 基本统计
        total_signals = len(df)
        total_pnl_rate = df['pnl_rate'].sum()
        total_volume = df['original_open_volume'].sum()
        total_commission = df['total_commission_incurred'].sum()
        
        # 盈亏分类（基于total_price_difference）
        winning_df = df[df['total_price_difference'] > 0]
        losing_df = df[df['total_price_difference'] < 0]
        zero_pnl_df = df[df['total_price_difference'] == 0]
        
        winning_signals = len(winning_df)
        losing_signals = len(losing_df)
        zero_pnl_signals = len(zero_pnl_df)
        
        # 胜率计算（基于total_price_difference）
        if self.exclude_zero_pnl:
            effective_signals = winning_signals + losing_signals
            win_rate = winning_signals / effective_signals if effective_signals > 0 else 0
        else:
            win_rate = winning_signals / total_signals if total_signals > 0 else 0
        
        # 平均盈亏率（基于pnl_rate）
        average_pnl_rate = total_pnl_rate / total_signals if total_signals > 0 else 0
        average_winning_pnl_rate = winning_df['pnl_rate'].mean() if not winning_df.empty else 0
        average_losing_pnl_rate = losing_df['pnl_rate'].mean() if not losing_df.empty else 0
        
        # 盈亏比（基于pnl_rate的平均值）
        if losing_signals >= self.min_trades_for_ratio:
            average_winning_pnl_rate = winning_df['pnl_rate'].mean() if not winning_df.empty else 0
            average_losing_pnl_rate = abs(losing_df['pnl_rate'].mean()) if not losing_df.empty else 0
            if average_losing_pnl_rate > 0:
                profit_loss_ratio = average_winning_pnl_rate / average_losing_pnl_rate
            else:
                profit_loss_ratio = 0
        else:
            profit_loss_ratio = 0
        
        # 极值
        max_pnl_rate = df['pnl_rate'].max() if not df.empty else 0
        min_pnl_rate = df['pnl_rate'].min() if not df.empty else 0
        
        # 持续时间统计
        average_duration_days = df['duration_days'].mean() if not df.empty else 0
        max_duration_days = df['duration_days'].max() if not df.empty else 0
        min_duration_days = df['duration_days'].min() if not df.empty else 0
        
        return PerformanceMetrics(
            group_key=group_key,
            total_signals=total_signals,
            winning_signals=winning_signals,
            losing_signals=losing_signals,
            zero_pnl_signals=zero_pnl_signals,
            win_rate=win_rate,
            total_pnl_rate=total_pnl_rate,
            average_pnl=average_pnl_rate,
            average_winning_pnl=average_winning_pnl_rate,
            average_losing_pnl=average_losing_pnl_rate,
            profit_loss_ratio=profit_loss_ratio,
            max_pnl=max_pnl_rate,
            min_pnl=min_pnl_rate,
            total_volume=total_volume,
            total_commission_incurred=total_commission,
            average_duration_days=average_duration_days,
            max_duration_days=max_duration_days,
            min_duration_days=min_duration_days
        )
    
    def _create_empty_metrics(self, group_key: str) -> PerformanceMetrics:
        """创建空的绩效指标
        
        Args:
            group_key: 分组键
            
        Returns:
            空的绩效指标
        """
        return PerformanceMetrics(
            group_key=group_key,
            total_signals=0,
            winning_signals=0,
            losing_signals=0,
            zero_pnl_signals=0,
            win_rate=0,
            total_pnl_rate=0,
            average_pnl=0,
            average_winning_pnl=0,
            average_losing_pnl=0,
            profit_loss_ratio=0,
            max_pnl=0,
            min_pnl=0,
            total_volume=0,
            total_commission_incurred=0,
            average_duration_days=0,
            max_duration_days=0,
            min_duration_days=0
        )
    
    def calculate_risk_metrics(self, completed_signals: List[CompletedSignal]) -> Dict[str, float]:
        """计算风险指标
        
        Args:
            completed_signals: 已完成信号列表
            
        Returns:
            风险指标字典
        """
        if not completed_signals:
            return {}
        
        pnl_rate_series = pd.Series([signal.pnl_rate for signal in completed_signals])
        
        # 计算累计盈亏率
        cumulative_pnl_rate = pnl_rate_series.cumsum()
        
        # 最大回撤
        running_max = cumulative_pnl_rate.expanding().max()
        drawdown = cumulative_pnl_rate - running_max
        max_drawdown = drawdown.min()
        
        # 夏普比率（假设无风险利率为0）
        if pnl_rate_series.std() != 0:
            sharpe_ratio = pnl_rate_series.mean() / pnl_rate_series.std()
        else:
            sharpe_ratio = 0
        
        # 卡尔马比率
        if max_drawdown != 0:
            calmar_ratio = pnl_rate_series.mean() / abs(max_drawdown)
        else:
            calmar_ratio = 0
        
        # 波动率
        volatility = pnl_rate_series.std()
        
        # VaR (Value at Risk) - 95%置信度
        var_95 = pnl_rate_series.quantile(0.05)
        
        return {
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'calmar_ratio': calmar_ratio,
            'volatility': volatility,
            'var_95': var_95,
            'total_return': cumulative_pnl_rate.iloc[-1] if not cumulative_pnl_rate.empty else 0
        }
    
    def export_performance_metrics_to_dataframe(self, 
                                               performance_metrics: Dict[str, PerformanceMetrics]) -> pd.DataFrame:
        """将绩效指标导出为DataFrame
        
        Args:
            performance_metrics: 绩效指标字典
            
        Returns:
            绩效指标DataFrame
        """
        if not performance_metrics:
            return pd.DataFrame()
        
        data = [metrics.to_dict() for metrics in performance_metrics.values()]
        return pd.DataFrame(data)
    
    def get_top_performers(self, performance_metrics: Dict[str, PerformanceMetrics],
                          metric: str = 'total_pnl_rate', top_n: int = 10) -> List[PerformanceMetrics]:
        """获取表现最佳的分组
        
        Args:
            performance_metrics: 绩效指标字典
            metric: 排序指标
            top_n: 返回数量
            
        Returns:
            表现最佳的绩效指标列表
        """
        if not performance_metrics:
            return []
        
        # 按指定指标排序
        sorted_metrics = sorted(
            performance_metrics.values(),
            key=lambda x: getattr(x, metric, 0),
            reverse=True
        )
        
        return sorted_metrics[:top_n]
    
    def get_performance_summary(self, performance_metrics: Dict[str, PerformanceMetrics]) -> Dict[str, Any]:
        """获取绩效摘要
        
        Args:
            performance_metrics: 绩效指标字典
            
        Returns:
            绩效摘要字典
        """
        if not performance_metrics:
            return {}
        
        # 获取整体指标
        overall_metrics = performance_metrics.get('overall')
        if not overall_metrics:
            return {}
        
        # 计算分组统计
        group_metrics = [m for k, m in performance_metrics.items() if k != 'overall']
        
        if group_metrics:
            best_win_rate = max(group_metrics, key=lambda x: x.win_rate)
            best_pnl = max(group_metrics, key=lambda x: x.total_pnl_rate)
            best_ratio = max(group_metrics, key=lambda x: x.profit_loss_ratio)
            
            summary = {
                'overall_metrics': overall_metrics.to_dict(),
                'total_groups': len(group_metrics),
                'best_win_rate_group': {
                    'group': best_win_rate.group_key,
                    'win_rate': best_win_rate.win_rate
                },
                'best_pnl_group': {
                    'group': best_pnl.group_key,
                    'total_pnl_rate': best_pnl.total_pnl_rate
                },
                'best_ratio_group': {
                    'group': best_ratio.group_key,
                    'profit_loss_ratio': best_ratio.profit_loss_ratio
                }
            }
        else:
            summary = {
                'overall_metrics': overall_metrics.to_dict(),
                'total_groups': 0
            }
        
        return summary
    
    def compare_performance(self, metrics1: PerformanceMetrics, 
                          metrics2: PerformanceMetrics) -> Dict[str, float]:
        """比较两个绩效指标
        
        Args:
            metrics1: 第一个绩效指标
            metrics2: 第二个绩效指标
            
        Returns:
            比较结果字典
        """
        comparison = {}
        
        # 比较关键指标
        key_metrics = ['win_rate', 'total_pnl_rate', 'average_pnl', 'profit_loss_ratio']
        
        for metric in key_metrics:
            value1 = getattr(metrics1, metric, 0)
            value2 = getattr(metrics2, metric, 0)
            
            if value2 != 0:
                change_pct = ((value1 - value2) / value2) * 100
            else:
                change_pct = 0 if value1 == 0 else float('inf')
            
            comparison[f'{metric}_change_pct'] = change_pct
            comparison[f'{metric}_diff'] = value1 - value2
        
        return comparison
    
    def calculate_pnl_rate_summary(self, completed_signals_df: pd.DataFrame) -> pd.DataFrame:
        """计算pnl_rate的多维度汇总
        
        Args:
            completed_signals_df: 已完成信号的DataFrame
            
        Returns:
            按strategy_signal、signal_freq、symbol_category分组的平均pnl_rate汇总DataFrame
        """
        if completed_signals_df.empty:
            return pd.DataFrame()
        
        # 按指定维度分组并计算平均pnl_rate
        summary_df = completed_signals_df.groupby(
            ['strategy_signal', 'signal_freq', 'symbol_category']
        ).agg({
            'pnl_rate': ['mean', 'count', 'std'],
            'total_price_difference': ['mean', 'sum']
        }).round(6)
        
        # 重命名列
        summary_df.columns = [
            'avg_pnl_rate', 'signal_count', 'pnl_rate_std',
            'avg_price_diff', 'total_price_diff'
        ]
        
        # 重置索引，将分组列转为普通列
        summary_df = summary_df.reset_index()
        
        # 按平均pnl_rate降序排序
        summary_df = summary_df.sort_values('avg_pnl_rate', ascending=False)
        
        return summary_df
    
    def calculate_multidimensional_pnl_summary(self, completed_signals_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """计算pnl_rate多维度汇总功能
        
        根据文档要求，对已完成的交易信号进行深入的绩效分析，
        特别是检查每个"开仓信号"在不同symbol_category下的止盈止损比例及整体表现。
        
        Args:
            completed_signals_df: 已完成信号的DataFrame，必须包含以下字段：
                - strategy_signal: 策略信号
                - signal_freq: 信号频率
                - symbol_category: 合约类别
                - pnl_rate: 盈亏率
                - total_price_difference: 总价格差
                - industry: 行业
                - duration_days: 持仓天数
                
        Returns:
            Dict[str, pd.DataFrame]: 以(strategy_signal, signal_freq)组合为键的字典，
            每个值包含按symbol_category分组的详细绩效指标DataFrame和按industry分组的行业分析DataFrame
        """
        if completed_signals_df.empty:
            return {}
        
        # 验证必需字段
        required_fields = ['strategy_signal', 'signal_freq', 'symbol_category', 'pnl_rate', 'total_price_difference']
        missing_fields = [field for field in required_fields if field not in completed_signals_df.columns]
        if missing_fields:
            self.log_warning(f"缺少必需字段: {missing_fields}")
            return {}
        
        # 第一层分组：按strategy_signal和signal_freq分组
        signal_groups = completed_signals_df.groupby(['strategy_signal', 'signal_freq'])
        
        result_dict = {}
        
        for (strategy_signal, signal_freq), group_df in signal_groups:
            # 第二层分组：在每个开仓信号内部按symbol_category分组
            category_groups = group_df.groupby('symbol_category')
            
            category_results = []
            
            for symbol_category, category_df in category_groups:
                metrics = self._calculate_detailed_pnl_metrics(category_df, symbol_category)
                category_results.append(metrics)
            
            # 创建该开仓信号的汇总DataFrame
            if category_results:
                summary_df = pd.DataFrame(category_results)
                # 按信号数量降序排序
                summary_df = summary_df.sort_values('信号数量', ascending=False)
                
                # 生成键名
                key_name = f"{strategy_signal}_{signal_freq}"
                result_dict[key_name] = summary_df
                
                # 新增：按行业分组的分析
                if 'industry' in group_df.columns:
                    industry_results = []
                    industry_groups = group_df.groupby('industry')
                    
                    for industry, industry_df in industry_groups:
                        industry_metrics = self._calculate_industry_metrics(industry_df, industry)
                        industry_results.append(industry_metrics)
                    
                    if industry_results:
                        industry_summary_df = pd.DataFrame(industry_results)
                        # 按信号数量降序排序
                        industry_summary_df = industry_summary_df.sort_values('信号数量', ascending=False)
                        
                        # 生成行业分析键名
                        industry_key_name = f"{strategy_signal}_{signal_freq}_行业分析"
                        result_dict[industry_key_name] = industry_summary_df
        
        return result_dict
    
    def _calculate_detailed_pnl_metrics(self, df: pd.DataFrame, symbol_category: str) -> Dict[str, Any]:
        """计算详细的PnL指标（增强版）
        
        Args:
            df: 单个(strategy_signal, signal_freq, symbol_category)组合的数据
            symbol_category: 合约类别
            
        Returns:
            包含所有计算指标的字典
        """
        from scipy import stats
        
        metrics = {
            'symbol_category': symbol_category,
            '信号数量': len(df)
        }
        
        if df.empty:
            # 返回空指标
            empty_metrics = {
                '平均盈利pnl_rate': 0.0,
                '平均亏损pnl_rate': 0.0,
                '胜率': 0.0,
                '盈亏比': 0.0,
                '中位数盈亏率': 0.0,
                '盈亏率标准差': 0.0,
                '最大盈亏率': 0.0,
                '最小盈亏率': 0.0,
                '盈亏率偏度': 0.0,
                '盈亏率峰度': 0.0,
                '盈亏率夏普比率': 0.0,
                '止盈幅度中位数': 0.0,
                '止损幅度中位数': 0.0,
                '止盈幅度标准差': 0.0,
                '止损幅度标准差': 0.0,
                '总盈利': 0.0,
                '总亏损': 0.0,
                '盈利因子': 0.0,
                '止损信号平均持仓时间': 0.0,
                '止损信号持仓时间中位数': 0.0
            }
            metrics.update(empty_metrics)
            return metrics
        
        pnl_rates = df['pnl_rate']
        price_diffs = df['total_price_difference']
        
        # 1. 信号数量（已在上面计算）
        
        # 2. 平均盈利pnl_rate
        profit_pnl_rates = pnl_rates[pnl_rates > 0]
        avg_profit_pnl = profit_pnl_rates.mean() if not profit_pnl_rates.empty else 0.0
        
        # 3. 平均亏损pnl_rate
        loss_pnl_rates = pnl_rates[pnl_rates < 0]
        avg_loss_pnl = loss_pnl_rates.mean() if not loss_pnl_rates.empty else 0.0
        
        # 4. 胜率
        non_zero_pnl = pnl_rates[pnl_rates != 0]
        win_count = len(pnl_rates[pnl_rates > 0])
        total_non_zero = len(non_zero_pnl)
        win_rate = win_count / total_non_zero if total_non_zero > 0 else 0.0
        
        # 5. 盈亏比
        profit_price_diffs = price_diffs[price_diffs > 0]
        loss_price_diffs = price_diffs[price_diffs < 0]
        
        avg_profit_price_diff = profit_price_diffs.mean() if not profit_price_diffs.empty else 0.0
        avg_loss_price_diff = abs(loss_price_diffs.mean()) if not loss_price_diffs.empty else 0.0
        
        profit_loss_ratio = avg_profit_price_diff / avg_loss_price_diff if avg_loss_price_diff > 0 else 0.0
        
        # 6. 中位数盈亏率
        median_pnl_rate = pnl_rates.median()
        
        # 7. 盈亏率标准差
        pnl_rate_std = pnl_rates.std()
        
        # 8. 最大盈亏率
        max_pnl_rate = pnl_rates.max()
        
        # 9. 最小盈亏率
        min_pnl_rate = pnl_rates.min()
        
        # 10. 盈亏率偏度
        try:
            pnl_rate_skewness = stats.skew(pnl_rates.dropna())
            if np.isnan(pnl_rate_skewness):
                pnl_rate_skewness = 0.0
        except:
            pnl_rate_skewness = 0.0
        
        # 11. 盈亏率峰度
        try:
            pnl_rate_kurtosis = stats.kurtosis(pnl_rates.dropna())
            if np.isnan(pnl_rate_kurtosis):
                pnl_rate_kurtosis = 0.0
        except:
            pnl_rate_kurtosis = 0.0
        
        # 12. 盈亏率夏普比率（简化版）
        mean_pnl_rate = pnl_rates.mean()
        sharpe_ratio = mean_pnl_rate / pnl_rate_std if pnl_rate_std > 0 else 0.0
        
        # 新增指标计算
        # 13. 止盈幅度中位数
        profit_median = profit_pnl_rates.median() if not profit_pnl_rates.empty else 0.0
        
        # 14. 止损幅度中位数
        loss_median = abs(loss_pnl_rates.median()) if not loss_pnl_rates.empty else 0.0
        
        # 15. 止盈幅度标准差
        profit_std = profit_pnl_rates.std() if not profit_pnl_rates.empty else 0.0
        
        # 16. 止损幅度标准差
        loss_std = abs(loss_pnl_rates.std()) if not loss_pnl_rates.empty else 0.0
        
        # 17. 总盈利（盈利信号求和）
        total_profit = profit_pnl_rates.sum() if not profit_pnl_rates.empty else 0.0
        
        # 18. 总亏损（亏损信号求和）
        total_loss = abs(loss_pnl_rates.sum()) if not loss_pnl_rates.empty else 0.0
        
        # 19. 盈利因子（总盈利/总亏损）
        profit_factor = total_profit / total_loss if total_loss > 0 else 0.0
        
        # 20. 止损信号平均持仓时间和中位数
        if 'duration_days' in df.columns:
            loss_duration = df[df['pnl_rate'] < 0]['duration_days']
            avg_loss_duration = loss_duration.mean() if not loss_duration.empty else 0.0
            median_loss_duration = loss_duration.median() if not loss_duration.empty else 0.0
        else:
            avg_loss_duration = 0.0
            median_loss_duration = 0.0
        
        # 按规律排序的指标字典
        detailed_metrics = {
            # 基础指标
            '平均盈利pnl_rate': round(avg_profit_pnl, 6),
            '平均亏损pnl_rate': round(avg_loss_pnl, 6),
            '胜率': round(win_rate, 6),
            '盈亏比': round(profit_loss_ratio, 6),
            
            # 分布指标
            '中位数盈亏率': round(median_pnl_rate, 6),
            '盈亏率标准差': round(pnl_rate_std, 6),
            '最大盈亏率': round(max_pnl_rate, 6),
            '最小盈亏率': round(min_pnl_rate, 6),
            
            # 形状指标
            '盈亏率偏度': round(pnl_rate_skewness, 6),
            '盈亏率峰度': round(pnl_rate_kurtosis, 6),
            '盈亏率夏普比率': round(sharpe_ratio, 6),
            
            # 新增止盈止损指标
            '止盈幅度中位数': round(profit_median, 6),
            '止损幅度中位数': round(loss_median, 6),
            '止盈幅度标准差': round(profit_std, 6),
            '止损幅度标准差': round(loss_std, 6),
            
            # 盈亏汇总指标
            '总盈利': round(total_profit, 6),
            '总亏损': round(total_loss, 6),
            '盈利因子': round(profit_factor, 6),
            
            # 时间指标
            '止损信号平均持仓时间': round(avg_loss_duration, 2),
            '止损信号持仓时间中位数': round(median_loss_duration, 2)
        }
        
        # 如果有新增字段，也计算相关指标
        if 'signal_pnl_amount' in df.columns:
            pnl_amounts = df['signal_pnl_amount']
            detailed_metrics.update({
                '总盈亏金额': round(pnl_amounts.sum(), 2),
                '平均盈亏金额': round(pnl_amounts.mean(), 2),
                '最大盈亏金额': round(pnl_amounts.max(), 2),
                '最小盈亏金额': round(pnl_amounts.min(), 2)
            })
        
        if 'position_market_value' in df.columns:
            market_values = df['position_market_value']
            detailed_metrics.update({
                '总持仓市值': round(market_values.sum(), 2),
                '平均持仓市值': round(market_values.mean(), 2),
                '最大持仓市值': round(market_values.max(), 2),
                '最小持仓市值': round(market_values.min(), 2)
            })
        
        # 删除行业数量指标，只保留主要行业
        if 'industry' in df.columns:
            industry_counts = df['industry'].value_counts()
            if len(industry_counts) > 0:
                detailed_metrics['主要行业'] = industry_counts.index[0]
        
        # 平均持仓时间指标
        if 'duration_days' in df.columns:
            duration_days = df['duration_days']
            detailed_metrics.update({
                '平均持仓天数': round(duration_days.mean(), 2),
                '最长持仓天数': round(duration_days.max(), 2),
                '最短持仓天数': round(duration_days.min(), 2),
                '持仓天数标准差': round(duration_days.std(), 2),
                '中位数持仓天数': round(duration_days.median(), 2)
            })
        
        metrics.update(detailed_metrics)
        return metrics
    
    def _calculate_industry_metrics(self, df: pd.DataFrame, industry: str) -> Dict[str, Any]:
        """计算行业维度的详细指标
        
        Args:
            df: 单个行业的数据
            industry: 行业名称
            
        Returns:
            包含行业分析指标的字典
        """
        from scipy import stats
        
        metrics = {
            'industry': industry,
            '信号数量': len(df)
        }
        
        if df.empty:
            # 返回空指标
            empty_metrics = {
                '胜率': 0.0,
                '盈亏比': 0.0,
                '平均盈亏率': 0.0,
                '平均持仓天数': 0.0,
                '总盈亏金额': 0.0,
                '平均盈亏金额': 0.0,
                '最大盈亏率': 0.0,
                '最小盈亏率': 0.0,
                '盈亏率标准差': 0.0,
                '夏普比率': 0.0
            }
            metrics.update(empty_metrics)
            return metrics
        
        pnl_rates = df['pnl_rate']
        price_diffs = df['total_price_difference']
        
        # 1. 胜率
        non_zero_pnl = pnl_rates[pnl_rates != 0]
        win_count = len(pnl_rates[pnl_rates > 0])
        total_non_zero = len(non_zero_pnl)
        win_rate = win_count / total_non_zero if total_non_zero > 0 else 0.0
        
        # 2. 盈亏比
        profit_price_diffs = price_diffs[price_diffs > 0]
        loss_price_diffs = price_diffs[price_diffs < 0]
        
        avg_profit_price_diff = profit_price_diffs.mean() if not profit_price_diffs.empty else 0.0
        avg_loss_price_diff = abs(loss_price_diffs.mean()) if not loss_price_diffs.empty else 0.0
        
        profit_loss_ratio = avg_profit_price_diff / avg_loss_price_diff if avg_loss_price_diff > 0 else 0.0
        
        # 3. 平均盈亏率
        avg_pnl_rate = pnl_rates.mean()
        
        # 4. 平均持仓天数
        avg_duration_days = 0.0
        if 'duration_days' in df.columns:
            avg_duration_days = df['duration_days'].mean()
        
        # 5. 盈亏金额相关指标
        total_pnl_amount = 0.0
        avg_pnl_amount = 0.0
        if 'signal_pnl_amount' in df.columns:
            pnl_amounts = df['signal_pnl_amount']
            total_pnl_amount = pnl_amounts.sum()
            avg_pnl_amount = pnl_amounts.mean()
        
        # 6. 风险指标
        max_pnl_rate = pnl_rates.max()
        min_pnl_rate = pnl_rates.min()
        pnl_rate_std = pnl_rates.std()
        
        # 7. 夏普比率
        sharpe_ratio = avg_pnl_rate / pnl_rate_std if pnl_rate_std > 0 else 0.0
        
        # 8. 合约分布
        symbol_categories = []
        if 'symbol_category' in df.columns:
            symbol_categories = df['symbol_category'].value_counts().to_dict()
        
        # 更新指标字典
        industry_metrics = {
            '胜率': round(win_rate, 4),
            '盈亏比': round(profit_loss_ratio, 4),
            '平均盈亏率': round(avg_pnl_rate, 6),
            '平均持仓天数': round(avg_duration_days, 2),
            '总盈亏金额': round(total_pnl_amount, 2),
            '平均盈亏金额': round(avg_pnl_amount, 2),
            '最大盈亏率': round(max_pnl_rate, 6),
            '最小盈亏率': round(min_pnl_rate, 6),
            '盈亏率标准差': round(pnl_rate_std, 6),
            '夏普比率': round(sharpe_ratio, 4),
            '主要合约类别': max(symbol_categories, key=symbol_categories.get) if symbol_categories else '未知',
            '涉及合约类别数': len(symbol_categories)
        }
        
        metrics.update(industry_metrics)
        return metrics