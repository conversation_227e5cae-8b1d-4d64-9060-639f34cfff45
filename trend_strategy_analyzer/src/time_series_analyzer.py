"""时序分析模块

提供绩效指标在时间维度上的动态变化分析。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass
from .logger_config import LoggerMixin
from .config_manager import ConfigManager
from .position_matcher import CompletedSignal


@dataclass
class TimeSeriesMetrics:
    """时序指标数据结构"""
    date: datetime
    period_type: str  # 'daily', 'weekly', 'monthly', 'quarterly'
    cumulative_pnl_rate: float
    period_pnl_rate: float
    cumulative_signals: int
    period_signals: int
    cumulative_win_rate: float
    period_win_rate: float
    cumulative_profit_loss_ratio: float
    period_profit_loss_ratio: float
    rolling_win_rate: Optional[float] = None
    rolling_profit_loss_ratio: Optional[float] = None
    max_drawdown: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'date': self.date,
            'period_type': self.period_type,
            'cumulative_pnl_rate': self.cumulative_pnl_rate,
            'period_pnl_rate': self.period_pnl_rate,
            'cumulative_signals': self.cumulative_signals,
            'period_signals': self.period_signals,
            'cumulative_win_rate': self.cumulative_win_rate,
            'period_win_rate': self.period_win_rate,
            'cumulative_profit_loss_ratio': self.cumulative_profit_loss_ratio,
            'period_profit_loss_ratio': self.period_profit_loss_ratio,
            'rolling_win_rate': self.rolling_win_rate,
            'rolling_profit_loss_ratio': self.rolling_profit_loss_ratio,
            'max_drawdown': self.max_drawdown
        }


class TimeSeriesAnalyzer(LoggerMixin):
    """时序分析器
    
    负责时间序列分析和动态指标计算。
    """
    
    def __init__(self, config_manager: ConfigManager):
        """初始化时序分析器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.rolling_window_config = config_manager.get_rolling_window_config()
        self.exclude_zero_pnl = config_manager.should_exclude_zero_pnl()
        self.min_trades_for_ratio = config_manager.get_min_trades_for_ratio()
    
    def analyze_time_series(self, completed_signals: List[CompletedSignal],
                          analysis_types: Optional[List[str]] = None) -> Dict[str, List[TimeSeriesMetrics]]:
        """执行时序分析
        
        Args:
            completed_signals: 已完成信号列表
            analysis_types: 分析类型列表 ['daily', 'weekly', 'monthly', 'quarterly']
            
        Returns:
            时序分析结果字典
        """
        self.log_info("开始时序分析")
        
        if not completed_signals:
            self.log_warning("没有已完成信号，无法进行时序分析")
            return {}
        
        # 默认分析类型
        if analysis_types is None:
            analysis_types = ['daily', 'weekly', 'monthly']
        
        # 转换为DataFrame
        df = self._signals_to_dataframe(completed_signals)
        
        # 按平仓时间排序
        df = df.sort_values('close_datetime').reset_index(drop=True)
        
        results = {}
        
        # 执行不同类型的时序分析
        for analysis_type in analysis_types:
            try:
                if analysis_type == 'daily':
                    results['daily'] = self._analyze_daily_metrics(df)
                elif analysis_type == 'weekly':
                    results['weekly'] = self._analyze_weekly_metrics(df)
                elif analysis_type == 'monthly':
                    results['monthly'] = self._analyze_monthly_metrics(df)
                elif analysis_type == 'quarterly':
                    results['quarterly'] = self._analyze_quarterly_metrics(df)
                elif analysis_type == 'rolling':
                    results['rolling'] = self._analyze_rolling_metrics(df)
                else:
                    self.log_warning(f"不支持的分析类型: {analysis_type}")
            except Exception as e:
                self.log_error(f"执行 {analysis_type} 分析时出错: {e}")
        
        self.log_info(f"时序分析完成，共 {len(results)} 种分析类型")
        return results
    
    def _signals_to_dataframe(self, completed_signals: List[CompletedSignal]) -> pd.DataFrame:
        """将已完成信号转换为DataFrame
        
        Args:
            completed_signals: 已完成信号列表
            
        Returns:
            信号DataFrame
        """
        data = []
        for signal in completed_signals:
            data.append({
                'signal_id': signal.signal_id,
                'strategy_name': signal.strategy_name,
                'symbol': signal.symbol,
                'direction': signal.direction,
                'open_datetime': signal.first_open_datetime,
                'close_datetime': signal.close_datetime,
                'pnl_rate': signal.pnl_rate,
                'original_open_volume': signal.total_open_volume,
                'total_commission_incurred': signal.total_commission_incurred,
                'duration_days': signal.duration_seconds / 86400  # 转换为天数
            })
        
        df = pd.DataFrame(data)
        
        # 确保日期时间列是正确的类型
        df['close_datetime'] = pd.to_datetime(df['close_datetime'])
        df['open_datetime'] = pd.to_datetime(df['open_datetime'])
        df['close_date'] = df['close_datetime'].dt.date
        
        return df
    
    def _analyze_daily_metrics(self, df: pd.DataFrame) -> List[TimeSeriesMetrics]:
        """分析每日指标
        
        Args:
            df: 信号DataFrame
            
        Returns:
            每日时序指标列表
        """
        self.log_info("开始每日指标分析")
        
        # 按日期分组
        daily_groups = df.groupby('close_date')
        
        metrics_list = []
        cumulative_pnl = 0
        cumulative_signals = 0
        cumulative_winning = 0
        cumulative_losing = 0
        cumulative_pnl_series = []
        
        for date, group in daily_groups:
            # 当日指标
            period_pnl = group['pnl_rate'].sum()
            period_signals = len(group)
            period_winning = len(group[group['pnl_rate'] > 0])
            period_losing = len(group[group['pnl_rate'] < 0])
            
            # 累计指标
            cumulative_pnl += period_pnl
            cumulative_signals += period_signals
            cumulative_winning += period_winning
            cumulative_losing += period_losing
            cumulative_pnl_series.append(cumulative_pnl)
            
            # 计算胜率
            if self.exclude_zero_pnl:
                period_effective = period_winning + period_losing
                period_win_rate = period_winning / period_effective if period_effective > 0 else 0
                
                cumulative_effective = cumulative_winning + cumulative_losing
                cumulative_win_rate = cumulative_winning / cumulative_effective if cumulative_effective > 0 else 0
            else:
                period_win_rate = period_winning / period_signals if period_signals > 0 else 0
                cumulative_win_rate = cumulative_winning / cumulative_signals if cumulative_signals > 0 else 0
            
            # 计算盈亏比
            period_profit_loss_ratio = self._calculate_profit_loss_ratio(group)
            cumulative_profit_loss_ratio = self._calculate_profit_loss_ratio(
                df[df['close_date'] <= date]
            )
            
            # 计算最大回撤
            max_drawdown = self._calculate_max_drawdown(cumulative_pnl_series)
            
            metrics = TimeSeriesMetrics(
                date=datetime.combine(date, datetime.min.time()),
                period_type='daily',
                cumulative_pnl_rate=cumulative_pnl,
                period_pnl_rate=period_pnl,
                cumulative_signals=cumulative_signals,
                period_signals=period_signals,
                cumulative_win_rate=cumulative_win_rate,
                period_win_rate=period_win_rate,
                cumulative_profit_loss_ratio=cumulative_profit_loss_ratio,
                period_profit_loss_ratio=period_profit_loss_ratio,
                max_drawdown=max_drawdown
            )
            
            metrics_list.append(metrics)
        
        return metrics_list
    
    def _analyze_weekly_metrics(self, df: pd.DataFrame) -> List[TimeSeriesMetrics]:
        """分析每周指标
        
        Args:
            df: 信号DataFrame
            
        Returns:
            每周时序指标列表
        """
        self.log_info("开始每周指标分析")
        
        # 添加周标识
        df['week'] = df['close_datetime'].dt.to_period('W')
        
        return self._analyze_period_metrics(df, 'week', 'weekly')
    
    def _analyze_monthly_metrics(self, df: pd.DataFrame) -> List[TimeSeriesMetrics]:
        """分析每月指标
        
        Args:
            df: 信号DataFrame
            
        Returns:
            每月时序指标列表
        """
        self.log_info("开始每月指标分析")
        
        # 添加月标识
        df['month'] = df['close_datetime'].dt.to_period('M')
        
        return self._analyze_period_metrics(df, 'month', 'monthly')
    
    def _analyze_quarterly_metrics(self, df: pd.DataFrame) -> List[TimeSeriesMetrics]:
        """分析每季度指标
        
        Args:
            df: 信号DataFrame
            
        Returns:
            每季度时序指标列表
        """
        self.log_info("开始每季度指标分析")
        
        # 添加季度标识
        df['quarter'] = df['close_datetime'].dt.to_period('Q')
        
        return self._analyze_period_metrics(df, 'quarter', 'quarterly')
    
    def _analyze_period_metrics(self, df: pd.DataFrame, period_column: str, 
                              period_type: str) -> List[TimeSeriesMetrics]:
        """分析周期性指标的通用方法
        
        Args:
            df: 信号DataFrame
            period_column: 周期列名
            period_type: 周期类型
            
        Returns:
            周期时序指标列表
        """
        period_groups = df.groupby(period_column)
        
        metrics_list = []
        cumulative_pnl = 0
        cumulative_signals = 0
        cumulative_winning = 0
        cumulative_losing = 0
        cumulative_pnl_series = []
        
        for period, group in period_groups:
            # 当期指标
            period_pnl = group['pnl_rate'].sum()
            period_signals = len(group)
            period_winning = len(group[group['pnl_rate'] > 0])
            period_losing = len(group[group['pnl_rate'] < 0])
            
            # 累计指标
            cumulative_pnl += period_pnl
            cumulative_signals += period_signals
            cumulative_winning += period_winning
            cumulative_losing += period_losing
            cumulative_pnl_series.append(cumulative_pnl)
            
            # 计算胜率
            if self.exclude_zero_pnl:
                period_effective = period_winning + period_losing
                period_win_rate = period_winning / period_effective if period_effective > 0 else 0
                
                cumulative_effective = cumulative_winning + cumulative_losing
                cumulative_win_rate = cumulative_winning / cumulative_effective if cumulative_effective > 0 else 0
            else:
                period_win_rate = period_winning / period_signals if period_signals > 0 else 0
                cumulative_win_rate = cumulative_winning / cumulative_signals if cumulative_signals > 0 else 0
            
            # 计算盈亏比
            period_profit_loss_ratio = self._calculate_profit_loss_ratio(group)
            cumulative_profit_loss_ratio = self._calculate_profit_loss_ratio(
                df[df[period_column] <= period]
            )
            
            # 计算最大回撤
            max_drawdown = self._calculate_max_drawdown(cumulative_pnl_series)
            
            # 获取周期结束日期
            period_end_date = group['close_datetime'].max()
            
            metrics = TimeSeriesMetrics(
                date=period_end_date,
                period_type=period_type,
                cumulative_pnl_rate=cumulative_pnl,
                period_pnl_rate=period_pnl,
                cumulative_signals=cumulative_signals,
                period_signals=period_signals,
                cumulative_win_rate=cumulative_win_rate,
                period_win_rate=period_win_rate,
                cumulative_profit_loss_ratio=cumulative_profit_loss_ratio,
                period_profit_loss_ratio=period_profit_loss_ratio,
                max_drawdown=max_drawdown
            )
            
            metrics_list.append(metrics)
        
        return metrics_list
    
    def _analyze_rolling_metrics(self, df: pd.DataFrame) -> List[TimeSeriesMetrics]:
        """分析滚动窗口指标
        
        Args:
            df: 信号DataFrame
            
        Returns:
            滚动时序指标列表
        """
        self.log_info("开始滚动窗口指标分析")
        
        window_size = self.rolling_window_config.get('size', 30)
        window_type = self.rolling_window_config.get('type', 'days')  # 'days' or 'trades'
        
        metrics_list = []
        
        if window_type == 'trades':
            # 按交易数量滚动
            for i in range(window_size, len(df) + 1):
                window_df = df.iloc[i-window_size:i]
                
                rolling_win_rate = self._calculate_win_rate(window_df)
                rolling_profit_loss_ratio = self._calculate_profit_loss_ratio(window_df)
                
                # 使用窗口结束时的累计指标
                cumulative_df = df.iloc[:i]
                cumulative_pnl = cumulative_df['pnl_rate'].sum()
                cumulative_signals = len(cumulative_df)
                cumulative_win_rate = self._calculate_win_rate(cumulative_df)
                cumulative_profit_loss_ratio = self._calculate_profit_loss_ratio(cumulative_df)
                
                # 计算最大回撤
                cumulative_pnl_series = cumulative_df['pnl_rate'].cumsum().tolist()
                max_drawdown = self._calculate_max_drawdown(cumulative_pnl_series)
                
                metrics = TimeSeriesMetrics(
                    date=df.iloc[i-1]['close_datetime'],
                    period_type='rolling',
                    cumulative_pnl_rate=cumulative_pnl,
                    period_pnl_rate=window_df['pnl_rate'].sum(),
                    cumulative_signals=cumulative_signals,
                    period_signals=len(window_df),
                    cumulative_win_rate=cumulative_win_rate,
                    period_win_rate=0,  # 周期胜率在滚动分析中不适用
                    cumulative_profit_loss_ratio=cumulative_profit_loss_ratio,
                    period_profit_loss_ratio=0,  # 周期盈亏比在滚动分析中不适用
                    rolling_win_rate=rolling_win_rate,
                    rolling_profit_loss_ratio=rolling_profit_loss_ratio,
                    max_drawdown=max_drawdown
                )
                
                metrics_list.append(metrics)
        
        else:  # window_type == 'days'
            # 按天数滚动
            df['close_date'] = df['close_datetime'].dt.date
            unique_dates = sorted(df['close_date'].unique())
            
            for i, end_date in enumerate(unique_dates):
                start_date = end_date - timedelta(days=window_size)
                
                # 滚动窗口数据
                window_df = df[(df['close_date'] > start_date) & (df['close_date'] <= end_date)]
                
                if len(window_df) == 0:
                    continue
                
                rolling_win_rate = self._calculate_win_rate(window_df)
                rolling_profit_loss_ratio = self._calculate_profit_loss_ratio(window_df)
                
                # 累计数据
                cumulative_df = df[df['close_date'] <= end_date]
                cumulative_pnl = cumulative_df['pnl_rate'].sum()
                cumulative_signals = len(cumulative_df)
                cumulative_win_rate = self._calculate_win_rate(cumulative_df)
                cumulative_profit_loss_ratio = self._calculate_profit_loss_ratio(cumulative_df)
                
                # 计算最大回撤
                cumulative_pnl_series = cumulative_df.sort_values('close_datetime')['pnl_rate'].cumsum().tolist()
                max_drawdown = self._calculate_max_drawdown(cumulative_pnl_series)
                
                metrics = TimeSeriesMetrics(
                    date=datetime.combine(end_date, datetime.min.time()),
                    period_type='rolling',
                    cumulative_pnl_rate=cumulative_pnl,
                    period_pnl_rate=window_df['pnl_rate'].sum(),
                    cumulative_signals=cumulative_signals,
                    period_signals=len(window_df),
                    cumulative_win_rate=cumulative_win_rate,
                    period_win_rate=0,
                    cumulative_profit_loss_ratio=cumulative_profit_loss_ratio,
                    period_profit_loss_ratio=0,
                    rolling_win_rate=rolling_win_rate,
                    rolling_profit_loss_ratio=rolling_profit_loss_ratio,
                    max_drawdown=max_drawdown
                )
                
                metrics_list.append(metrics)
        
        return metrics_list
    
    def _calculate_win_rate(self, df: pd.DataFrame) -> float:
        """计算胜率
        
        Args:
            df: 信号DataFrame
            
        Returns:
            胜率
        """
        if df.empty:
            return 0
        
        winning_signals = len(df[df['pnl_rate'] > 0])
        
        if self.exclude_zero_pnl:
            losing_signals = len(df[df['pnl_rate'] < 0])
            effective_signals = winning_signals + losing_signals
            return winning_signals / effective_signals if effective_signals > 0 else 0
        else:
            total_signals = len(df)
            return winning_signals / total_signals if total_signals > 0 else 0
    
    def _calculate_profit_loss_ratio(self, df: pd.DataFrame) -> float:
        """计算盈亏比
        
        Args:
            df: 信号DataFrame
            
        Returns:
            盈亏比
        """
        if df.empty:
            return 0
        
        winning_df = df[df['pnl_rate'] > 0]
        losing_df = df[df['pnl_rate'] < 0]
        
        if len(losing_df) < self.min_trades_for_ratio:
            return 0
        
        avg_winning_pnl = winning_df['pnl_rate'].mean() if not winning_df.empty else 0
        avg_losing_pnl = losing_df['pnl_rate'].mean() if not losing_df.empty else 0
        
        if avg_losing_pnl < 0:
            return avg_winning_pnl / abs(avg_losing_pnl)
        else:
            return 0
    
    def _calculate_max_drawdown(self, cumulative_pnl_series: List[float]) -> float:
        """计算最大回撤
        
        Args:
            cumulative_pnl_series: 累计盈亏序列
            
        Returns:
            最大回撤
        """
        if not cumulative_pnl_series:
            return 0
        
        pnl_series = pd.Series(cumulative_pnl_series)
        running_max = pnl_series.expanding().max()
        drawdown = pnl_series - running_max
        
        return drawdown.min()
    
    def export_time_series_to_dataframe(self, time_series_results: Dict[str, List[TimeSeriesMetrics]]) -> Dict[str, pd.DataFrame]:
        """将时序分析结果导出为DataFrame
        
        Args:
            time_series_results: 时序分析结果字典
            
        Returns:
            时序分析DataFrame字典
        """
        dataframes = {}
        
        for analysis_type, metrics_list in time_series_results.items():
            if metrics_list:
                data = [metrics.to_dict() for metrics in metrics_list]
                dataframes[analysis_type] = pd.DataFrame(data)
            else:
                dataframes[analysis_type] = pd.DataFrame()
        
        return dataframes
    
    def get_performance_trends(self, time_series_results: Dict[str, List[TimeSeriesMetrics]]) -> Dict[str, Dict[str, Any]]:
        """获取绩效趋势分析
        
        Args:
            time_series_results: 时序分析结果字典
            
        Returns:
            趋势分析结果字典
        """
        trends = {}
        
        for analysis_type, metrics_list in time_series_results.items():
            if not metrics_list or len(metrics_list) < 2:
                continue
            
            # 计算趋势
            first_metrics = metrics_list[0]
            last_metrics = metrics_list[-1]
            
            # 盈亏趋势
            pnl_trend = last_metrics.cumulative_pnl_rate - first_metrics.cumulative_pnl_rate
            
            # 胜率趋势
            win_rate_trend = last_metrics.cumulative_win_rate - first_metrics.cumulative_win_rate
            
            # 盈亏比趋势
            ratio_trend = last_metrics.cumulative_profit_loss_ratio - first_metrics.cumulative_profit_loss_ratio
            
            # 计算波动性（标准差）
            pnl_values = [m.period_pnl_rate for m in metrics_list]
            pnl_volatility = np.std(pnl_values) if len(pnl_values) > 1 else 0
            
            trends[analysis_type] = {
                'total_periods': len(metrics_list),
                'pnl_trend': pnl_trend,
                'win_rate_trend': win_rate_trend,
                'profit_loss_ratio_trend': ratio_trend,
                'pnl_volatility': pnl_volatility,
                'final_cumulative_pnl': last_metrics.cumulative_pnl_rate,
                'final_win_rate': last_metrics.cumulative_win_rate,
                'final_profit_loss_ratio': last_metrics.cumulative_profit_loss_ratio,
                'max_drawdown': last_metrics.max_drawdown
            }
        
        return trends
    
    def identify_performance_patterns(self, daily_metrics: List[TimeSeriesMetrics]) -> Dict[str, Any]:
        """识别绩效模式
        
        Args:
            daily_metrics: 每日指标列表
            
        Returns:
            绩效模式分析结果
        """
        if not daily_metrics:
            return {}
        
        # 转换为DataFrame便于分析
        df = pd.DataFrame([m.to_dict() for m in daily_metrics])
        df['date'] = pd.to_datetime(df['date'])
        df['weekday'] = df['date'].dt.weekday
        df['month'] = df['date'].dt.month
        
        patterns = {}
        
        # 周内效应
        weekday_performance = df.groupby('weekday')['period_pnl_rate'].agg(['mean', 'std', 'count']).to_dict()
        patterns['weekday_effect'] = weekday_performance
        
        # 月度效应
        monthly_performance = df.groupby('month')['period_pnl_rate'].agg(['mean', 'std', 'count']).to_dict()
        patterns['monthly_effect'] = monthly_performance
        
        # 连续盈利/亏损天数
        df['is_profitable'] = df['period_pnl_rate'] > 0
        df['profit_streak'] = (df['is_profitable'] != df['is_profitable'].shift()).cumsum()
        
        streak_analysis = df.groupby(['profit_streak', 'is_profitable']).size().reset_index(name='days')
        
        max_winning_streak = streak_analysis[streak_analysis['is_profitable']]['days'].max() if not streak_analysis[streak_analysis['is_profitable']].empty else 0
        max_losing_streak = streak_analysis[~streak_analysis['is_profitable']]['days'].max() if not streak_analysis[~streak_analysis['is_profitable']].empty else 0
        
        patterns['streak_analysis'] = {
            'max_winning_streak': max_winning_streak,
            'max_losing_streak': max_losing_streak,
            'avg_winning_streak': streak_analysis[streak_analysis['is_profitable']]['days'].mean() if not streak_analysis[streak_analysis['is_profitable']].empty else 0,
            'avg_losing_streak': streak_analysis[~streak_analysis['is_profitable']]['days'].mean() if not streak_analysis[~streak_analysis['is_profitable']].empty else 0
        }
        
        return patterns
    
    def calculate_daily_signal_occupancy(self, processed_df: pd.DataFrame) -> pd.DataFrame:
        """计算每日信号占用数量
        
        基于实际持仓状态计算每日有多少个信号处于占用状态。
        信号占用定义：当某个策略存在未平仓的持仓时，该信号处于占用状态。
        
        Args:
            processed_df: 原始处理后的交易数据DataFrame
            
        Returns:
            包含每日信号占用数量的DataFrame，列包括：date, signal_occupancy
        """
        self.log_info("开始计算每日信号占用")
        
        if processed_df.empty:
            self.log_warning("输入数据为空，无法计算信号占用")
            return pd.DataFrame(columns=['date', 'signal_occupancy'])
        
        # 确保必要的列存在
        required_cols = ['strategy_name', 'datetime', 'volume', 'offset']
        missing_cols = [col for col in required_cols if col not in processed_df.columns]
        if missing_cols:
            self.log_error(f"缺少必要的列: {missing_cols}")
            return pd.DataFrame(columns=['date', 'signal_occupancy'])
        
        # 复制数据并确保datetime列是正确的类型
        df = processed_df.copy()
        df['datetime'] = pd.to_datetime(df['datetime'])
        df['date'] = df['datetime'].dt.date
        
        # 获取开平仓配置
        open_offset_types = self.config_manager.get('position_matching.open_offset_types', ['OPEN'])
        close_offset_types = self.config_manager.get('position_matching.close_offset_types', ['CLOSE'])
        
        # 按strategy_name分组，计算每个策略每日的持仓状态
        strategy_daily_positions = {}
        
        for strategy_name, group in df.groupby('strategy_name'):
            # 按时间排序
            group = group.sort_values('datetime').reset_index(drop=True)
            
            # 计算正确的持仓变化：开仓为正，平仓为负
            position_changes = []
            for _, row in group.iterrows():
                if row['offset'] in open_offset_types:
                    # 开仓操作：增加持仓
                    position_changes.append(abs(row['volume']))
                elif row['offset'] in close_offset_types:
                    # 平仓操作：减少持仓
                    position_changes.append(-abs(row['volume']))
                else:
                    # 未知操作类型，记录警告但继续处理
                    self.log_warning(f"未知的offset类型: {row['offset']}，策略: {strategy_name}")
                    position_changes.append(0)
            
            group['position_change'] = position_changes
            group['cumulative_position'] = group['position_change'].cumsum()
            
            # 按日期分组，计算每日的持仓状态
            daily_positions = {}
            for _, row in group.iterrows():
                date = row['date']
                if date not in daily_positions:
                    daily_positions[date] = 0
                # 使用当日最后的累计持仓量作为该日的持仓状态
                daily_positions[date] = row['cumulative_position']
            
            strategy_daily_positions[strategy_name] = daily_positions
        
        if not strategy_daily_positions:
            self.log_warning("没有找到有效的策略持仓数据")
            return pd.DataFrame(columns=['date', 'signal_occupancy'])
        
        # 获取所有日期范围
        all_dates = set()
        for positions in strategy_daily_positions.values():
            all_dates.update(positions.keys())
        
        if not all_dates:
            self.log_warning("没有找到有效的日期数据")
            return pd.DataFrame(columns=['date', 'signal_occupancy'])
        
        all_dates = sorted(all_dates)
        
        # 计算每日信号占用
        daily_occupancy = []
        
        for date in all_dates:
            occupancy_count = 0
            
            for strategy_name, positions in strategy_daily_positions.items():
                # 获取该策略在当前日期的持仓量
                if date in positions:
                    current_position = positions[date]
                else:
                    # 如果当天没有交易，使用前一天的持仓状态
                    current_position = 0
                    for prev_date in reversed(all_dates):
                        if prev_date < date and prev_date in positions:
                            current_position = positions[prev_date]
                            break
                
                # 如果持仓量不为零，则该信号处于占用状态
                if abs(current_position) > 1e-6:  # 使用小的阈值避免浮点数精度问题
                    occupancy_count += 1
            
            daily_occupancy.append({
                'date': date,
                'signal_occupancy': occupancy_count
            })
        
        result_df = pd.DataFrame(daily_occupancy)
        result_df = result_df.sort_values('date').reset_index(drop=True)
        
        self.log_info(f"信号占用计算完成，共 {len(result_df)} 天的数据")
        return result_df