"""配置管理模块

集中管理所有可配置参数，提供统一的配置访问接口。
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from .logger_config import LoggerMixin


class ConfigManager(LoggerMixin):
    """配置管理器
    
    负责加载、验证和提供配置参数访问接口。
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为 config/config.yaml
        """
        self.config_path = config_path or "config/config.yaml"
        self._config: Dict[str, Any] = {}
        self._load_config()
        self._validate_config()
    
    def _load_config(self) -> None:
        """加载配置文件
        
        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: 配置文件格式错误
        """
        config_file = Path(self.config_path)
        
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f) or {}
            self.log_info(f"成功加载配置文件: {self.config_path}")
        except yaml.YAMLError as e:
            self.log_error(f"配置文件格式错误: {e}")
            raise
        except Exception as e:
            self.log_error(f"加载配置文件失败: {e}")
            raise
    
    def _validate_config(self) -> None:
        """验证配置文件的完整性
        
        Raises:
            ValueError: 配置验证失败
        """
        required_sections = [
            'data_source',
            'strategy_signals',
            'signal_frequencies',
            'position_matching'
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in self._config:
                missing_sections.append(section)
        
        if missing_sections:
            error_msg = f"配置文件缺少必需的配置节: {', '.join(missing_sections)}"
            self.log_error(error_msg)
            raise ValueError(error_msg)
        
        self.log_info("配置文件验证通过")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_strategy_signal_mapping(self) -> Dict[str, List[str]]:
        """获取策略信号关键词映射
        
        Returns:
            策略信号映射字典
        """
        return self.get('strategy_signals', {})
    
    def get_signal_frequency_mapping(self) -> Dict[str, List[str]]:
        """获取信号频率关键词映射
        
        Returns:
            信号频率映射字典
        """
        return self.get('signal_frequencies', {})
    
    def get_offset_types(self) -> Dict[str, List[str]]:
        """获取开平仓类型映射
        
        Returns:
            开平仓类型映射字典
        """
        open_types = self.get('position_matching.open_offset_types', [])
        close_types = self.get('position_matching.close_offset_types', [])
        return {
            'open': open_types,
            'close': close_types
        }
    
    def get_direction_mapping(self) -> Dict[str, List[str]]:
        """获取交易方向映射
        
        Returns:
            交易方向映射字典
        """
        mapping = self.get('position_matching.direction_mapping', {})
        # 直接返回配置中的映射结构
        result = {
            'long': mapping.get('long', []),
            'short': mapping.get('short', [])
        }
        return result
    
    def get_direction_match_rules(self) -> Dict[str, str]:
        """获取开平仓方向匹配规则
        
        Returns:
            方向匹配规则字典
        """
        mapping = self.get('position_matching.direction_match_rules', {})
        # 转换为小写格式
        result = {}
        for key, value in mapping.items():
            result[key.lower()] = value.lower()
        
        # 如果配置为空，使用默认值
        if not result:
            result = {
                'long': 'short',
                'short': 'long'
            }
        return result
    
    def get_max_signal_duration(self) -> int:
        """获取信号最长持续时间（秒）
        
        Returns:
            最长持续时间（秒）
        """
        seconds = self.get('position_matching.max_signal_duration_seconds', 604800)
        return seconds
    
    def get_rolling_window_config(self) -> Dict[str, int]:
        """获取滚动窗口配置
        
        Returns:
            滚动窗口配置字典
        """
        return self.get('time_series_analysis.rolling_window', {})
    
    def get_required_columns(self) -> List[str]:
        """获取必需的数据列
        
        Returns:
            必需列名列表
        """
        return self.get('data_validation.required_columns', [])
    
    def get_numeric_columns(self) -> List[str]:
        """获取数值型列名
        
        Returns:
            数值型列名列表
        """
        return self.get('data_validation.numeric_columns', [])
    
    def get_datetime_columns(self) -> List[str]:
        """获取日期时间型列名
        
        Returns:
            日期时间型列名列表
        """
        return self.get('data_validation.datetime_columns', [])
    
    def get_input_file_path(self) -> str:
        """获取输入文件路径
        
        Returns:
            输入文件路径
        """
        return self.get('data_source.input_file', 'input/merged_trades.csv')
    
    def get_output_config(self) -> Dict[str, Any]:
        """获取输出配置
        
        Returns:
            输出配置字典
        """
        return self.get('output', {})
    
    def get_visualization_config(self) -> Dict[str, Any]:
        """获取可视化配置
        
        Returns:
            可视化配置字典
        """
        return self.get('visualization', {})
    
    def get_performance_config(self) -> Dict[str, Any]:
        """获取绩效指标配置
        
        Returns:
            绩效指标配置字典
        """
        return self.get('performance_metrics', {})
    
    def get_error_handling_config(self) -> Dict[str, Any]:
        """获取错误处理配置
        
        Returns:
            错误处理配置字典
        """
        return self.get('error_handling', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置
        
        Returns:
            日志配置字典
        """
        return self.get('logging', {})
    
    def get_data_source_config(self) -> Dict[str, Any]:
        """获取数据源配置
        
        Returns:
            数据源配置字典
        """
        return self.get('data_source', {})
    
    def is_fifo_matching_enabled(self) -> bool:
        """是否启用先进先出匹配
        
        Returns:
            是否启用FIFO匹配
        """
        method = self.get('position_matching.matching_method', 'FIFO')
        return method.upper() == 'FIFO'
    
    def is_partial_close_allowed(self) -> bool:
        """是否允许部分平仓
        
        Returns:
            是否允许部分平仓
        """
        return self.get('position_matching.allow_partial_matching', True)
    
    def get_commission_allocation_method(self) -> str:
        """获取手续费分摊方式
        
        Returns:
            手续费分摊方式
        """
        return self.get('position_matching.commission_allocation_method', 'proportional')
    
    def should_exclude_zero_pnl(self) -> bool:
        """胜率计算是否排除盈亏为零的信号
        
        Returns:
            是否排除零盈亏信号
        """
        return self.get('performance_metrics.exclude_zero_pnl', True)
    
    def get_min_trades_for_ratio(self) -> int:
        """获取计算盈亏比的最小交易数量
        
        Returns:
            最小交易数量
        """
        return self.get('performance_metrics.min_trades_for_ratio', 10)
    
    def get_groupby_fields(self) -> List[str]:
        """获取分组字段列表
        
        Returns:
            分组字段列表
        """
        return self.get('performance_metrics.groupby_fields', [])
    
    def reload_config(self) -> None:
        """重新加载配置文件"""
        self.log_info("重新加载配置文件")
        self._load_config()
        self._validate_config()
    
    def update_config(self, key: str, value: Any) -> None:
        """更新配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 新的配置值
        """
        keys = key.split('.')
        config = self._config
        
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
        self.log_info(f"更新配置: {key} = {value}")
    
    def save_config(self, output_path: Optional[str] = None) -> None:
        """保存配置到文件
        
        Args:
            output_path: 输出文件路径，默认为原配置文件路径
        """
        output_file = output_path or self.config_path
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(self._config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            self.log_info(f"配置已保存到: {output_file}")
        except Exception as e:
            self.log_error(f"保存配置文件失败: {e}")
            raise
    
    def __str__(self) -> str:
        """返回配置的字符串表示"""
        return f"ConfigManager(config_path={self.config_path})"
    
    def get_contracts_config(self) -> Dict[str, Dict[str, Any]]:
        """获取合约配置
        
        Returns:
            合约配置字典，格式为 {code: {name, industry, mult}}
        """
        return self.get('contracts', {})
    
    def get_contract_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """根据合约代码获取合约信息
        
        Args:
            symbol: 合约代码（如 ag2501）
            
        Returns:
            合约信息字典，包含 name, industry, mult，如果未找到返回 None
        """
        # 提取合约代码的英文字母部分
        contract_code = self._extract_contract_code(symbol)
        contracts = self.get_contracts_config()
        return contracts.get(contract_code.upper())
    
    def _extract_contract_code(self, symbol: str) -> str:
        """从合约代码中提取英文字母部分
        
        Args:
            symbol: 完整的合约代码（如 ag2501, AP505）
            
        Returns:
            英文字母部分（如 ag, AP）
        """
        import re
        # 提取开头的英文字母部分
        match = re.match(r'^([A-Za-z]+)', symbol)
        return match.group(1) if match else symbol
    
    def get_contract_multiplier(self, symbol: str) -> int:
        """获取合约乘数
        
        Args:
            symbol: 合约代码
            
        Returns:
            合约乘数，如果未找到返回1
        """
        contract_info = self.get_contract_info(symbol)
        return contract_info.get('mult', 1) if contract_info else 1
    
    def get_contract_industry(self, symbol: str) -> str:
        """获取合约所属行业
        
        Args:
            symbol: 合约代码
            
        Returns:
            行业名称，如果未找到返回"未知"
        """
        contract_info = self.get_contract_info(symbol)
        return contract_info.get('industry', '未知') if contract_info else '未知'
    
    def get_contract_name(self, symbol: str) -> str:
        """获取合约名称
        
        Args:
            symbol: 合约代码
            
        Returns:
            合约名称，如果未找到返回原始代码
        """
        contract_info = self.get_contract_info(symbol)
        return contract_info.get('name', symbol) if contract_info else symbol
    
    def __repr__(self) -> str:
        """返回配置的详细字符串表示"""
        return f"ConfigManager(config_path={self.config_path}, sections={list(self._config.keys())})"