"""报告与可视化模块

负责生成详细的分析报告和可视化图表。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import json
from .logger_config import LoggerMixin
from .config_manager import ConfigManager
from .position_matcher import CompletedSignal, UncompletedSignal
from .performance_calculator import PerformanceMetrics
from .time_series_analyzer import TimeSeriesMetrics


class ReportGenerator(LoggerMixin):
    """报告生成器
    
    负责生成分析报告和可视化图表。
    """
    
    def __init__(self, config_manager: ConfigManager):
        """初始化报告生成器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.output_config = config_manager.get_output_config()
        self.visualization_config = config_manager.get_visualization_config()
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置seaborn样式
        sns.set_style("whitegrid")
        sns.set_palette("husl")
    
    def generate_comprehensive_report(self, 
                                    completed_signals: List[CompletedSignal],
                                    uncompleted_signals: List[UncompletedSignal],
                                    unmatched_trades: List[Dict[str, Any]],
                                    performance_metrics: Dict[str, PerformanceMetrics],
                                    time_series_results: Dict[str, List[TimeSeriesMetrics]],
                                    output_dir: Optional[str] = None) -> Dict[str, str]:
        """生成综合分析报告
        
        Args:
            completed_signals: 已完成信号列表
            uncompleted_signals: 未完成信号列表
            unmatched_trades: 未匹配交易列表
            performance_metrics: 绩效指标字典
            time_series_results: 时序分析结果
            output_dir: 输出目录
            
        Returns:
            生成的文件路径字典
        """
        self.log_info("开始生成综合分析报告")
        
        if output_dir is None:
            output_dir = self.output_config.get('directory', 'output')
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        generated_files = {}
        
        try:
            # 1. 导出数据文件
            data_files = self._export_data_files(completed_signals, uncompleted_signals, unmatched_trades,
                                                performance_metrics, time_series_results, output_path)
            generated_files.update(data_files)
            
            # 2. 生成可视化图表
            chart_files = self._generate_charts(completed_signals, performance_metrics, 
                                               time_series_results, output_path)
            generated_files.update(chart_files)
            
            # 3. 生成HTML报告
            html_file = self._generate_html_report(completed_signals, uncompleted_signals,
                                                  performance_metrics, time_series_results, output_path)
            generated_files['html_report'] = html_file
            
            # 4. 生成摘要报告
            summary_file = self._generate_summary_report(completed_signals, uncompleted_signals,
                                                        performance_metrics, time_series_results, output_path)
            generated_files['summary_report'] = summary_file
            
            self.log_info(f"综合分析报告生成完成，共生成 {len(generated_files)} 个文件")
            
        except Exception as e:
            self.log_error(f"生成综合分析报告时出错: {e}")
            raise
        
        return generated_files
    
    def _export_data_files(self, completed_signals: List[CompletedSignal],
                          uncompleted_signals: List[UncompletedSignal],
                          unmatched_trades: List[Dict[str, Any]],
                          performance_metrics: Dict[str, PerformanceMetrics],
                          time_series_results: Dict[str, List[TimeSeriesMetrics]],
                          output_path: Path) -> Dict[str, str]:
        """导出数据文件
        
        Args:
            completed_signals: 已完成信号列表
            uncompleted_signals: 未完成信号列表
            unmatched_trades: 未匹配交易列表
            performance_metrics: 绩效指标字典
            time_series_results: 时序分析结果
            output_path: 输出路径
            
        Returns:
            导出的文件路径字典
        """
        files = {}
        
        # 导出已完成信号
        if completed_signals:
            completed_df = pd.DataFrame([signal.to_dict() for signal in completed_signals])
            completed_file = output_path / 'completed_signals.csv'
            completed_df.to_csv(completed_file, index=False, encoding='utf-8-sig')
            files['completed_signals'] = str(completed_file)
            self.log_info(f"已完成信号导出到: {completed_file}")
        
        # 导出未完成信号
        if uncompleted_signals:
            uncompleted_df = pd.DataFrame([signal.to_dict() for signal in uncompleted_signals])
            uncompleted_file = output_path / 'uncompleted_signals.csv'
            uncompleted_df.to_csv(uncompleted_file, index=False, encoding='utf-8-sig')
            files['uncompleted_signals'] = str(uncompleted_file)
            self.log_info(f"未完成信号导出到: {uncompleted_file}")
        
        # 导出未匹配交易
        if unmatched_trades:
            unmatched_df = pd.DataFrame(unmatched_trades)
            unmatched_file = output_path / 'unmatched_trades.csv'
            unmatched_df.to_csv(unmatched_file, index=False, encoding='utf-8-sig')
            files['unmatched_trades'] = str(unmatched_file)
            self.log_info(f"未匹配交易导出到: {unmatched_file}")
        else:
            self.log_info("没有未匹配的交易数据")
        
        # 导出绩效指标
        if performance_metrics:
            performance_data = []
            for group_key, metrics in performance_metrics.items():
                metrics_dict = metrics.to_dict()
                metrics_dict['group_key'] = group_key
                performance_data.append(metrics_dict)
            
            performance_df = pd.DataFrame(performance_data)
            performance_file = output_path / 'performance_metrics.csv'
            performance_df.to_csv(performance_file, index=False, encoding='utf-8-sig')
            files['performance_metrics'] = str(performance_file)
            self.log_info(f"绩效指标导出到: {performance_file}")
        
        # 导出时序分析结果
        for analysis_type, metrics_list in time_series_results.items():
            if metrics_list:
                time_series_data = [metrics.to_dict() for metrics in metrics_list]
                time_series_df = pd.DataFrame(time_series_data)
                time_series_file = output_path / f'time_series_{analysis_type}.csv'
                time_series_df.to_csv(time_series_file, index=False, encoding='utf-8-sig')
                files[f'time_series_{analysis_type}'] = str(time_series_file)
                self.log_info(f"时序分析结果({analysis_type})导出到: {time_series_file}")
        
        return files
    
    def export_multidimensional_pnl_to_excel(self, 
                                            multidimensional_pnl_summary: Dict[str, pd.DataFrame],
                                            output_path: Path) -> str:
        """将多维度PnL汇总导出为Excel文件
        
        根据文档要求，每个唯一的(strategy_signal, signal_freq)组合对应一个独立的工作表，
        展示该"开仓信号"在不同symbol_category下的绩效。
        
        Args:
            multidimensional_pnl_summary: 多维度PnL汇总字典
            output_path: 输出路径
            
        Returns:
            Excel文件路径
        """
        if not multidimensional_pnl_summary:
            self.log_warning("多维度PnL汇总数据为空，跳过Excel导出")
            return ""
        
        excel_file = output_path / 'pnl_multidimensional_analysis.xlsx'
        
        try:
            with pd.ExcelWriter(str(excel_file), engine='openpyxl') as writer:
                # 为每个开仓信号组合创建一个工作表
                for signal_key, summary_df in multidimensional_pnl_summary.items():
                    # 确保工作表名称符合Excel要求（最大31字符，不包含特殊字符）
                    sheet_name = self._sanitize_sheet_name(signal_key)
                    
                    # 写入数据到工作表
                    summary_df.to_excel(writer, sheet_name=sheet_name, index=False)
                    
                    # 获取工作表对象进行格式化
                    worksheet = writer.sheets[sheet_name]
                    
                    # 设置列宽
                    self._format_excel_worksheet(worksheet, summary_df)
                    
                    self.log_info(f"已创建工作表: {sheet_name}，包含 {len(summary_df)} 行数据")
                
                # 创建汇总工作表
                self._create_summary_sheet(writer, multidimensional_pnl_summary)
            
            self.log_info(f"多维度PnL分析Excel文件已生成: {excel_file}")
            return str(excel_file)
            
        except Exception as e:
            self.log_error(f"导出多维度PnL分析Excel文件时出错: {e}")
            raise
    
    def _sanitize_sheet_name(self, name: str) -> str:
        """清理工作表名称，确保符合Excel要求
        
        Args:
            name: 原始名称
            
        Returns:
            清理后的名称
        """
        # 替换不允许的字符
        invalid_chars = ['[', ']', '*', '?', ':', '/', '\\']
        sanitized = name
        for char in invalid_chars:
            sanitized = sanitized.replace(char, '_')
        
        # 限制长度为31字符
        if len(sanitized) > 31:
            sanitized = sanitized[:31]
        
        return sanitized
    
    def _format_excel_worksheet(self, worksheet, df: pd.DataFrame):
        """格式化Excel工作表
        
        Args:
            worksheet: openpyxl工作表对象
            df: 数据DataFrame
        """
        try:
            from openpyxl.styles import Font, PatternFill, Alignment
            from openpyxl.utils import get_column_letter
            
            # 设置标题行样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            # 应用标题行样式
            for col_num in range(1, len(df.columns) + 1):
                cell = worksheet.cell(row=1, column=col_num)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            
            # 设置列宽
            for col_num, column in enumerate(df.columns, 1):
                column_letter = get_column_letter(col_num)
                # 根据列内容调整宽度
                if 'symbol_category' in column:
                    worksheet.column_dimensions[column_letter].width = 15
                elif '信号数量' in column:
                    worksheet.column_dimensions[column_letter].width = 12
                elif any(keyword in column for keyword in ['pnl_rate', '胜率', '盈亏比', '夏普比率']):
                    worksheet.column_dimensions[column_letter].width = 18
                else:
                    worksheet.column_dimensions[column_letter].width = 16
            
            # 设置数据行对齐方式
            for row_num in range(2, len(df) + 2):
                for col_num in range(1, len(df.columns) + 1):
                    cell = worksheet.cell(row=row_num, column=col_num)
                    if col_num == 1:  # symbol_category列左对齐
                        cell.alignment = Alignment(horizontal="left", vertical="center")
                    else:  # 数值列右对齐
                        cell.alignment = Alignment(horizontal="right", vertical="center")
            
        except ImportError:
            self.log_warning("openpyxl未安装，跳过Excel格式化")
        except Exception as e:
            self.log_warning(f"Excel格式化时出错: {e}")
    
    def _create_summary_sheet(self, writer, multidimensional_pnl_summary: Dict[str, pd.DataFrame]):
        """创建汇总工作表
        
        Args:
            writer: Excel写入器
            multidimensional_pnl_summary: 多维度PnL汇总字典
        """
        try:
            # 创建汇总数据
            summary_data = []
            
            for signal_key, summary_df in multidimensional_pnl_summary.items():
                # 跳过行业分析工作表，只处理常规分析工作表
                if '行业分析' in signal_key:
                    continue
                    
                if not summary_df.empty:
                    # 基于开仓信号重新计算整体统计，而不是简单求均值
                    total_signals = summary_df['信号数量'].sum()
                    
                    # 重新计算胜率：需要基于原始数据重新计算
                    # 胜率 = 总盈利信号数 / 总有效信号数
                    total_winning_signals = 0
                    total_losing_signals = 0
                    total_profit_pnl = 0
                    total_loss_pnl = 0
                    total_pnl_rate_sum = 0
                    total_pnl_rate_squared_sum = 0
                    
                    # 用于计算加权平均夏普比率
                    weighted_sharpe_sum = 0
                    
                    for _, row in summary_df.iterrows():
                        signals_count = row['信号数量']
                        win_rate = row['胜率']
                        
                        # 检查字段是否存在，兼容不同的数据结构
                        if '平均盈利pnl_rate' in row and '平均亏损pnl_rate' in row:
                            avg_profit_pnl = row['平均盈利pnl_rate']
                            avg_loss_pnl = row['平均亏损pnl_rate']
                            avg_pnl_rate = (avg_profit_pnl * win_rate + avg_loss_pnl * (1 - win_rate))
                        else:
                            # 如果没有分别的盈利/亏损字段，使用平均盈亏率
                            avg_pnl_rate = row.get('平均盈亏率', 0)
                            avg_profit_pnl = avg_pnl_rate if avg_pnl_rate > 0 else 0
                            avg_loss_pnl = avg_pnl_rate if avg_pnl_rate < 0 else 0
                        
                        pnl_std = row['盈亏率标准差']
                        
                        # 检查夏普比率字段名称
                        if '盈亏率夏普比率' in row:
                            sharpe_ratio = row['盈亏率夏普比率']
                        elif '夏普比率' in row:
                            sharpe_ratio = row['夏普比率']
                        else:
                            sharpe_ratio = 0
                        
                        # 估算盈利和亏损信号数量
                        winning_signals = int(signals_count * win_rate)
                        losing_signals = signals_count - winning_signals
                        
                        total_winning_signals += winning_signals
                        total_losing_signals += losing_signals
                        
                        # 累计盈亏
                        if avg_profit_pnl > 0:
                            total_profit_pnl += avg_profit_pnl * winning_signals
                        if avg_loss_pnl < 0:
                            total_loss_pnl += avg_loss_pnl * losing_signals
                        
                        # 累计用于计算整体平均值和标准差
                        total_pnl_rate_sum += avg_pnl_rate * signals_count
                        total_pnl_rate_squared_sum += (pnl_std ** 2 + avg_pnl_rate ** 2) * signals_count
                        
                        # 累计加权夏普比率（如果夏普比率有效）
                        if not (np.isnan(sharpe_ratio) or np.isinf(sharpe_ratio)):
                            weighted_sharpe_sum += sharpe_ratio * signals_count
                    
                    # 计算整体胜率
                    effective_signals = total_winning_signals + total_losing_signals
                    overall_win_rate = total_winning_signals / effective_signals if effective_signals > 0 else 0
                    
                    # 计算整体盈亏比
                    if total_losing_signals > 0 and total_profit_pnl > 0 and total_loss_pnl < 0:
                        avg_profit = total_profit_pnl / total_winning_signals if total_winning_signals > 0 else 0
                        avg_loss = abs(total_loss_pnl / total_losing_signals) if total_losing_signals > 0 else 0
                        overall_profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else 0
                    else:
                        overall_profit_loss_ratio = 0
                    
                    # 计算整体夏普比率 - 使用加权平均方法
                    if total_signals > 0:
                        # 方法1：加权平均夏普比率
                        overall_sharpe_ratio = weighted_sharpe_sum / total_signals
                        
                        # 方法2：如果加权平均失败，尝试重新计算
                        if np.isnan(overall_sharpe_ratio) or np.isinf(overall_sharpe_ratio) or overall_sharpe_ratio == 0:
                            overall_avg_pnl = total_pnl_rate_sum / total_signals
                            overall_variance = (total_pnl_rate_squared_sum / total_signals) - (overall_avg_pnl ** 2)
                            overall_std = np.sqrt(max(0, overall_variance))
                            overall_sharpe_ratio = overall_avg_pnl / overall_std if overall_std > 0 else 0
                        
                        # 最终检查：如果计算出的夏普比率是NaN或无穷大，设为0
                        if np.isnan(overall_sharpe_ratio) or np.isinf(overall_sharpe_ratio):
                            overall_sharpe_ratio = 0
                    else:
                        overall_sharpe_ratio = 0
                    
                    summary_data.append({
                        '开仓信号': signal_key,
                        '总信号数量': total_signals,
                        '整体胜率': round(overall_win_rate, 4),
                        '整体盈亏比': round(overall_profit_loss_ratio, 4),
                        '整体夏普比率': round(overall_sharpe_ratio, 4),
                        '合约类别数量': len(summary_df),
                        '盈利信号数': total_winning_signals,
                        '亏损信号数': total_losing_signals
                    })
            
            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                # 按总信号数量降序排序
                summary_df = summary_df.sort_values('总信号数量', ascending=False)
                
                # 写入汇总工作表
                summary_df.to_excel(writer, sheet_name='汇总', index=False)
                
                # 格式化汇总工作表
                if '汇总' in writer.sheets:
                    self._format_excel_worksheet(writer.sheets['汇总'], summary_df)
                
                self.log_info(f"已创建汇总工作表，包含 {len(summary_df)} 个开仓信号的汇总")
            
        except Exception as e:
            self.log_warning(f"创建汇总工作表时出错: {e}")
    
    def _generate_charts(self, completed_signals: List[CompletedSignal],
                        performance_metrics: Dict[str, PerformanceMetrics],
                        time_series_results: Dict[str, List[TimeSeriesMetrics]],
                        output_path: Path) -> Dict[str, str]:
        """生成可视化图表
        
        Args:
            completed_signals: 已完成信号列表
            performance_metrics: 绩效指标字典
            time_series_results: 时序分析结果
            output_path: 输出路径
            
        Returns:
            生成的图表文件路径字典
        """
        chart_files = {}
        chart_dir = output_path / 'charts'
        chart_dir.mkdir(exist_ok=True)
        
        # 保留的图表功能：胜率趋势图
        if 'daily' in time_series_results and time_series_results['daily']:
            # 胜率趋势图
            win_rate_chart = self._create_win_rate_trend_chart(time_series_results['daily'])
            win_rate_file = chart_dir / 'win_rate_trend.html'
            win_rate_chart.write_html(str(win_rate_file))
            chart_files['win_rate_trend'] = str(win_rate_file)
        
        # 行业盈亏分析图表（修复版）
        if completed_signals:
            industry_chart = self._create_industry_analysis_chart(completed_signals)
            industry_file = chart_dir / 'industry_analysis.html'
            industry_chart.write_html(str(industry_file))
            chart_files['industry_analysis'] = str(industry_file)
        
        # 每日信号占用图表（改进版）
        if completed_signals:
            daily_occupancy_file = self.create_daily_signal_occupancy_chart(completed_signals, output_path)
            if daily_occupancy_file:
                chart_files['daily_signal_occupancy'] = daily_occupancy_file
        
        # 止盈止损分布图表（新增）
        if completed_signals:
            profit_loss_file = self.create_profit_loss_distribution_charts(completed_signals, output_path)
            if profit_loss_file:
                chart_files['profit_loss_distribution'] = profit_loss_file
        
        self.log_info(f"生成了 {len(chart_files)} 个图表文件")
        return chart_files
    

    

    
    def _create_win_rate_trend_chart(self, daily_metrics: List[TimeSeriesMetrics]) -> go.Figure:
        """创建胜率趋势图
        
        Args:
            daily_metrics: 每日指标列表
            
        Returns:
            Plotly图表对象
        """
        dates = [metrics.date for metrics in daily_metrics]
        cumulative_win_rates = [metrics.cumulative_win_rate * 100 for metrics in daily_metrics]
        
        # 计算滚动胜率（如果有的话）
        rolling_win_rates = []
        window_size = 30  # 30天滚动窗口
        
        for i in range(len(daily_metrics)):
            start_idx = max(0, i - window_size + 1)
            window_metrics = daily_metrics[start_idx:i+1]
            
            total_winning = sum(1 for m in window_metrics if m.period_pnl_rate > 0)
            total_signals = len(window_metrics)
            
            rolling_win_rate = (total_winning / total_signals * 100) if total_signals > 0 else 0
            rolling_win_rates.append(rolling_win_rate)
        
        fig = go.Figure()
        
        # 累计胜率
        fig.add_trace(
            go.Scatter(x=dates, y=cumulative_win_rates, mode='lines', name='累计胜率',
                      line=dict(color='blue', width=2))
        )
        
        # 滚动胜率
        fig.add_trace(
            go.Scatter(x=dates, y=rolling_win_rates, mode='lines', name=f'{window_size}天滚动胜率',
                      line=dict(color='orange', width=1, dash='dash'))
        )
        
        # 添加50%基准线
        fig.add_hline(y=50, line_dash="dot", line_color="red", 
                     annotation_text="50%基准线")
        
        fig.update_layout(
            title="胜率趋势分析",
            xaxis_title="日期",
            yaxis_title="胜率 (%)",
            height=400
        )
        
        return fig
    

    

    

    
    def _create_industry_analysis_chart(self, completed_signals: List[CompletedSignal]) -> go.Figure:
        """创建行业盈亏分析图表
        
        Args:
            completed_signals: 已完成信号列表
            
        Returns:
            Plotly图表对象
        """
        # 转换为DataFrame
        df = pd.DataFrame([signal.to_dict() for signal in completed_signals])
        
        if 'industry' not in df.columns or 'signal_pnl_amount' not in df.columns:
            # 如果没有相关字段，返回空图表
            fig = go.Figure()
            fig.add_annotation(text="缺少行业或盈亏金额数据", 
                             xref="paper", yref="paper", x=0.5, y=0.5, showarrow=False)
            return fig
        
        # 按行业分组统计
        industry_stats = df.groupby('industry').agg({
            'signal_pnl_amount': ['sum', 'mean', 'count'],
            'pnl_rate': 'mean',
            'position_market_value': 'mean'  # 修改为平均市值
        }).round(4)
        
        # 重命名列
        industry_stats.columns = ['总盈亏金额', '平均盈亏金额', '信号数量', '平均盈亏率', '平均持仓市值']
        industry_stats = industry_stats.reset_index()
        
        # 计算胜率
        win_rates = df.groupby('industry')['pnl_rate'].apply(lambda x: (x > 0).mean()).reset_index()
        win_rates.columns = ['industry', '胜率']
        industry_stats = industry_stats.merge(win_rates, on='industry')
        
        # 按总盈亏金额排序
        industry_stats = industry_stats.sort_values('总盈亏金额', ascending=True)
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('各行业总盈亏金额', '各行业平均盈亏率', '各行业胜率', '各行业平均持仓市值'),
            specs=[[{"type": "bar"}, {"type": "bar"}], 
                   [{"type": "bar"}, {"type": "bar"}]]
        )
        
        # 总盈亏金额
        colors = ['red' if x < 0 else 'green' for x in industry_stats['总盈亏金额']]
        fig.add_trace(
            go.Bar(x=industry_stats['总盈亏金额'], y=industry_stats['industry'], 
                   orientation='h', name="总盈亏金额", marker_color=colors),
            row=1, col=1
        )
        
        # 平均盈亏率
        colors = ['red' if x < 0 else 'green' for x in industry_stats['平均盈亏率']]
        fig.add_trace(
            go.Bar(x=industry_stats['平均盈亏率'], y=industry_stats['industry'], 
                   orientation='h', name="平均盈亏率", marker_color=colors),
            row=1, col=2
        )
        
        # 胜率
        fig.add_trace(
            go.Bar(x=industry_stats['胜率'], y=industry_stats['industry'], 
                   orientation='h', name="胜率", marker_color='blue'),
            row=2, col=1
        )
        
        # 平均持仓市值
        fig.add_trace(
            go.Bar(x=industry_stats['平均持仓市值'], y=industry_stats['industry'], 
                   orientation='h', name="平均持仓市值", marker_color='orange'),
            row=2, col=2
        )
        
        fig.update_layout(
            title_text="各行业盈亏分析（基于新增字段）",
            height=800,
            showlegend=False
        )
        
        # 更新x轴标题
        fig.update_xaxes(title_text="盈亏金额", row=1, col=1)
        fig.update_xaxes(title_text="盈亏率", row=1, col=2)
        fig.update_xaxes(title_text="胜率", row=2, col=1)
        fig.update_xaxes(title_text="平均持仓市值", row=2, col=2)
        
        return fig
    

    
    def _generate_html_report(self, completed_signals: List[CompletedSignal],
                             uncompleted_signals: List[UncompletedSignal],
                             performance_metrics: Dict[str, PerformanceMetrics],
                             time_series_results: Dict[str, List[TimeSeriesMetrics]],
                             output_path: Path) -> str:
        """生成HTML报告
        
        Args:
            completed_signals: 已完成信号列表
            uncompleted_signals: 未完成信号列表
            performance_metrics: 绩效指标字典
            time_series_results: 时序分析结果
            output_path: 输出路径
            
        Returns:
            HTML文件路径
        """
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>趋势策略交易分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }}
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .summary-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }}
        .summary-card .value {{
            font-size: 2em;
            font-weight: bold;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #3498db;
            color: white;
        }}
        tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        .positive {{
            color: #27ae60;
            font-weight: bold;
        }}
        .negative {{
            color: #e74c3c;
            font-weight: bold;
        }}
        .chart-container {{
            margin: 20px 0;
            text-align: center;
        }}
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #7f8c8d;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>趋势策略交易分析报告</h1>
        
        <div class="summary-grid">
            <div class="summary-card">
                <h3>已完成信号</h3>
                <div class="value">{len(completed_signals)}</div>
            </div>
            <div class="summary-card">
                <h3>未完成信号</h3>
                <div class="value">{len(uncompleted_signals)}</div>
            </div>
            <div class="summary-card">
                <h3>总盈亏率</h3>
                <div class="value">{sum(signal.pnl_rate for signal in completed_signals):.4f}</div>
            </div>
            <div class="summary-card">
                <h3>分析时间</h3>
                <div class="value">{datetime.now().strftime('%Y-%m-%d')}</div>
            </div>
        </div>
        
        <h2>绩效指标汇总</h2>
        <table>
            <thead>
                <tr>
                    <th>分组</th>
                    <th>信号数量</th>
                    <th>胜率 (%)</th>
                    <th>盈亏比</th>
                    <th>总盈亏率</th>
                    <th>平均盈利</th>
                    <th>平均亏损</th>
                </tr>
            </thead>
            <tbody>
"""
        
        # 添加绩效指标表格
        for group_key, metrics in performance_metrics.items():
            pnl_class = 'positive' if metrics.total_pnl_rate > 0 else 'negative'
            html_content += f"""
                <tr>
                    <td>{group_key}</td>
                    <td>{metrics.total_signals}</td>
                    <td>{metrics.win_rate * 100:.2f}%</td>
                    <td>{metrics.profit_loss_ratio:.2f}</td>
                    <td class="{pnl_class}">{metrics.total_pnl_rate:.4f}</td>
                    <td class="positive">{metrics.average_winning_pnl:.2f}</td>
                    <td class="negative">{metrics.average_losing_pnl:.2f}</td>
                </tr>
"""
        
        html_content += """
            </tbody>
        </table>
        
        <h2>图表分析</h2>
        <div class="chart-container">
            <p>详细的可视化图表请查看 charts 文件夹中的HTML文件。</p>
            <ul style="text-align: left; display: inline-block;">
                <li>胜率趋势图: charts/win_rate_trend.html</li>
                <li>每日信号占用图: charts/daily_signal_occupancy.html</li>
                <li>行业盈亏分析图: charts/industry_analysis.html</li>
            </ul>
        </div>
        
        <div class="footer">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>趋势策略交易分析系统 v1.0</p>
        </div>
    </div>
</body>
</html>
"""
        
        html_file = output_path / 'analysis_report.html'
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.log_info(f"HTML报告生成完成: {html_file}")
        return str(html_file)
    
    def _generate_summary_report(self, completed_signals: List[CompletedSignal],
                                uncompleted_signals: List[UncompletedSignal],
                                performance_metrics: Dict[str, PerformanceMetrics],
                                time_series_results: Dict[str, List[TimeSeriesMetrics]],
                                output_path: Path) -> str:
        """生成摘要报告
        
        Args:
            completed_signals: 已完成信号列表
            uncompleted_signals: 未完成信号列表
            performance_metrics: 绩效指标字典
            time_series_results: 时序分析结果
            output_path: 输出路径
            
        Returns:
            摘要报告文件路径
        """
        summary_content = f"""
# 趋势策略交易分析摘要报告

## 基本统计
- 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 已完成信号数量: {len(completed_signals)}
- 未完成信号数量: {len(uncompleted_signals)}
- 总盈亏率: {sum(signal.pnl_rate for signal in completed_signals):.4f}

## 主要发现
"""
        
        if performance_metrics:
            # 找出最佳和最差表现的分组
            best_group = max(performance_metrics.items(), key=lambda x: x[1].total_pnl_rate)
            worst_group = min(performance_metrics.items(), key=lambda x: x[1].total_pnl_rate)
            
            summary_content += f"""
### 绩效表现
- 最佳表现分组: {best_group[0]} (总盈亏率: {best_group[1].total_pnl_rate:.4f})
- 最差表现分组: {worst_group[0]} (总盈亏率: {worst_group[1].total_pnl_rate:.4f})

### 胜率分析
"""
            
            for group_key, metrics in performance_metrics.items():
                summary_content += f"- {group_key}: 胜率 {metrics.win_rate*100:.2f}%, 盈亏比 {metrics.profit_loss_ratio:.2f}\n"
        
        if 'daily' in time_series_results and time_series_results['daily']:
            daily_metrics = time_series_results['daily']
            final_metrics = daily_metrics[-1]
            
            summary_content += f"""

### 时序分析
- 分析期间: {daily_metrics[0].date.strftime('%Y-%m-%d')} 至 {final_metrics.date.strftime('%Y-%m-%d')}
- 最终累计盈亏: {final_metrics.cumulative_pnl_rate:.2f}
- 最终累计胜率: {final_metrics.cumulative_win_rate*100:.2f}%
- 最大回撤: {final_metrics.max_drawdown:.2f}
"""
        
        summary_content += """

## 建议
1. 关注表现最佳的策略分组，分析其成功因素
2. 对表现较差的分组进行优化或调整
3. 监控最大回撤，控制风险
4. 定期回顾和更新策略参数

## 文件说明
- completed_signals.csv: 已完成信号详细数据
- uncompleted_signals.csv: 未完成信号详细数据
- performance_metrics.csv: 绩效指标汇总
- charts/: 可视化图表文件夹
- analysis_report.html: 详细HTML报告
"""
        
        summary_file = output_path / 'summary_report.md'
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        self.log_info(f"摘要报告生成完成: {summary_file}")
        return str(summary_file)
    
    def create_custom_chart(self, data: pd.DataFrame, chart_type: str, 
                           title: str, output_path: str, **kwargs) -> str:
        """创建自定义图表
        
        Args:
            data: 数据DataFrame
            chart_type: 图表类型 ('line', 'bar', 'scatter', 'histogram')
            title: 图表标题
            output_path: 输出路径
            **kwargs: 其他图表参数
            
        Returns:
            图表文件路径
        """
        fig = go.Figure()
        
        if chart_type == 'line':
            x_col = kwargs.get('x_column', data.columns[0])
            y_col = kwargs.get('y_column', data.columns[1])
            fig.add_trace(go.Scatter(x=data[x_col], y=data[y_col], mode='lines', name=y_col))
        
        elif chart_type == 'bar':
            x_col = kwargs.get('x_column', data.columns[0])
            y_col = kwargs.get('y_column', data.columns[1])
            fig.add_trace(go.Bar(x=data[x_col], y=data[y_col], name=y_col))
        
        elif chart_type == 'scatter':
            x_col = kwargs.get('x_column', data.columns[0])
            y_col = kwargs.get('y_column', data.columns[1])
            fig.add_trace(go.Scatter(x=data[x_col], y=data[y_col], mode='markers', name=f'{x_col} vs {y_col}'))
        
        elif chart_type == 'histogram':
            col = kwargs.get('column', data.columns[0])
            fig.add_trace(go.Histogram(x=data[col], name=col))
        
        fig.update_layout(
            title=title,
            xaxis_title=kwargs.get('x_title', ''),
            yaxis_title=kwargs.get('y_title', ''),
            height=kwargs.get('height', 400)
        )
        
        fig.write_html(output_path)
        self.log_info(f"自定义图表生成完成: {output_path}")
        
        return output_path
    
    def export_pnl_rate_summary(self, pnl_rate_summary_df: pd.DataFrame, output_path: Path) -> str:
        """导出pnl_rate汇总表格
        
        Args:
            pnl_rate_summary_df: pnl_rate汇总DataFrame
            output_path: 输出路径
            
        Returns:
            导出的文件路径
        """
        if pnl_rate_summary_df.empty:
            self.log_warning("pnl_rate汇总数据为空，跳过导出")
            return ""
        
        summary_file = output_path / 'pnl_rate_summary.csv'
        pnl_rate_summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
        
        self.log_info(f"pnl_rate汇总表格导出到: {summary_file}")
        return str(summary_file)
    
    def create_daily_signal_occupancy_chart(self, completed_signals: List[CompletedSignal], output_path: Path) -> str:
        """创建每日信号占用折线图（改进版）
        
        计算每一天存在持仓的信号数量，当天平仓的信号不计入
        加入不同策略信号类型的每日持仓数量变动情况
        
        Args:
            completed_signals: 已完成信号列表
            output_path: 输出路径
            
        Returns:
            图表文件路径
        """
        if not completed_signals:
            self.log_warning("已完成信号数据为空，跳过图表生成")
            return ""
        
        # 创建图表目录
        chart_dir = output_path / 'charts'
        chart_dir.mkdir(exist_ok=True)
        
        # 转换为DataFrame
        df = pd.DataFrame([signal.to_dict() for signal in completed_signals])
        
        # 添加策略信号类型和频率字段
        df['strategy_signal'] = df['strategy_name'].apply(self._extract_strategy_signal)
        df['signal_freq'] = df['strategy_name'].apply(self._extract_signal_frequency)
        df['strategy_signal_type'] = df['strategy_signal'] + '_' + df['signal_freq']
        
        # 获取日期范围
        all_dates = []
        for _, row in df.iterrows():
            start_date = pd.to_datetime(row['first_open_datetime']).date()
            end_date = pd.to_datetime(row['close_datetime']).date()
            # 持仓期间不包括平仓当天
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')[:-1]
            all_dates.extend(date_range.date)
        
        if not all_dates:
            self.log_warning("没有有效的持仓日期数据")
            return ""
        
        # 创建完整的日期范围
        min_date = min(all_dates)
        max_date = max(all_dates)
        full_date_range = pd.date_range(start=min_date, end=max_date, freq='D')
        
        # 计算每日总信号占用数量
        daily_occupancy = []
        strategy_daily_occupancy = {}
        
        for date in full_date_range:
            date_obj = date.date()
            total_count = 0
            strategy_counts = {}
            
            for _, row in df.iterrows():
                start_date = pd.to_datetime(row['first_open_datetime']).date()
                end_date = pd.to_datetime(row['close_datetime']).date()
                
                # 检查该信号在当天是否持仓（不包括平仓当天）
                if start_date <= date_obj < end_date:
                    total_count += 1
                    strategy_signal_type = row['strategy_signal_type']
                    strategy_counts[strategy_signal_type] = strategy_counts.get(strategy_signal_type, 0) + 1
            
            daily_occupancy.append({
                'date': date_obj,
                'signal_occupancy': total_count
            })
            
            # 记录各策略信号类型的每日占用
            for strategy_type, count in strategy_counts.items():
                if strategy_type not in strategy_daily_occupancy:
                    strategy_daily_occupancy[strategy_type] = []
                strategy_daily_occupancy[strategy_type].append({
                    'date': date_obj,
                    'count': count
                })
            
            # 为没有持仓的策略信号类型补0
            for strategy_type in strategy_daily_occupancy:
                if strategy_type not in strategy_counts:
                    strategy_daily_occupancy[strategy_type].append({
                        'date': date_obj,
                        'count': 0
                    })
        
        daily_occupancy_df = pd.DataFrame(daily_occupancy)
        
        # 创建图表
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('总体每日信号占用趋势', '各策略信号类型每日占用趋势'),
            vertical_spacing=0.1,
            row_heights=[0.4, 0.6]
        )
        
        # 总体趋势图
        fig.add_trace(
            go.Scatter(
                x=daily_occupancy_df['date'],
                y=daily_occupancy_df['signal_occupancy'],
                mode='lines+markers',
                name='总信号占用',
                line=dict(color='#1f77b4', width=2),
                marker=dict(size=4)
            ),
            row=1, col=1
        )
        
        # 添加总体平均线
        avg_occupancy = daily_occupancy_df['signal_occupancy'].mean()
        fig.add_hline(
            y=avg_occupancy,
            line_dash="dash",
            line_color="red",
            annotation_text=f"平均占用: {avg_occupancy:.1f}",
            row=1
        )
        
        # 各策略信号类型趋势图
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
        for i, (strategy_type, data) in enumerate(strategy_daily_occupancy.items()):
            strategy_df = pd.DataFrame(data)
            fig.add_trace(
                go.Scatter(
                    x=strategy_df['date'],
                    y=strategy_df['count'],
                    mode='lines+markers',
                    name=strategy_type,
                    line=dict(color=colors[i % len(colors)], width=1.5),
                    marker=dict(size=3)
                ),
                row=2, col=1
            )
        
        # 设置图表布局
        fig.update_layout(
            title={
                'text': '每日信号占用趋势分析（按策略信号类型统计，持仓期间不含平仓当天）',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 16}
            },
            height=800,
            showlegend=True,
            hovermode='x unified',
            template='plotly_white'
        )
        
        # 更新x轴和y轴
        fig.update_xaxes(title_text="日期", tickangle=45, row=2, col=1)
        fig.update_yaxes(title_text="信号数量", row=1, col=1)
        fig.update_yaxes(title_text="信号数量", row=2, col=1)
        
        # 保存图表
        chart_file = chart_dir / 'daily_signal_occupancy.html'
        fig.write_html(str(chart_file))
        
        self.log_info(f"每日信号占用折线图生成完成: {chart_file}")
        return str(chart_file)
    
    def export_pnl_rate_summary(self, pnl_summary_df: pd.DataFrame, output_path: Path) -> str:
        """导出PnL Rate汇总报告
        
        Args:
            pnl_summary_df: PnL汇总DataFrame
            output_path: 输出路径
            
        Returns:
            导出文件路径
        """
        if pnl_summary_df.empty:
            self.log_warning("PnL汇总数据为空，跳过导出")
            return ""
        
        try:
            # 导出CSV文件
            csv_file = output_path / 'pnl_rate_summary.csv'
            pnl_summary_df.to_csv(str(csv_file), index=False, encoding='utf-8-sig')
            
            self.log_info(f"PnL Rate汇总报告导出完成: {csv_file}")
            return str(csv_file)
            
        except Exception as e:
            self.log_error(f"导出PnL Rate汇总报告时出错: {e}")
            raise
    
    def create_profit_loss_distribution_charts(self, completed_signals: List[CompletedSignal], output_path: Path) -> str:
        """创建止盈止损幅度的直方图和箱线图（按策略信号类型统计）
        
        Args:
            completed_signals: 已完成信号列表
            output_path: 输出路径
            
        Returns:
            图表文件路径
        """
        if not completed_signals:
            self.log_warning("已完成信号数据为空，跳过图表生成")
            return ""
        
        # 创建图表目录
        chart_dir = output_path / 'charts'
        chart_dir.mkdir(exist_ok=True)
        
        # 转换为DataFrame
        df = pd.DataFrame([signal.to_dict() for signal in completed_signals])
        
        # 添加策略信号类型和频率字段
        df['strategy_signal'] = df['strategy_name'].apply(self._extract_strategy_signal)
        df['signal_freq'] = df['strategy_name'].apply(self._extract_signal_frequency)
        df['strategy_signal_type'] = df['strategy_signal'] + '_' + df['signal_freq']
        
        # 分离盈利和亏损信号
        profit_signals = df[df['pnl_rate'] > 0]['pnl_rate']
        loss_signals = abs(df[df['pnl_rate'] < 0]['pnl_rate'])  # 取绝对值
        
        # 按策略信号类型分组
        strategy_groups = df.groupby('strategy_signal_type')
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('止盈分布', '止损分布', '各策略信号类型止盈对比', '各策略信号类型止损对比'),
            specs=[[{"type": "histogram"}, {"type": "histogram"}], 
                   [{"type": "box"}, {"type": "box"}]],
            vertical_spacing=0.12,
            horizontal_spacing=0.1
        )
        
        # 颜色列表
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
        
        # 1. 止盈幅度直方图
        if not profit_signals.empty:
            fig.add_trace(
                go.Histogram(
                    x=profit_signals,
                    name='止盈分布',
                    nbinsx=25,
                    opacity=0.7,
                    marker_color='green',
                    showlegend=False
                ),
                row=1, col=1
            )
        
        # 2. 止损幅度直方图
        if not loss_signals.empty:
            fig.add_trace(
                go.Histogram(
                    x=loss_signals,
                    name='止损分布',
                    nbinsx=25,
                    opacity=0.7,
                    marker_color='red',
                    showlegend=False
                ),
                row=1, col=2
            )
        
        # 3. 按策略信号类型的止盈幅度箱线图
        for i, (strategy_signal_type, group_df) in enumerate(strategy_groups):
            strategy_profit = group_df[group_df['pnl_rate'] > 0]['pnl_rate']
            if not strategy_profit.empty:
                fig.add_trace(
                    go.Box(
                        y=strategy_profit,
                        name=f'{strategy_signal_type}(盈)',
                        marker_color=colors[i % len(colors)],
                        boxpoints='outliers',
                        legendgroup=f'group{i}',
                        showlegend=True
                    ),
                    row=2, col=1
                )
        
        # 4. 按策略信号类型的止损幅度箱线图
        for i, (strategy_signal_type, group_df) in enumerate(strategy_groups):
            strategy_loss = abs(group_df[group_df['pnl_rate'] < 0]['pnl_rate'])
            if not strategy_loss.empty:
                fig.add_trace(
                    go.Box(
                        y=strategy_loss,
                        name=f'{strategy_signal_type}(损)',
                        marker_color=colors[i % len(colors)],
                        boxpoints='outliers',
                        legendgroup=f'group{i}',
                        showlegend=False  # 避免重复图例
                    ),
                    row=2, col=2
                )
        
        # 更新布局
        fig.update_layout(
            title={
                'text': '策略信号类型止盈止损幅度分布分析',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 16}
            },
            height=800,
            showlegend=True,
            template='plotly_white',
            legend=dict(
                orientation="v",
                yanchor="top",
                y=0.45,
                xanchor="left",
                x=1.02,
                font=dict(size=10)
            )
        )
        
        # 更新轴标题
        fig.update_xaxes(title_text="盈利幅度", row=1, col=1)
        fig.update_xaxes(title_text="亏损幅度", row=1, col=2)
        fig.update_yaxes(title_text="频次", row=1, col=1)
        fig.update_yaxes(title_text="频次", row=1, col=2)
        fig.update_yaxes(title_text="盈利幅度", row=2, col=1)
        fig.update_yaxes(title_text="亏损幅度", row=2, col=2)
        
        # 保存图表
        chart_file = chart_dir / 'profit_loss_distribution.html'
        fig.write_html(str(chart_file))
        
        self.log_info(f"止盈止损幅度分布图表生成完成: {chart_file}")
        return str(chart_file)
    
    def _extract_strategy_signal(self, strategy_name: str) -> str:
        """从策略名称提取信号类型"""
        strategy_signal_mapping = self.config_manager.get_strategy_signal_mapping()
        strategy_name_lower = str(strategy_name).lower()
        
        for signal_type, keywords in strategy_signal_mapping.items():
            for keyword in keywords:
                if keyword.lower() in strategy_name_lower:
                    return signal_type
        
        return 'unknown_signal'
    
    def _extract_signal_frequency(self, strategy_name: str) -> str:
        """从策略名称提取信号频率"""
        signal_frequency_mapping = self.config_manager.get_signal_frequency_mapping()
        strategy_name_lower = str(strategy_name).lower()
        
        for freq_type, keywords in signal_frequency_mapping.items():
            for keyword in keywords:
                if keyword.lower() in strategy_name_lower:
                    return freq_type
        
        return 'unknown_freq'
    
