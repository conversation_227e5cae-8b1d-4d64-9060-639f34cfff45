"""头寸匹配与盈亏计算模块（支持信号合并版）

实现基于信号追踪的开平仓匹配逻辑，支持信号合并和加权平均价格计算。
核心特点：
1. 按信号标识（策略+合约+方向）进行匹配
2. 支持同一信号的连续开仓合并，计算加权平均价格
3. 自定义时间排序：21:00-24:00优先于00:00-15:00
4. 计算价格基础的盈亏率，不涉及合约乘数
5. 手续费独立累计，不直接影响盈亏率计算
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Tuple, Any, NamedTuple
from collections import defaultdict
from dataclasses import dataclass, field
from .logger_config import LoggerMixin
from .config_manager import ConfigManager


@dataclass
class OpenPosition:
    """开仓头寸数据结构（支持信号合并版）"""
    signal_key: str  # 信号标识（strategy_name + symbol + direction）
    original_order_id: str  # 首次开仓交易的orderid
    first_open_datetime: datetime  # 首次开仓时间
    last_open_datetime: datetime  # 最后一次开仓时间
    weighted_avg_open_price: float  # 加权平均开仓价格
    total_open_volume: float  # 累计开仓量
    remaining_open_volume: float  # 当前剩余未平仓量
    direction: str  # 开仓方向
    symbol: str  # 合约代码
    strategy_name: str  # 策略名称
    total_commission_incurred: float = 0.0  # 累计手续费（开仓+平仓）
    weighted_avg_close_price: float = 0.0  # 加权平均平仓价格
    matched_close_volume: float = 0.0  # 已匹配的平仓总量
    open_transactions: List[Dict[str, Any]] = field(default_factory=list)  # 所有开仓交易明细
    close_transactions: List[Dict[str, Any]] = field(default_factory=list)  # 所有平仓交易明细
    
    def add_open_transaction(self, row: pd.Series) -> None:
        """添加开仓交易，更新加权平均价格"""
        new_volume = row['volume']
        new_price = row['price']
        new_commission = row['commission']
        
        # 记录开仓交易明细
        open_transaction = {
            'open_datetime': row['datetime'],
            'open_price': new_price,
            'open_volume': new_volume,
            'open_orderid': row['orderid'],
            'commission': new_commission
        }
        self.open_transactions.append(open_transaction)
        
        # 更新加权平均开仓价格
        if self.total_open_volume == 0:
            self.weighted_avg_open_price = new_price
        else:
            total_value = self.weighted_avg_open_price * self.total_open_volume + new_price * new_volume
            self.weighted_avg_open_price = total_value / (self.total_open_volume + new_volume)
        
        # 更新数量和手续费
        self.total_open_volume += new_volume
        self.remaining_open_volume += new_volume
        self.total_commission_incurred += new_commission
        self.last_open_datetime = row['datetime']
    
    def __post_init__(self):
        """初始化后处理"""
        if self.remaining_open_volume is None:
            self.remaining_open_volume = self.total_open_volume


@dataclass
class CompletedSignal:
    """已完成信号数据结构（支持信号合并版）"""
    signal_id: str
    signal_key: str  # 信号标识
    original_order_id: str  # 首次开仓orderid
    strategy_name: str
    symbol: str
    direction: str
    first_open_datetime: datetime  # 首次开仓时间
    last_open_datetime: datetime   # 最后开仓时间
    close_datetime: datetime       # 平仓完成时间
    weighted_avg_open_price: float  # 加权平均开仓价格
    weighted_avg_close_price: float  # 加权平均平仓价格
    total_open_volume: float  # 总开仓量
    pnl_rate: float  # 盈亏率（基于加权平均价格）
    total_price_difference: float  # 总价格差
    total_commission_incurred: float  # 总手续费
    open_transactions: List[Dict[str, Any]]   # 所有开仓交易明细
    close_transactions: List[Dict[str, Any]]  # 所有平仓交易明细
    duration_seconds: float
    # 新增字段
    contract_multiplier: int = 1  # 合约乘数
    industry: str = "未知"  # 所属行业
    contract_name: str = ""  # 合约名称
    
    @property
    def duration_days(self) -> float:
        """信号持续天数"""
        return self.duration_seconds / (24 * 3600)
    
    @property
    def position_market_value(self) -> float:
        """持仓市值 = 持仓数量 * 持仓均价 * 合约乘数"""
        return self.total_open_volume * self.weighted_avg_open_price * self.contract_multiplier
    
    @property
    def signal_pnl_amount(self) -> float:
        """信号周期盈亏金额 = 持仓数量 * 合约乘数 * 价格差"""
        return self.total_open_volume * self.contract_multiplier * self.total_price_difference
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'signal_id': self.signal_id,
            'signal_key': self.signal_key,
            'original_order_id': self.original_order_id,
            'strategy_name': self.strategy_name,
            'symbol': self.symbol,
            'direction': self.direction,
            'first_open_datetime': self.first_open_datetime,
            'last_open_datetime': self.last_open_datetime,
            'close_datetime': self.close_datetime,
            'weighted_avg_open_price': self.weighted_avg_open_price,
            'weighted_avg_close_price': self.weighted_avg_close_price,
            'total_open_volume': self.total_open_volume,
            'pnl_rate': self.pnl_rate,
            'total_price_difference': self.total_price_difference,
            'total_commission_incurred': self.total_commission_incurred,
            'duration_seconds': self.duration_seconds,
            'duration_days': self.duration_days,
            'num_open_transactions': len(self.open_transactions),
            'num_close_transactions': len(self.close_transactions),
            # 新增字段
            'contract_multiplier': self.contract_multiplier,
            'industry': self.industry,
            'contract_name': self.contract_name,
            'position_market_value': self.position_market_value,
            'signal_pnl_amount': self.signal_pnl_amount
        }


@dataclass
class UncompletedSignal:
    """未完成信号数据结构（支持信号合并版）"""
    signal_id: str
    signal_key: str  # 信号标识
    original_order_id: str  # 首次开仓orderid
    strategy_name: str
    symbol: str
    direction: str
    first_open_datetime: datetime  # 首次开仓时间
    last_open_datetime: datetime   # 最后开仓时间
    weighted_avg_open_price: float  # 加权平均开仓价格
    total_open_volume: float  # 总开仓量
    remaining_open_volume: float  # 剩余未平仓量
    total_commission_incurred: float  # 累计手续费
    open_transactions: List[Dict[str, Any]]  # 所有开仓交易明细
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'signal_id': self.signal_id,
            'signal_key': self.signal_key,
            'original_order_id': self.original_order_id,
            'strategy_name': self.strategy_name,
            'symbol': self.symbol,
            'direction': self.direction,
            'first_open_datetime': self.first_open_datetime,
            'last_open_datetime': self.last_open_datetime,
            'weighted_avg_open_price': self.weighted_avg_open_price,
            'total_open_volume': self.total_open_volume,
            'remaining_open_volume': self.remaining_open_volume,
            'total_commission_incurred': self.total_commission_incurred,
            'num_open_transactions': len(self.open_transactions)
        }


class PositionMatcher(LoggerMixin):
    """头寸匹配器（支持信号合并版）
    
    实现基于信号追踪的开平仓匹配逻辑：
    1. 按信号标识（策略+合约+方向）进行匹配
    2. 支持同一信号的连续开仓合并，计算加权平均价格
    3. 自定义时间排序：21:00-24:00优先于00:00-15:00
    4. 计算价格基础的盈亏率，不涉及合约乘数
    5. 手续费独立累计，不直接影响盈亏率计算
    """
    
    def __init__(self, config_manager: ConfigManager):
        """初始化头寸匹配器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        
        # 获取配置
        offset_types = config_manager.get_offset_types()
        self.open_offsets = set(offset_types['open'])
        self.close_offsets = set(offset_types['close'])
        
        direction_mapping = config_manager.get_direction_mapping()
        self.long_directions = set(direction_mapping['long'])
        self.short_directions = set(direction_mapping['short'])
        
        # 从配置文件获取方向匹配规则
        direction_match_rules = config_manager.get_direction_match_rules()
        # 转换为大写格式以匹配数据中的标准化方向
        self.direction_matching = {}
        for open_dir, close_dir in direction_match_rules.items():
            self.direction_matching[open_dir.upper()] = close_dir.upper()
        
        # 内部状态：按信号标识分组的未平仓头寸字典
        self.open_positions_by_signal: Dict[str, OpenPosition] = {}
        self.completed_signals: List[CompletedSignal] = []
        
        self.signal_counter = 0   
 
    def match_positions(self, df: pd.DataFrame) -> Tuple[List[CompletedSignal], List[UncompletedSignal]]:
        """执行头寸匹配（按symbol和strategy_name分组处理版）
        
        Args:
            df: 预处理后的交易数据DataFrame
            
        Returns:
            (已完成信号列表, 未完成信号列表)
        """
        self.log_info("开始头寸匹配（按symbol和strategy_name分组处理版）")
        
        # 重置内部状态
        self._reset_state()
        
        # 验证数据
        self._validate_input_data(df)
        
        # 按照自定义规则排序数据
        df_sorted = self._custom_sort_data(df)
        
        # 按symbol分组处理
        self._process_trades_by_symbol_groups(df_sorted)
        
        # 生成未完成信号
        uncompleted_signals = self._generate_uncompleted_signals()
        
        self.log_info(f"头寸匹配完成，已完成信号: {len(self.completed_signals)}, "
                     f"未完成信号: {len(uncompleted_signals)}, "
                     f"未匹配交易: {len(self.get_unmatched_trades())}")
        
        return self.completed_signals, uncompleted_signals
    
    def _reset_state(self) -> None:
        """重置内部状态"""
        self.open_positions_by_signal.clear()
        self.completed_signals.clear()
        self.unmatched_trades = []
        self.signal_counter = 0
    
    def _validate_input_data(self, df: pd.DataFrame) -> None:
        """验证输入数据
        
        Args:
            df: 待验证的DataFrame
            
        Raises:
            ValueError: 数据验证失败
        """
        required_columns = [
            'strategy_name', 'symbol', 'direction_standardized', 
            'offset_standardized', 'datetime', 'price', 'volume', 'commission', 'orderid'
        ]
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"缺少必需的列: {missing_columns}")
        
        if df.empty:
            raise ValueError("输入数据为空")
    
    def _custom_sort_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """按合约、日期、时间排序数据（升序）
        
        排序规则：
        1. 按symbol（合约）排序
        2. 按datetime（日期时间）排序
        
        Args:
            df: 待排序的DataFrame
            
        Returns:
            排序后的DataFrame
        """
        # 按symbol、datetime排序（升序）
        df_sorted = df.sort_values(['symbol', 'datetime']).reset_index(drop=True)
        
        self.log_info(f"数据排序完成，共 {len(df_sorted)} 条记录")
        return df_sorted
    
    def _process_trades_by_symbol_groups(self, df: pd.DataFrame) -> None:
        """按symbol分组处理交易记录
        
        新的处理逻辑：
        1. 先按symbol筛选，再按strategy_name筛选
        2. 按时间排序选择第一条开仓信号，合并相同strategy_name连续开仓
        3. 按时间排序匹配相同strategy_name平仓信号
        4. 循环处理不同symbol直到处理完所有信号
        
        Args:
            df: 已排序的交易数据
        """
        self.log_info("开始按symbol分组处理交易记录")
        
        # 按symbol分组
        symbol_groups = df.groupby('symbol')
        
        for symbol, symbol_df in symbol_groups:
            self.log_debug(f"处理symbol: {symbol}, 交易记录数: {len(symbol_df)}")
            self._process_symbol_trades(symbol, symbol_df)
        
        self.log_info("按symbol分组处理交易记录完成")
    
    def _process_symbol_trades(self, symbol: str, symbol_df: pd.DataFrame) -> None:
        """处理单个symbol的所有交易记录
        
        Args:
            symbol: 合约代码
            symbol_df: 该symbol的所有交易记录
        """
        # 按strategy_name分组
        strategy_groups = symbol_df.groupby('strategy_name')
        
        for strategy_name, strategy_df in strategy_groups:
            self.log_debug(f"处理 {symbol}-{strategy_name}, 交易记录数: {len(strategy_df)}")
            self._process_strategy_trades(symbol, strategy_name, strategy_df)
    
    def _process_strategy_trades(self, symbol: str, strategy_name: str, strategy_df: pd.DataFrame) -> None:
        """处理单个策略的所有交易记录
        
        新的逻辑：
        1. 按strategy_name分组
        2. 筛选direction、offset分为两组：
           - 多头开仓-空头平仓组
           - 空头开仓-多头平仓组
        3. 每组按时间顺序处理，第一个开仓记为信号1，合并连续开仓直到碰到平仓
        
        Args:
            symbol: 合约代码
            strategy_name: 策略名称
            strategy_df: 该策略的所有交易记录（已按时间排序）
        """
        if strategy_df.empty:
            self.log_debug(f"策略 {symbol}-{strategy_name} 没有交易记录")
            return
        
        self.log_debug(f"处理策略 {symbol}-{strategy_name}: 总交易记录{len(strategy_df)}笔")
        
        # 分离多头开仓-空头平仓组和空头开仓-多头平仓组
        long_open_short_close_df = strategy_df[
            ((strategy_df['direction_standardized'] == 'LONG') & (strategy_df['offset_standardized'].isin(self.open_offsets))) |
            ((strategy_df['direction_standardized'] == 'SHORT') & (strategy_df['offset_standardized'].isin(self.close_offsets)))
        ].copy()
        
        short_open_long_close_df = strategy_df[
            ((strategy_df['direction_standardized'] == 'SHORT') & (strategy_df['offset_standardized'].isin(self.open_offsets))) |
            ((strategy_df['direction_standardized'] == 'LONG') & (strategy_df['offset_standardized'].isin(self.close_offsets)))
        ].copy()
        
        # 处理多头开仓-空头平仓组
        if not long_open_short_close_df.empty:
            self.log_debug(f"处理 {symbol}-{strategy_name} 多头开仓-空头平仓组: {len(long_open_short_close_df)}笔")
            self._process_direction_group(symbol, strategy_name, long_open_short_close_df, "LONG_OPEN_SHORT_CLOSE")
        
        # 处理空头开仓-多头平仓组
        if not short_open_long_close_df.empty:
            self.log_debug(f"处理 {symbol}-{strategy_name} 空头开仓-多头平仓组: {len(short_open_long_close_df)}笔")
            self._process_direction_group(symbol, strategy_name, short_open_long_close_df, "SHORT_OPEN_LONG_CLOSE")
    
    def _process_direction_group(self, symbol: str, strategy_name: str, group_df: pd.DataFrame, group_type: str) -> None:
        """处理方向组的交易记录
        
        新逻辑：
        1. 按时间顺序遍历交易记录
        2. 第一个开仓记为信号1
        3. 信号1进入平仓周期但未完全平仓时，后续新开仓仍合并到信号1
        4. 只有信号1完全平仓后，新的开仓才开始信号2
        
        Args:
            symbol: 合约代码
            strategy_name: 策略名称
            group_df: 方向组的交易记录（已按时间排序）
            group_type: 组类型（LONG_OPEN_SHORT_CLOSE 或 SHORT_OPEN_LONG_CLOSE）
        """
        trades_df = group_df.copy().reset_index(drop=True)
        signal_count = 0
        current_signal = None
        unmatched_trades = []  # 存储未匹配的交易
        
        # 确定该组的开仓和平仓方向
        if group_type == "LONG_OPEN_SHORT_CLOSE":
            open_direction = "LONG"
            close_direction = "SHORT"
        else:  # SHORT_OPEN_LONG_CLOSE
            open_direction = "SHORT"
            close_direction = "LONG"
        
        # 按时间顺序遍历所有交易记录
        for idx in trades_df.index:
            trade = trades_df.loc[idx]
            
            if trade['offset_standardized'] in self.open_offsets and trade['direction_standardized'] == open_direction:
                # 开仓交易
                if current_signal is None:
                    # 创建新信号
                    signal_count += 1
                    signal_key = f"{symbol}_{strategy_name}_{open_direction}_{signal_count}"
                    
                    current_signal = {
                        'signal_key': signal_key,
                        'symbol': symbol,
                        'strategy_name': strategy_name,
                        'direction': open_direction,
                        'open_transactions': [],
                        'close_transactions': [],
                        'total_open_volume': 0.0,
                        'weighted_avg_open_price': 0.0,
                        'total_open_commission': 0.0,
                        'first_open_datetime': None,
                        'last_open_datetime': None
                    }
                    
                    self.log_debug(f"创建新信号 {signal_key}: {open_direction} {trade['volume']}@{trade['price']} "
                                  f"时间: {trade['datetime']}")
                
                # 无论信号是否在平仓周期，都合并到当前信号
                self._add_open_transaction_to_signal(current_signal, trade)
                
            elif trade['offset_standardized'] in self.close_offsets and trade['direction_standardized'] == close_direction:
                # 平仓交易
                if current_signal is not None:
                    # 添加平仓交易
                    self._add_close_transaction_to_signal(current_signal, trade)
                    
                    self.log_debug(f"平仓信号 {current_signal['signal_key']}: "
                                  f"{close_direction} {trade['volume']}@{trade['price']}")
                    
                    # 检查信号是否完成
                    if self._is_signal_completed(current_signal):
                        self._finalize_signal(current_signal)
                        current_signal = None  # 重置当前信号，允许开始新信号
                        self.log_debug(f"信号完成平仓，重置当前信号")
                else:
                    # 没有对应的开仓信号，记录为未匹配交易
                    unmatched_trade = {
                        'symbol': symbol,
                        'strategy_name': strategy_name,
                        'datetime': trade['datetime'],
                        'direction': trade['direction_standardized'],
                        'offset': trade['offset_standardized'],
                        'price': trade['price'],
                        'volume': trade['volume'],
                        'commission': trade['commission'],
                        'orderid': trade['orderid'],
                        'reason': f"孤立的{close_direction}平仓交易，无对应开仓信号"
                    }
                    unmatched_trades.append(unmatched_trade)
                    self.log_warning(f"发现孤立的平仓交易: {close_direction} "
                                   f"{trade['volume']}@{trade['price']} 时间: {trade['datetime']}")
        
        # 存储未匹配的交易
        if unmatched_trades:
            if not hasattr(self, 'unmatched_trades'):
                self.unmatched_trades = []
            self.unmatched_trades.extend(unmatched_trades)
        
        # 处理未完成的信号
        if current_signal is not None:
            self._finalize_uncompleted_signal(current_signal)
    
    def _add_open_transaction_to_signal(self, signal: dict, trade: pd.Series) -> None:
        """添加开仓交易到信号"""
        open_transaction = {
            'open_datetime': trade['datetime'],
            'open_price': trade['price'],
            'open_volume': trade['volume'],
            'open_orderid': trade['orderid'],
            'commission': trade['commission']
        }
        signal['open_transactions'].append(open_transaction)
        
        # 更新加权平均开仓价格
        if signal['total_open_volume'] == 0:
            signal['weighted_avg_open_price'] = trade['price']
        else:
            total_value = (signal['weighted_avg_open_price'] * signal['total_open_volume'] + 
                          trade['price'] * trade['volume'])
            signal['weighted_avg_open_price'] = total_value / (signal['total_open_volume'] + trade['volume'])
        
        signal['total_open_volume'] += trade['volume']
        signal['total_open_commission'] += trade['commission']
        
        if signal['first_open_datetime'] is None:
            signal['first_open_datetime'] = trade['datetime']
        signal['last_open_datetime'] = trade['datetime']
    
    def _add_close_transaction_to_signal(self, signal: dict, trade: pd.Series) -> None:
        """添加平仓交易到信号（需要验证方向匹配）"""
        close_direction = trade['direction_standardized']
        open_direction = signal['direction']
        
        # 验证开平仓方向是否匹配
        expected_close_direction = self.direction_matching.get(open_direction)
        if expected_close_direction != close_direction:
            self.log_warning(f"信号 {signal['signal_key']} 开平仓方向不匹配: "
                           f"开仓方向 {open_direction} 应对应平仓方向 {expected_close_direction}, "
                           f"但实际平仓方向为 {close_direction}")
            return  # 不添加不匹配的平仓交易
        
        close_transaction = {
            'close_datetime': trade['datetime'],
            'close_price': trade['price'],
            'close_volume': trade['volume'],
            'close_direction': close_direction,
            'close_orderid': trade['orderid'],
            'commission': trade['commission']
        }
        signal['close_transactions'].append(close_transaction)
        signal['total_open_commission'] += trade['commission']
    
    def _is_signal_completed(self, signal: dict) -> bool:
        """检查信号是否完成（所有开仓都已平仓）"""
        total_close_volume = sum(t['close_volume'] for t in signal['close_transactions'])
        return total_close_volume >= signal['total_open_volume'] - 0.001  # 考虑浮点数精度
    
    def _finalize_signal(self, signal: dict) -> None:
        """完成信号处理"""
        close_transactions = signal['close_transactions']
        
        # 计算加权平均平仓价格
        total_close_volume = 0.0
        weighted_avg_close_price = 0.0
        close_datetime = None
        
        for close_transaction in close_transactions:
            volume = close_transaction['close_volume']
            price = close_transaction['close_price']
            
            if total_close_volume == 0:
                weighted_avg_close_price = price
            else:
                total_value = weighted_avg_close_price * total_close_volume + price * volume
                weighted_avg_close_price = total_value / (total_close_volume + volume)
            
            total_close_volume += volume
            close_datetime = close_transaction['close_datetime']  # 最后一笔平仓时间
        
        # 创建已完成信号
        self._create_completed_signal_from_transactions(
            signal['signal_key'], signal['symbol'], signal['strategy_name'], signal['direction'],
            signal['open_transactions'], close_transactions,
            signal['weighted_avg_open_price'], weighted_avg_close_price,
            signal['total_open_volume'], signal['total_open_commission'],
            signal['first_open_datetime'], signal['last_open_datetime'], close_datetime
        )
    
    def _finalize_uncompleted_signal(self, signal: dict) -> None:
        """处理未完成信号"""
        if signal['open_transactions']:
            # 计算已平仓量
            total_close_volume = sum(t['close_volume'] for t in signal['close_transactions'])
            remaining_volume = signal['total_open_volume'] - total_close_volume
            
            # 计算加权平均平仓价格（如果有平仓交易）
            weighted_avg_close_price = 0.0
            if signal['close_transactions']:
                total_close_value = sum(t['close_price'] * t['close_volume'] 
                                      for t in signal['close_transactions'])
                weighted_avg_close_price = total_close_value / total_close_volume if total_close_volume > 0 else 0.0
            
            # 创建未完成信号
            self._create_uncompleted_signal_from_transactions(
                signal['signal_key'], signal['symbol'], signal['strategy_name'], signal['direction'],
                signal['open_transactions'], signal['close_transactions'],
                signal['weighted_avg_open_price'], weighted_avg_close_price,
                signal['total_open_volume'], remaining_volume,
                signal['total_open_commission'],
                signal['first_open_datetime'], signal['last_open_datetime']
            )


    

    
    def _create_completed_signal_from_transactions(self, signal_key: str, symbol: str,
                                                 strategy_name: str, direction: str,
                                                 open_transactions: List[Dict], close_transactions: List[Dict],
                                                 weighted_avg_open_price: float, weighted_avg_close_price: float,
                                                 total_open_volume: float, total_commission: float,
                                                 first_open_datetime: datetime, last_open_datetime: datetime,
                                                 close_datetime: datetime) -> None:
        """从交易明细创建完成信号
        
        Args:
            signal_key: 信号标识
            symbol: 合约代码
            strategy_name: 策略名称
            direction: 交易方向
            open_transactions: 开仓交易明细列表
            close_transactions: 平仓交易明细列表
            weighted_avg_open_price: 加权平均开仓价格
            weighted_avg_close_price: 加权平均平仓价格
            total_open_volume: 总开仓量
            total_commission: 总手续费
            first_open_datetime: 首次开仓时间
            last_open_datetime: 最后开仓时间
            close_datetime: 平仓完成时间
        """
        self.signal_counter += 1
        signal_id = f"signal_{self.signal_counter:06d}"
        
        # 计算盈亏率和价格差
        if weighted_avg_open_price == 0:
            pnl_rate = 0.0
            total_price_difference = 0.0
        elif direction == 'LONG':
            pnl_rate = (weighted_avg_close_price - weighted_avg_open_price) / weighted_avg_open_price
            total_price_difference = weighted_avg_close_price - weighted_avg_open_price
        elif direction == 'SHORT':
            pnl_rate = (weighted_avg_open_price - weighted_avg_close_price) / weighted_avg_open_price
            total_price_difference = weighted_avg_open_price - weighted_avg_close_price
        else:
            pnl_rate = 0.0
            total_price_difference = 0.0
        
        # 计算持续时间：直接使用close_datetime - first_open_datetime
        duration_seconds = (close_datetime - first_open_datetime).total_seconds()
        # 获取合约信息
        contract_multiplier = self.config_manager.get_contract_multiplier(symbol)
        industry = self.config_manager.get_contract_industry(symbol)
        contract_name = self.config_manager.get_contract_name(symbol)
        
        # 创建完成信号
        completed_signal = CompletedSignal(
            signal_id=signal_id,
            signal_key=signal_key,
            original_order_id=open_transactions[0]['open_orderid'],
            strategy_name=strategy_name,
            symbol=symbol,
            direction=direction,
            first_open_datetime=first_open_datetime,
            last_open_datetime=last_open_datetime,
            close_datetime=close_datetime,
            weighted_avg_open_price=weighted_avg_open_price,
            weighted_avg_close_price=weighted_avg_close_price,
            total_open_volume=total_open_volume,
            pnl_rate=pnl_rate,
            total_price_difference=total_price_difference,
            total_commission_incurred=total_commission,
            open_transactions=open_transactions,
            close_transactions=close_transactions,
            duration_seconds=duration_seconds,
            contract_multiplier=contract_multiplier,
            industry=industry,
            contract_name=contract_name
        )
        
        self.completed_signals.append(completed_signal)
        
        self.log_debug(f"创建完成信号: {signal_id} ({signal_key}), "
                      f"开仓均价: {weighted_avg_open_price:.4f}, "
                      f"平仓均价: {weighted_avg_close_price:.4f}, "
                      f"盈亏率: {pnl_rate:.4f}")
    
    def _create_uncompleted_signal_from_transactions(self, signal_key: str, symbol: str,
                                                   strategy_name: str, direction: str,
                                                   open_transactions: List[Dict], close_transactions: List[Dict],
                                                   weighted_avg_open_price: float, weighted_avg_close_price: float,
                                                   total_open_volume: float, remaining_open_volume: float,
                                                   total_commission: float,
                                                   first_open_datetime: datetime, last_open_datetime: datetime) -> None:
        """从交易明细创建未完成信号
        
        Args:
            signal_key: 信号标识
            symbol: 合约代码
            strategy_name: 策略名称
            direction: 交易方向
            open_transactions: 开仓交易明细列表
            close_transactions: 平仓交易明细列表
            weighted_avg_open_price: 加权平均开仓价格
            weighted_avg_close_price: 加权平均平仓价格
            total_open_volume: 总开仓量
            remaining_open_volume: 剩余开仓量
            total_commission: 总手续费
            first_open_datetime: 首次开仓时间
            last_open_datetime: 最后开仓时间
        """
        # 创建开仓头寸对象
        open_position = OpenPosition(
            signal_key=signal_key,
            original_order_id=open_transactions[0]['open_orderid'],
            first_open_datetime=first_open_datetime,
            last_open_datetime=last_open_datetime,
            weighted_avg_open_price=weighted_avg_open_price,
            total_open_volume=total_open_volume,
            remaining_open_volume=remaining_open_volume,
            direction=direction,
            symbol=symbol,
            strategy_name=strategy_name,
            total_commission_incurred=total_commission,
            weighted_avg_close_price=weighted_avg_close_price,
            matched_close_volume=total_open_volume - remaining_open_volume,
            open_transactions=open_transactions,
            close_transactions=close_transactions
        )
        
        # 存储到未完成信号字典
        self.open_positions_by_signal[signal_key] = open_position
        
        self.log_debug(f"创建未完成信号: {signal_key}, "
                      f"总开仓量: {total_open_volume}, 剩余量: {remaining_open_volume}")
    


    
    def _get_matching_direction(self, close_direction: str) -> Optional[str]:
        """获取匹配的开仓方向
        
        Args:
            close_direction: 平仓方向
            
        Returns:
            对应的开仓方向，如果找不到则返回None
        """
        return self.direction_matching.get(close_direction, None)
    

    
    def _generate_uncompleted_signals(self) -> List[UncompletedSignal]:
        """生成未完成信号列表（支持信号合并）
        
        Returns:
            未完成信号列表
        """
        uncompleted_signals = []
        signal_counter = 0
        
        for signal_key, open_position in self.open_positions_by_signal.items():
            if open_position.remaining_open_volume > 0:
                signal_counter += 1
                signal_id = f"uncompleted_signal_{signal_counter:06d}"
                
                uncompleted_signal = UncompletedSignal(
                    signal_id=signal_id,
                    signal_key=open_position.signal_key,
                    original_order_id=open_position.original_order_id,
                    strategy_name=open_position.strategy_name,
                    symbol=open_position.symbol,
                    direction=open_position.direction,
                    first_open_datetime=open_position.first_open_datetime,
                    last_open_datetime=open_position.last_open_datetime,
                    weighted_avg_open_price=open_position.weighted_avg_open_price,
                    total_open_volume=open_position.total_open_volume,
                    remaining_open_volume=open_position.remaining_open_volume,
                    total_commission_incurred=open_position.total_commission_incurred,
                    open_transactions=open_position.open_transactions.copy()
                )
                
                uncompleted_signals.append(uncompleted_signal)
        
        return uncompleted_signals
    
    def get_unmatched_trades(self) -> List[Dict[str, Any]]:
        """获取未匹配的交易数据
        
        Returns:
            未匹配交易列表
        """
        return getattr(self, 'unmatched_trades', [])
    
    def get_matching_statistics(self) -> Dict[str, Any]:
        """获取匹配统计信息
        
        Returns:
            匹配统计字典
        """
        total_completed_signals = len(self.completed_signals)
        
        # 计算盈亏率统计
        if self.completed_signals:
            pnl_rate_list = [signal.pnl_rate for signal in self.completed_signals]
            avg_pnl_rate = np.mean(pnl_rate_list)
            max_pnl_rate = max(pnl_rate_list)
            min_pnl_rate = min(pnl_rate_list)
            
            # 胜率统计（基于盈亏率）
            winning_signals = len([rate for rate in pnl_rate_list if rate > 0])
            losing_signals = len([rate for rate in pnl_rate_list if rate < 0])
            zero_signals = len([rate for rate in pnl_rate_list if rate == 0])
            
            win_rate = winning_signals / total_completed_signals if total_completed_signals > 0 else 0
            
            # 手续费统计
            total_commission = sum(signal.total_commission_incurred for signal in self.completed_signals)
            avg_commission = total_commission / total_completed_signals
        else:
            avg_pnl_rate = max_pnl_rate = min_pnl_rate = 0
            winning_signals = losing_signals = zero_signals = 0
            win_rate = 0
            total_commission = avg_commission = 0
        
        # 未完成信号统计
        uncompleted_count = len(self.open_positions_by_signal)
        
        return {
            'total_completed_signals': total_completed_signals,
            'total_uncompleted_signals': uncompleted_count,
            'total_unmatched_trades': len(self.get_unmatched_trades()),
            'average_pnl_rate': avg_pnl_rate,
            'max_pnl_rate': max_pnl_rate,
            'min_pnl_rate': min_pnl_rate,
            'winning_signals': winning_signals,
            'losing_signals': losing_signals,
            'zero_pnl_signals': zero_signals,
            'win_rate': win_rate,
            'total_commission': total_commission,
            'average_commission': avg_commission
        }
    
    def export_completed_signals_to_dataframe(self) -> pd.DataFrame:
        """将已完成信号导出为DataFrame
        
        Returns:
            已完成信号DataFrame
        """
        if not self.completed_signals:
            return pd.DataFrame()
        
        data = []
        for signal in self.completed_signals:
            data.append({
                'signal_id': signal.signal_id,
                'signal_key': signal.signal_key,
                'original_order_id': signal.original_order_id,
                'strategy_name': signal.strategy_name,
                'symbol': signal.symbol,
                'direction': signal.direction,
                'first_open_datetime': signal.first_open_datetime,
                'last_open_datetime': signal.last_open_datetime,
                'close_datetime': signal.close_datetime,
                'weighted_avg_open_price': signal.weighted_avg_open_price,
                'weighted_avg_close_price': signal.weighted_avg_close_price,
                'total_open_volume': signal.total_open_volume,
                'pnl_rate': signal.pnl_rate,
                'total_price_difference': signal.total_price_difference,
                'total_commission_incurred': signal.total_commission_incurred,
                'duration_seconds': signal.duration_seconds,
                'num_open_transactions': len(signal.open_transactions),
                'num_close_transactions': len(signal.close_transactions),
                # 新增字段
                'contract_multiplier': signal.contract_multiplier,
                'industry': signal.industry,
                'contract_name': signal.contract_name,
                'position_market_value': signal.position_market_value,
                'signal_pnl_amount': signal.signal_pnl_amount
            })
        
        return pd.DataFrame(data)
    
    def export_uncompleted_signals_to_dataframe(self, uncompleted_signals: List[UncompletedSignal]) -> pd.DataFrame:
        """将未完成信号导出为DataFrame
        
        Args:
            uncompleted_signals: 未完成信号列表
            
        Returns:
            未完成信号DataFrame
        """
        if not uncompleted_signals:
            return pd.DataFrame()
        
        data = []
        for signal in uncompleted_signals:
            data.append({
                'signal_id': signal.signal_id,
                'signal_key': signal.signal_key,
                'original_order_id': signal.original_order_id,
                'strategy_name': signal.strategy_name,
                'symbol': signal.symbol,
                'direction': signal.direction,
                'first_open_datetime': signal.first_open_datetime,
                'last_open_datetime': signal.last_open_datetime,
                'weighted_avg_open_price': signal.weighted_avg_open_price,
                'total_open_volume': signal.total_open_volume,
                'remaining_open_volume': signal.remaining_open_volume,
                'total_commission_incurred': signal.total_commission_incurred,
                'num_open_transactions': len(signal.open_transactions)
            })
        
        return pd.DataFrame(data)