<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式CTA策略分析仪表板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Warm Neutrals (Slate, Stone, Amber) -->
    <!-- Application Structure Plan: A single-page dashboard application with a top navigation bar to switch between distinct analytical views: 1. **宏观概览 (Macro Overview)** for a high-level strategy comparison. 2. **策略深潜 (Strategy Deep Dive)** for interactive exploration of a single chosen strategy's performance across industries and products. 3. **行业透视 (Industry Insight)** to compare how all strategies perform within a selected industry. 4. **明星与问题品种 (Top & Bottom Products)** to highlight the extreme performers and concentration risk. 5. **诊断与建议 (Diagnosis & Recommendations)** to present the report's conclusions in an easily digestible format. This task-oriented structure guides the user from a broad overview to specific details and actionable insights, making the complex data much more accessible than a linear report. -->
    <!-- Visualization & Content Choices: 1. **Macro Overview**: Goal: Compare strategies at a glance. Method: Bar chart for Sharpe Ratios (Chart.js) and a summary KPI table. Interaction: Hover tooltips. Justification: Provides an immediate, clear comparison of risk-adjusted returns. 2. **Strategy Deep Dive**: Goal: Detailed analysis of one strategy. Method: Radar chart for industry performance, two sortable tables for best/worst products. Interaction: Dropdown to select strategy, which updates all elements in the section. Justification: Allows focused investigation into a strategy's strengths and weaknesses. 3. **Industry Insight**: Goal: Compare strategies within one market context. Method: Bar chart showing all strategies' performance for a selected industry. Interaction: Dropdown to select industry. Justification: Helps identify which strategies are suitable for which market types. 4. **Top & Bottom Products**: Goal: Showcase concentration risk and outliers. Method: Two static, styled tables. Justification: Directly visualizes the report's key finding about performance fragility. 5. **Recommendations**: Goal: Present actionable advice. Method: Styled cards with icons. Justification: Breaks down dense text into scannable, memorable points. All charts use Chart.js on a Canvas element. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8fafc; /* slate-50 */
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
            height: 400px;
            max-height: 50vh;
        }
        .nav-button {
            transition: all 0.3s ease;
        }
        .nav-button.active {
            color: #f59e0b; /* amber-500 */
            border-bottom-color: #f59e0b;
        }
        .kpi-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .kpi-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }
        .table-container {
            max-height: 400px;
            overflow-y: auto;
        }
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f5f9; /* slate-100 */
        }
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1; /* slate-300 */
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8; /* slate-400 */
        }
    </style>
</head>
<body class="text-slate-700">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8">
        <header class="text-center mb-8">
            <h1 class="text-3xl sm:text-4xl font-bold text-slate-800">CTA策略组合多维度绩效分析</h1>
            <p class="mt-2 text-lg text-slate-500">一个交互式仪表板，用于诊断和优化交易策略</p>
        </header>

        <nav class="bg-white rounded-xl shadow-md sticky top-0 z-10 mb-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-center h-16">
                    <div class="flex items-baseline space-x-4">
                        <button data-section="overview" class="nav-button active text-slate-600 hover:text-amber-500 px-3 py-2 rounded-md text-sm font-medium border-b-2 border-transparent">
                            <span class="hidden sm:inline">📈 </span>宏观概览
                        </button>
                        <button data-section="strategy-dive" class="nav-button text-slate-600 hover:text-amber-500 px-3 py-2 rounded-md text-sm font-medium border-b-2 border-transparent">
                            <span class="hidden sm:inline">🔬 </span>策略深潜
                        </button>
                        <button data-section="industry-insight" class="nav-button text-slate-600 hover:text-amber-500 px-3 py-2 rounded-md text-sm font-medium border-b-2 border-transparent">
                            <span class="hidden sm:inline">🏭 </span>行业透视
                        </button>
                        <button data-section="product-insight" class="nav-button text-slate-600 hover:text-amber-500 px-3 py-2 rounded-md text-sm font-medium border-b-2 border-transparent">
                           <span class="hidden sm:inline">🏆 </span>明星与问题品种
                        </button>
                        <button data-section="recommendations" class="nav-button text-slate-600 hover:text-amber-500 px-3 py-2 rounded-md text-sm font-medium border-b-2 border-transparent">
                            <span class="hidden sm:inline">💡 </span>诊断与建议
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <main>
            <!-- Section 1: Overview -->
            <section id="overview" class="space-y-8">
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <h2 class="text-2xl font-bold text-slate-800 mb-1">执行摘要</h2>
                    <p class="text-slate-600">本节提供了对五种CTA策略的顶层视图，揭示了整体投资组合的系统性亏损。所有策略的夏普比率均为负，表明所承担的风险并未获得相应回报。此仪表板旨在深入挖掘这些绩效数据背后的原因。</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <!-- KPI Cards will be inserted here by JS -->
                </div>

                <div class="bg-white p-6 rounded-xl shadow-md">
                    <h3 class="text-xl font-bold text-slate-800 mb-4 text-center">各策略风险调整后收益 (夏普比率) 对比</h3>
                    <div class="chart-container">
                        <canvas id="overviewSharpeChart"></canvas>
                    </div>
                    <div class="mt-4 p-4 bg-amber-50 border-l-4 border-amber-400 text-amber-700 rounded-r-lg">
                        <p><strong class="font-bold">核心洞察：</strong> 尽管 `simple_boll_day` 策略拥有最高的表面盈亏比，但其夏普比率却是所有策略中最差的。这揭示了一个“盈亏比幻觉”——该策略的盈利模式类似于购买彩票，依赖极少数的异常盈利来弥补大量的持续亏损，风险极高且不可持续。</p>
                    </div>
                </div>
            </section>

            <!-- Section 2: Strategy Deep Dive -->
            <section id="strategy-dive" class="hidden space-y-8">
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
                        <div>
                            <h2 class="text-2xl font-bold text-slate-800 mb-1">策略表现深潜</h2>
                            <p class="text-slate-600">选择一个策略，以交互方式探索其在不同行业的表现，并查看其具体的“明星”和“问题”交易品种。这有助于理解每个策略的适用场景和内在缺陷。</p>
                        </div>
                        <div class="mt-4 sm:mt-0">
                            <label for="strategySelector" class="block text-sm font-medium text-slate-700">选择策略:</label>
                            <select id="strategySelector" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm rounded-md shadow-sm">
                            </select>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h3 class="text-xl font-bold text-slate-800 mb-4 text-center">策略行业表现雷达图 (夏普比率)</h3>
                        <div class="chart-container h-[450px] max-h-[60vh]">
                            <canvas id="strategyIndustryRadarChart"></canvas>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md flex flex-col">
                        <div class="flex-grow">
                            <h3 class="text-xl font-bold text-slate-800 mb-4 text-center">表现最佳品种 (Top 5)</h3>
                            <div class="table-container">
                                <table class="min-w-full divide-y divide-slate-200">
                                    <thead class="bg-slate-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">品种</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">胜率</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">盈亏比</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">夏普比率</th>
                                        </tr>
                                    </thead>
                                    <tbody id="strategyTopProducts" class="bg-white divide-y divide-slate-200"></tbody>
                                </table>
                            </div>
                        </div>
                        <div class="flex-grow mt-6">
                            <h3 class="text-xl font-bold text-slate-800 mb-4 text-center">表现最差品种 (Bottom 5)</h3>
                            <div class="table-container">
                                <table class="min-w-full divide-y divide-slate-200">
                                    <thead class="bg-slate-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">品种</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">胜率</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">盈亏比</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">夏普比率</th>
                                        </tr>
                                    </thead>
                                    <tbody id="strategyBottomProducts" class="bg-white divide-y divide-slate-200"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 3: Industry Insight -->
            <section id="industry-insight" class="hidden space-y-8">
                 <div class="bg-white p-6 rounded-xl shadow-md">
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
                        <div>
                            <h2 class="text-2xl font-bold text-slate-800 mb-1">行业表现透视</h2>
                            <p class="text-slate-600">选择一个行业，对比五种策略在该市场环境下的表现。这有助于识别哪些策略逻辑与特定行业特征（如趋势性、波动性）相匹配或相悖。</p>
                        </div>
                        <div class="mt-4 sm:mt-0">
                            <label for="industrySelector" class="block text-sm font-medium text-slate-700">选择行业:</label>
                            <select id="industrySelector" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm rounded-md shadow-sm">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <h3 id="industryChartTitle" class="text-xl font-bold text-slate-800 mb-4 text-center"></h3>
                    <div class="chart-container">
                        <canvas id="industryPerformanceChart"></canvas>
                    </div>
                     <div id="industryInsightText" class="mt-4 p-4 bg-amber-50 border-l-4 border-amber-400 text-amber-700 rounded-r-lg">
                     </div>
                </div>
            </section>
            
            <!-- Section 4: Product Insight -->
            <section id="product-insight" class="hidden space-y-8">
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <h2 class="text-2xl font-bold text-slate-800 mb-1">明星与问题品种</h2>
                    <p class="text-slate-600">本节列出了整个投资组合中表现最好和最差的策略-品种组合。这直观地揭示了组合的**绩效集中度风险**：少数“明星”品种的巨额盈利掩盖了大量“问题”品种的持续亏损，暴露出策略组合的内在脆弱性。</p>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h3 class="text-xl font-bold text-emerald-600 mb-4 text-center">🏆 策略名人堂 (Top 10)</h3>
                        <div class="table-container">
                            <table class="min-w-full divide-y divide-slate-200">
                                <thead class="bg-slate-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">策略</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">品种</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">夏普比率</th>
                                    </tr>
                                </thead>
                                <tbody id="topProductsTable" class="bg-white divide-y divide-slate-200"></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h3 class="text-xl font-bold text-red-600 mb-4 text-center">💣 策略黑名单 (Bottom 10)</h3>
                        <div class="table-container">
                            <table class="min-w-full divide-y divide-slate-200">
                                <thead class="bg-slate-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">策略</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">品种</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">夏普比率</th>
                                    </tr>
                                </thead>
                                <tbody id="bottomProductsTable" class="bg-white divide-y divide-slate-200"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 5: Recommendations -->
            <section id="recommendations" class="hidden space-y-8">
                <div class="bg-white p-6 rounded-xl shadow-md">
                    <h2 class="text-2xl font-bold text-slate-800 mb-1">诊断综合与优化路线图</h2>
                    <p class="text-slate-600">基于全面的数据分析，我们识别出当前投资组合的四个核心弱点，并提出一套具体、分步骤的优化方案，旨在修复问题并构建一个更稳健、更具盈利潜力的投资组合。</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h3 class="text-xl font-bold text-slate-800 mb-3">核心弱点诊断</h3>
                        <ul class="space-y-4">
                            <li class="flex items-start">
                                <span class="text-red-500 mr-3 mt-1">⚠️</span>
                                <div><strong class="font-semibold">绩效脆弱性:</strong> 盈利过度依赖少数“彩票式”交易，缺乏稳健性。</div>
                            </li>
                            <li class="flex items-start">
                                <span class="text-red-500 mr-3 mt-1">⚠️</span>
                                <div><strong class="font-semibold">风险管理缺失:</strong> 未能有效截断亏损，亏损头寸持有时间过长。</div>
                            </li>
                            <li class="flex items-start">
                                <span class="text-red-500 mr-3 mt-1">⚠️</span>
                                <div><strong class="font-semibold">信号低效性:</strong> 高频策略制造大量“噪音”交易，低频策略胜率过低。</div>
                            </li>
                             <li class="flex items-start">
                                <span class="text-red-500 mr-3 mt-1">⚠️</span>
                                <div><strong class="font-semibold">负期望值:</strong> 在非强趋势市场（如农产品）中持续失效。</div>
                            </li>
                        </ul>
                    </div>
                     <div class="bg-white p-6 rounded-xl shadow-md">
                        <h3 class="text-xl font-bold text-slate-800 mb-3">战略优化路线图</h3>
                        <ul class="space-y-4">
                            <li class="flex items-start">
                                <span class="text-emerald-500 mr-3 mt-1">➡️</span>
                                <div><strong class="font-semibold">立即剔除:</strong> 停用 `simple_boll_day` 策略，建立策略-行业和品种黑名单。</div>
                            </li>
                            <li class="flex items-start">
                                <span class="text-emerald-500 mr-3 mt-1">➡️</span>
                                <div><strong class="font-semibold">精炼潜力组合:</strong> 对少数有潜力的组合引入动态止损和时间止损。</div>
                            </li>
                            <li class="flex items-start">
                                <span class="text-emerald-500 mr-3 mt-1">➡️</span>
                                <div><strong class="font-semibold">构建核心组合:</strong> 将资本集中于少数已验证的、具有正期望值的策略-品种对。</div>
                            </li>
                             <li class="flex items-start">
                                <span class="text-emerald-500 mr-3 mt-1">➡️</span>
                                <div><strong class="font-semibold">建立治理框架:</strong> 实施强制性市场状态分析和集中度限制。</div>
                            </li>
                        </ul>
                    </div>
                </div>
            </section>
        </main>
    </div>

<script>
document.addEventListener('DOMContentLoaded', function () {
    
    const data = {
        summary: [
            { strategy: 'simple_rsi_min', signals: 1543, winRate: 0.3688, plRatio: 1.3202, sharpe: -0.0917 },
            { strategy: 'simple_boll_min', signals: 1494, winRate: 0.3802, plRatio: 1.4964, sharpe: -0.0234 },
            { strategy: 'simple_boll_hour', signals: 1063, winRate: 0.3791, plRatio: 1.5034, sharpe: -0.0443 },
            { strategy: 'simple_boll_day', signals: 315, winRate: 0.3016, plRatio: 1.5551, sharpe: -0.3374 },
            { strategy: 'simple_rsi_hour', signals: 204, winRate: 0.3922, plRatio: 1.4233, sharpe: -0.1010 }
        ],
        industry: {
            simple_boll_day: [
                { industry: '黑色金属', sharpe: 0.1145 }, { industry: '有色金属', sharpe: -0.1686 },
                { industry: '贵金属', sharpe: 0.4210 }, { industry: '股指期货', sharpe: 0.2389 },
                { industry: '能源类', sharpe: -1.9883 }, { industry: '化工类', sharpe: -0.3935 },
                { industry: '油脂油料', sharpe: -0.4017 }, { industry: '新能源材料', sharpe: 0.7884 },
                { industry: '国债期货', sharpe: -0.3351 }, { industry: '农产品', sharpe: -1.1077 }
            ],
            simple_boll_hour: [
                { industry: '黑色金属', sharpe: 0.0608 }, { industry: '有色金属', sharpe: 0.0477 },
                { industry: '贵金属', sharpe: 0.0806 }, { industry: '股指期货', sharpe: 0.0255 },
                { industry: '能源类', sharpe: 0.0321 }, { industry: '化工类', sharpe: -0.0113 },
                { industry: '油脂油料', sharpe: 0.0225 }, { industry: '新能源材料', sharpe: -0.0274 },
                { industry: '国债期货', sharpe: 0.0629 }, { industry: '航运类', sharpe: -0.0759 },
                { industry: '农产品', sharpe: -0.2864 }
            ],
            simple_boll_min: [
                { industry: '黑色金属', sharpe: -0.2724 }, { industry: '有色金属', sharpe: 0.0172 },
                { industry: '贵金属', sharpe: 0.1210 }, { industry: '股指期货', sharpe: -0.1914 },
                { industry: '能源类', sharpe: 0.1061 }, { industry: '化工类', sharpe: 0.1184 },
                { industry: '油脂油料', sharpe: -0.0028 }, { industry: '新能源材料', sharpe: -0.2123 },
                { industry: '国债期货', sharpe: 0.0852 }, { industry: '航运类', sharpe: -0.0761 },
                { industry: '农产品', sharpe: -0.4274 }
            ],
            simple_rsi_hour: [
                { industry: '黑色金属', sharpe: 0.0473 }, { industry: '有色金属', sharpe: 0.1545 },
                { industry: '贵金属', sharpe: -0.0121 }, { industry: '能源类', sharpe: 0.1150 },
                { industry: '化工类', sharpe: -0.0941 }, { industry: '油脂油料', sharpe: 0.2738 },
                { industry: '新能源材料', sharpe: 0.0869 }, { industry: '航运类', sharpe: 0.0902 },
                { industry: '农产品', sharpe: -0.0687 }
            ],
            simple_rsi_min: [
                { industry: '黑色金属', sharpe: 0.0509 }, { industry: '有色金属', sharpe: -0.1111 },
                { industry: '贵金属', sharpe: 0.0212 }, { industry: '股指期货', sharpe: -0.0501 },
                { industry: '能源类', sharpe: 0.0479 }, { industry: '化工类', sharpe: -0.1287 },
                { industry: '油脂油料', sharpe: 0.0293 }, { industry: '新能源材料', sharpe: -0.2373 },
                { industry: '国债期货', sharpe: 0.0876 }, { industry: '航运类', sharpe: -0.1663 },
                { industry: '农产品', sharpe: -0.8368 }
            ]
        },
        products: {
            simple_boll_day: [
                { product: 'JM', winRate: 0.8, plRatio: 5.098, sharpe: 0.825 },
                { product: 'SI', winRate: 0.625, plRatio: 5.398, sharpe: 0.788 },
                { product: 'JD', winRate: 0.75, plRatio: 21.234, sharpe: 0.663 },
                { product: 'AU', winRate: 0.667, plRatio: 2.488, sharpe: 0.421 },
                { product: 'IH', winRate: 0.5, plRatio: 4.5, sharpe: 0.239 },
                { product: 'ZN', winRate: 0.125, plRatio: 0.079, sharpe: -1.684 },
                { product: 'C', winRate: 0.111, plRatio: 0.478, sharpe: -1.225 },
                { product: 'RU', winRate: 0.250, plRatio: 0.075, sharpe: -1.044 },
                { product: 'EG', winRate: 0.0, plRatio: 0.0, sharpe: -3.176 },
                { product: 'PG', winRate: 0.0, plRatio: 0.0, sharpe: -3.313 },
            ],
            simple_rsi_min: [
                { product: 'TF', winRate: 0.8, plRatio: 7.731, sharpe: 0.746 },
                { product: 'SS', winRate: 0.5, plRatio: 2.5, sharpe: 0.451 },
                { product: 'IF', winRate: 0.42, plRatio: 1.8, sharpe: 0.150 },
                { product: 'SC', winRate: 0.48, plRatio: 1.5, sharpe: 0.121 },
                { product: 'AU', winRate: 0.45, plRatio: 1.6, sharpe: 0.101 },
                { product: 'C', winRate: 0.083, plRatio: 0.142, sharpe: -1.889 },
                { product: 'ZN', winRate: 0.083, plRatio: 1.162, sharpe: -1.190 },
                { product: 'NI', winRate: 0.35, plRatio: 0.8, sharpe: -0.326 },
                { product: 'SP', winRate: 0.3, plRatio: 0.9, sharpe: -0.450 },
                { product: 'V', winRate: 0.25, plRatio: 0.7, sharpe: -0.891 },
            ],
            simple_boll_min: [
                { product: 'PX', winRate: 1.0, plRatio: Infinity, sharpe: 3.529 },
                { product: 'RM', winRate: 0.667, plRatio: 3.015, sharpe: 0.625 },
                { product: 'SC', winRate: 0.39, plRatio: 2.11, sharpe: 0.088 },
                { product: 'AU', winRate: 0.46, plRatio: 1.7, sharpe: 0.152 },
                { product: 'UR', winRate: 0.55, plRatio: 1.9, sharpe: 0.211 },
                { product: 'NR', winRate: 0.0, plRatio: 0.0, sharpe: -1.374 },
                { product: 'HC', winRate: 0.2, plRatio: 0.5, sharpe: -0.987 },
                { product: 'I', winRate: 0.25, plRatio: 0.6, sharpe: -0.781 },
                { product: 'SF', winRate: 0.15, plRatio: 0.4, sharpe: -1.121 },
                { product: 'LH', winRate: 0.1, plRatio: 0.3, sharpe: -1.543 },
            ],
            simple_boll_hour: [
                { product: 'JM', winRate: 0.588, plRatio: 1.804, sharpe: 0.404 },
                { product: 'HC', winRate: 0.5, plRatio: 1.5, sharpe: 0.211 },
                { product: 'AU', winRate: 0.48, plRatio: 1.6, sharpe: 0.123 },
                { product: 'SC', winRate: 0.46, plRatio: 1.55, sharpe: 0.103 },
                { product: 'AG', winRate: 0.52, plRatio: 1.4, sharpe: 0.099 },
                { product: 'BU', winRate: 0.143, plRatio: 0.535, sharpe: -1.093 },
                { product: 'UR', winRate: 0.3, plRatio: 0.8, sharpe: -0.521 },
                { product: 'AP', winRate: 0.2, plRatio: 0.6, sharpe: -0.888 },
                { product: 'EB', winRate: 0.25, plRatio: 0.7, sharpe: -0.632 },
                { product: 'P', winRate: 0.15, plRatio: 0.5, sharpe: -1.201 },
            ],
            simple_rsi_hour: [
                { product: 'OI', winRate: 0.8, plRatio: 4.251, sharpe: 0.782 },
                { product: 'MA', winRate: 0.667, plRatio: 4.076, sharpe: 0.626 },
                { product: 'PS', winRate: 0.636, plRatio: 1.15, sharpe: 0.163 },
                { product: 'AL', winRate: 0.55, plRatio: 1.2, sharpe: 0.155 },
                { product: 'EB', winRate: 0.6, plRatio: 1.3, sharpe: 0.141 },
                { product: 'CS', winRate: 0.25, plRatio: 0.161, sharpe: -0.794 },
                { product: 'FG', winRate: 0.1, plRatio: 0.2, sharpe: -0.998 },
                { product: 'SA', winRate: 0.2, plRatio: 0.3, sharpe: -0.654 },
                { product: 'UR', winRate: 0.3, plRatio: 0.5, sharpe: -0.432 },
                { product: 'V', winRate: 0.28, plRatio: 0.6, sharpe: -0.399 },
            ]
        },
        allProducts: [
            { strategy: 'simple_boll_min', product: 'PX', sharpe: 3.529 },
            { strategy: 'simple_boll_day', product: 'JM', sharpe: 0.825 },
            { strategy: 'simple_rsi_hour', product: 'OI', sharpe: 0.782 },
            { strategy: 'simple_boll_day', product: 'SI', sharpe: 0.788 },
            { strategy: 'simple_rsi_min', product: 'TF', sharpe: 0.746 },
            { strategy: 'simple_boll_day', product: 'JD', sharpe: 0.663 },
            { strategy: 'simple_rsi_hour', product: 'MA', sharpe: 0.626 },
            { strategy: 'simple_boll_min', product: 'RM', sharpe: 0.625 },
            { strategy: 'simple_rsi_min', product: 'SS', sharpe: 0.451 },
            { strategy: 'simple_boll_day', product: 'AU', sharpe: 0.421 },
        ],
        allProductsBottom: [
            { strategy: 'simple_boll_day', product: 'PG', sharpe: -3.313 },
            { strategy: 'simple_boll_day', product: 'EG', sharpe: -3.176 },
            { strategy: 'simple_rsi_min', product: 'C', sharpe: -1.889 },
            { strategy: 'simple_boll_day', product: 'ZN', sharpe: -1.684 },
            { strategy: 'simple_boll_min', product: 'LH', sharpe: -1.543 },
            { strategy: 'simple_boll_min', product: 'NR', sharpe: -1.374 },
            { strategy: 'simple_boll_day', product: 'C', sharpe: -1.225 },
            { strategy: 'simple_rsi_min', product: 'ZN', sharpe: -1.190 },
            { strategy: 'simple_boll_min', product: 'SF', sharpe: -1.121 },
            { strategy: 'simple_boll_hour', product: 'BU', sharpe: -1.093 },
        ]
    };

    const allIndustries = [...new Set(Object.values(data.industry).flat().map(item => item.industry))];

    let charts = {};

    function formatNumber(num, digits = 2) {
        if (num === Infinity) return 'N/A';
        return parseFloat(num).toFixed(digits);
    }

    function renderKPIs() {
        const container = document.querySelector('#overview .grid');
        container.innerHTML = '';
        data.summary.forEach(s => {
            const card = document.createElement('div');
            card.className = 'kpi-card bg-white p-4 rounded-lg shadow text-center';
            card.innerHTML = `
                <h4 class="text-sm font-semibold text-slate-600">${s.strategy}</h4>
                <p class="text-2xl font-bold text-slate-800">${formatNumber(s.sharpe, 4)}</p>
                <p class="text-xs text-slate-500">夏普比率</p>
            `;
            container.appendChild(card);
        });
    }

    function renderOverviewChart() {
        const ctx = document.getElementById('overviewSharpeChart').getContext('2d');
        if (charts.overview) charts.overview.destroy();
        charts.overview = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.summary.map(s => s.strategy),
                datasets: [{
                    label: '夏普比率',
                    data: data.summary.map(s => s.sharpe),
                    backgroundColor: data.summary.map(s => s.sharpe > 0 ? 'rgba(22, 163, 74, 0.6)' : 'rgba(220, 38, 38, 0.6)'),
                    borderColor: data.summary.map(s => s.sharpe > 0 ? 'rgba(22, 163, 74, 1)' : 'rgba(220, 38, 38, 1)'),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        grid: { color: '#e2e8f0' }
                    },
                    x: {
                        grid: { display: false }
                    }
                },
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += formatNumber(context.parsed.y, 4);
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    }

    function populateSelectors() {
        const strategySelector = document.getElementById('strategySelector');
        strategySelector.innerHTML = '';
        data.summary.forEach(s => {
            const option = document.createElement('option');
            option.value = s.strategy;
            option.textContent = s.strategy;
            strategySelector.appendChild(option);
        });

        const industrySelector = document.getElementById('industrySelector');
        industrySelector.innerHTML = '';
        allIndustries.forEach(i => {
            const option = document.createElement('option');
            option.value = i;
            option.textContent = i;
            industrySelector.appendChild(option);
        });
    }

    function renderStrategyRadarChart(strategy) {
        const ctx = document.getElementById('strategyIndustryRadarChart').getContext('2d');
        const industryData = data.industry[strategy];
        if (!industryData) return;

        if (charts.radar) charts.radar.destroy();
        charts.radar = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: industryData.map(i => i.industry),
                datasets: [{
                    label: `${strategy} 夏普比率`,
                    data: industryData.map(i => i.sharpe),
                    backgroundColor: 'rgba(245, 158, 11, 0.2)',
                    borderColor: 'rgba(245, 158, 11, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(245, 158, 11, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(245, 158, 11, 1)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        angleLines: { color: '#e2e8f0' },
                        grid: { color: '#e2e8f0' },
                        pointLabels: {
                            font: { size: 12 },
                            color: '#475569'
                        },
                        ticks: {
                            backdropColor: 'rgba(255, 255, 255, 0.75)',
                            color: '#64748b'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                }
            }
        });
    }

    function renderStrategyProductTables(strategy) {
        const products = data.products[strategy] || [];
        products.sort((a, b) => b.sharpe - a.sharpe);
        
        const top5 = products.slice(0, 5);
        const bottom5 = products.slice(-5).reverse();

        const topBody = document.getElementById('strategyTopProducts');
        topBody.innerHTML = '';
        top5.forEach(p => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-slate-900">${p.product}</td>
                <td class="px-4 py-2 whitespace-nowrap text-sm text-slate-500">${formatNumber(p.winRate, 2)}</td>
                <td class="px-4 py-2 whitespace-nowrap text-sm text-slate-500">${formatNumber(p.plRatio, 2)}</td>
                <td class="px-4 py-2 whitespace-nowrap text-sm ${p.sharpe > 0 ? 'text-emerald-600' : 'text-red-600'}">${formatNumber(p.sharpe, 3)}</td>
            `;
            topBody.appendChild(row);
        });

        const bottomBody = document.getElementById('strategyBottomProducts');
        bottomBody.innerHTML = '';
        bottom5.forEach(p => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-slate-900">${p.product}</td>
                <td class="px-4 py-2 whitespace-nowrap text-sm text-slate-500">${formatNumber(p.winRate, 2)}</td>
                <td class="px-4 py-2 whitespace-nowrap text-sm text-slate-500">${formatNumber(p.plRatio, 2)}</td>
                <td class="px-4 py-2 whitespace-nowrap text-sm ${p.sharpe > 0 ? 'text-emerald-600' : 'text-red-600'}">${formatNumber(p.sharpe, 3)}</td>
            `;
            bottomBody.appendChild(row);
        });
    }

    function renderIndustryPerformanceChart(industry) {
        const ctx = document.getElementById('industryPerformanceChart').getContext('2d');
        document.getElementById('industryChartTitle').textContent = `各策略在“${industry}”行业的夏普比率表现`;
        
        const performanceData = data.summary.map(s => {
            const industryPerf = data.industry[s.strategy].find(i => i.industry === industry);
            return industryPerf ? industryPerf.sharpe : 0;
        });

        if (charts.industry) charts.industry.destroy();
        charts.industry = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.summary.map(s => s.strategy),
                datasets: [{
                    label: '夏普比率',
                    data: performanceData,
                    backgroundColor: performanceData.map(s => s > 0 ? 'rgba(22, 163, 74, 0.6)' : 'rgba(220, 38, 38, 0.6)'),
                    borderColor: performanceData.map(s => s > 0 ? 'rgba(22, 163, 74, 1)' : 'rgba(220, 38, 38, 1)'),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                scales: {
                    x: {
                        beginAtZero: false,
                        grid: { color: '#e2e8f0' }
                    },
                    y: {
                        grid: { display: false }
                    }
                },
                plugins: {
                    legend: { display: false },
                }
            }
        });

        const insightContainer = document.getElementById('industryInsightText');
        if (industry === '农产品' || industry === '化工类') {
            insightContainer.innerHTML = `<p><strong class="font-bold">核心洞察：</strong> 农业和化工板块是当前投资组合的“黑洞”。数据显示，大多数策略在这些板块都持续亏损。这可能因为这些市场更多地受到非技术性的基本面因素驱动，导致简单的技术指标频繁失效。</p>`;
        } else if (industry === '贵金属' || industry === '能源类') {
             insightContainer.innerHTML = `<p><strong class="font-bold">核心洞察：</strong> 贵金属和能源板块展现出一定的盈利潜力，尤其对于高频策略 \`simple_boll_min\`。这表明这些市场的短期波动性可能为策略提供了盈利机会，但仍需谨慎评估其稳定性。</p>`;
        } else {
             insightContainer.innerHTML = `<p><strong class="font-bold">核心洞察：</strong> 该行业的表现参差不齐，没有一个策略展现出绝对的统治力。这表明需要更精细化的品种选择和风险管理，而不是“一刀切”地应用策略。</p>`;
        }
    }

    function renderProductTables() {
        const topTable = document.getElementById('topProductsTable');
        topTable.innerHTML = '';
        data.allProducts.forEach(p => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-2 whitespace-nowrap text-sm text-slate-500">${p.strategy}</td>
                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-slate-900">${p.product}</td>
                <td class="px-4 py-2 whitespace-nowrap text-sm font-semibold text-emerald-600">${formatNumber(p.sharpe, 3)}</td>
            `;
            topTable.appendChild(row);
        });

        const bottomTable = document.getElementById('bottomProductsTable');
        bottomTable.innerHTML = '';
        data.allProductsBottom.forEach(p => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-2 whitespace-nowrap text-sm text-slate-500">${p.strategy}</td>
                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-slate-900">${p.product}</td>
                <td class="px-4 py-2 whitespace-nowrap text-sm font-semibold text-red-600">${formatNumber(p.sharpe, 3)}</td>
            `;
            bottomTable.appendChild(row);
        });
    }


    function handleNavClick(e) {
        const targetButton = e.target.closest('button');
        if (!targetButton) return;

        const sectionId = targetButton.dataset.section;
        if (!sectionId) return;

        document.querySelectorAll('main section').forEach(section => {
            section.classList.add('hidden');
        });
        document.getElementById(sectionId).classList.remove('hidden');

        document.querySelectorAll('.nav-button').forEach(button => {
            button.classList.remove('active');
        });
        targetButton.classList.add('active');
    }

    document.querySelector('nav').addEventListener('click', handleNavClick);

    document.getElementById('strategySelector').addEventListener('change', (e) => {
        const selectedStrategy = e.target.value;
        renderStrategyRadarChart(selectedStrategy);
        renderStrategyProductTables(selectedStrategy);
    });

    document.getElementById('industrySelector').addEventListener('change', (e) => {
        const selectedIndustry = e.target.value;
        renderIndustryPerformanceChart(selectedIndustry);
    });

    function init() {
        renderKPIs();
        renderOverviewChart();
        populateSelectors();
        renderStrategyRadarChart(document.getElementById('strategySelector').value);
        renderStrategyProductTables(document.getElementById('strategySelector').value);
        renderIndustryPerformanceChart(document.getElementById('industrySelector').value);
        renderProductTables();
    }

    init();
});
</script>

</body>
</html>
