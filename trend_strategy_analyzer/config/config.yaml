# 趋势策略交易明细数据分析系统配置文件

# 数据源配置
data_source:
  input_file: "input/merged_trades.csv"
  encoding: "utf-8"
  date_format: "%Y-%m-%d %H:%M:%S%z"

# 策略信号识别配置
strategy_signals:
  simple_boll: ["simple_boll", "布林", "bollinger"]
  simple_rsi: ["simple_rsi", "相对强弱", "relative_strength"]


# 信号频率识别配置
signal_frequencies:
  day: ["day", "日线", "daily"]
  hour: ["hour", "小时", "hourly"]
  min: ["min", "分钟", "minute"]

# 开平仓类型配置
position_matching:
  # 开仓操作的offset类型
  open_offset_types:
    - "OPEN"
    - "开仓"
    - "开"
  
  # 平仓操作的offset类型
  close_offset_types:
    - "CLOSE"
    - "平仓"
    - "平今"
    - "平昨"
    - "平"
  
  # 交易方向标准化映射
  direction_mapping:
    long:
      - "多"
      - "LONG"
      - "买"
      - "long"
    short:
      - "空"
      - "SHORT"
      - "卖"
      - "short"
  
  # 开平仓方向匹配规则（开仓方向 -> 对应的平仓方向）
  direction_match_rules:
    "LONG": "SHORT"  # 多头开仓对应空头平仓
    "SHORT": "LONG"   # 空头开仓对应多头平仓
  
  # 头寸匹配方法
  matching_method: "FIFO"  # FIFO, LIFO
  
  # 一个信号的最长持续时间（秒）
  max_signal_duration_seconds: 604800  # 7天
  
  # 是否允许部分匹配
  allow_partial_matching: true
  allow_partial_close: true  # 是否允许部分平仓
  commission_allocation_method: "proportional"  # 手续费分摊方式：proportional/equal

# 绩效指标计算配置
performance_metrics:
  exclude_zero_pnl: true  # 胜率计算是否排除盈亏为零的信号
  min_trades_for_ratio: 10  # 计算盈亏比的最小交易数量
  groupby_fields:
    - "strategy_name"
    - "strategy_signal"
    - "signal_freq"
    - "symbol_category"

# 时序分析配置
time_series_analysis:
  rolling_window:
    days: 30  # 滚动窗口天数
    trades: 100  # 滚动窗口交易笔数
  periods:
    - "daily"
    - "weekly"


# 可视化配置
visualization:
  figure_size: [12, 8]
  dpi: 300
  style: "seaborn-v0_8"
  color_palette: "husl"
  output_formats: ["png", "pdf"]
  chinese_font: "SimHei"  # 中文字体
  
# 输出配置
output:
  reports_dir: "output/reports"
  charts_dir: "output/charts"
  data_dir: "output/data"
  file_formats:
    data: ["csv", "xlsx"]
    reports: ["xlsx", "html"]

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file:
    enabled: true
    path: "logs/trend_analyzer.log"
    max_size_mb: 10
    backup_count: 5
  console:
    enabled: true
    level: "INFO"

# 数据验证配置
data_validation:
  required_columns:
    - "strategy_name"
    - "orderid"
    - "symbol"
    - "exchange"
    - "direction"
    - "offset"
    - "price"
    - "volume"
    - "commission"
    - "datetime"
    - "gateway_name"
  numeric_columns:
    - "price"
    - "volume"
    - "commission"
  datetime_columns:
    - "datetime"

# 错误处理配置
error_handling:
  continue_on_data_error: true  # 数据错误时是否继续处理
  max_error_rate: 0.01  # 最大错误率（5%）
  save_error_records: true  # 是否保存错误记录
  error_output_file: "output/data/error_records.csv"

# 合约配置（从config.xlsx导入）
contracts:
  SC:
    name: "原油"
    industry: "能源类"
    mult: 1000
  FU:
    name: "燃料油"
    industry: "能源类"
    mult: 10
  LU:
    name: "低硫燃料油"
    industry: "能源类"
    mult: 10
  PG:
    name: "液化石油气（LPG）"
    industry: "能源类"
    mult: 20
  TA:
    name: "PTA（精对苯二甲酸）"
    industry: "化工类"
    mult: 5
  MA:
    name: "甲醇"
    industry: "化工类"
    mult: 10
  SA:
    name: "纯碱"
    industry: "化工类"
    mult: 20
  PP:
    name: "聚丙烯"
    industry: "化工类"
    mult: 5
  V:
    name: "聚氯乙烯（PVC）"
    industry: "化工类"
    mult: 5
  RU:
    name: "天然橡胶"
    industry: "化工类"
    mult: 10
  NR:
    name: "20号胶"
    industry: "化工类"
    mult: 10
  PF:
    name: "短纤"
    industry: "化工类"
    mult: 5
  EG:
    name: "乙二醇"
    industry: "化工类"
    mult: 10
  EB:
    name: "苯乙烯"
    industry: "化工类"
    mult: 5
  UR:
    name: "尿素"
    industry: "化工类"
    mult: 20
  BU:
    name: "沥青"
    industry: "化工类"
    mult: 10
  BZ:
    name: "纯苯"
    industry: "化工类"
    mult: 0
  CU:
    name: "铜"
    industry: "有色金属"
    mult: 5
  AL:
    name: "铝"
    industry: "有色金属"
    mult: 5
  ZN:
    name: "锌"
    industry: "有色金属"
    mult: 5
  PB:
    name: "铅"
    industry: "有色金属"
    mult: 5
  NI:
    name: "镍"
    industry: "有色金属"
    mult: 1
  SN:
    name: "锡"
    industry: "有色金属"
    mult: 1
  BC:
    name: "国际铜"
    industry: "有色金属"
    mult: 5
  RB:
    name: "螺纹钢"
    industry: "黑色金属"
    mult: 10
  HC:
    name: "热轧卷板"
    industry: "黑色金属"
    mult: 10
  I:
    name: "铁矿石"
    industry: "黑色金属"
    mult: 100
  J:
    name: "焦炭"
    industry: "黑色金属"
    mult: 100
  JM:
    name: "焦煤"
    industry: "黑色金属"
    mult: 60
  SS:
    name: "不锈钢"
    industry: "黑色金属"
    mult: 5
  SF:
    name: "硅铁"
    industry: "黑色金属"
    mult: 5
  SM:
    name: "锰硅"
    industry: "黑色金属"
    mult: 5
  AO:
    name: "氧化铝"
    industry: "有色金属"
    mult: 20
  AD:
    name: "铸造铝合金"
    industry: "有色金属"
    mult: 10
  AU:
    name: "黄金"
    industry: "贵金属"
    mult: 1000
  AG:
    name: "白银"
    industry: "贵金属"
    mult: 15
  A:
    name: "黄大豆1号"
    industry: "油脂油料"
    mult: 10
  B:
    name: "黄大豆2号"
    industry: "油脂油料"
    mult: 10
  M:
    name: "豆粕"
    industry: "油脂油料"
    mult: 10
  Y:
    name: "豆油"
    industry: "油脂油料"
    mult: 10
  C:
    name: "玉米"
    industry: "谷物"
    mult: 10
  CS:
    name: "玉米淀粉"
    industry: "谷物"
    mult: 10
  P:
    name: "棕榈油"
    industry: "油脂油料"
    mult: 10
  RM:
    name: "菜籽粕"
    industry: "油脂油料"
    mult: 10
  OI:
    name: "菜籽油"
    industry: "油脂油料"
    mult: 10
  CF:
    name: "棉花"
    industry: "软商品"
    mult: 5
  SR:
    name: "白糖"
    industry: "软商品"
    mult: 10
  AP:
    name: "苹果"
    industry: "特色农产品"
    mult: 10
  CJ:
    name: "红枣"
    industry: "特色农产品"
    mult: 5
  PK:
    name: "花生"
    industry: "油脂油料"
    mult: 5
  LH:
    name: "生猪"
    industry: "畜牧产品"
    mult: 16
  JD:
    name: "鸡蛋"
    industry: "畜牧产品"
    mult: 10
  WH:
    name: "强麦"
    industry: "谷物"
    mult: 20
  PM:
    name: "普麦"
    industry: "谷物"
    mult: 50
  RI:
    name: "早籼稻"
    industry: "谷物"
    mult: 20
  JR:
    name: "粳稻"
    industry: "谷物"
    mult: 20
  LR:
    name: "晚籼稻"
    industry: "谷物"
    mult: 20
  FB:
    name: "纤维板"
    industry: "非金属建材"
    mult: 10
  BB:
    name: "胶合板"
    industry: "非金属建材"
    mult: 500
  SI:
    name: "工业硅"
    industry: "新能源材料"
    mult: 5
  LC:
    name: "碳酸锂"
    industry: "新能源材料"
    mult: 1
  EC:
    name: "集运指数"
    industry: "航运类"
    mult: 50
  IF:
    name: "沪深300股指期货"
    industry: "股指期货"
    mult: 300
  IH:
    name: "上证50股指期货"
    industry: "股指期货"
    mult: 300
  IC:
    name: "中证500股指期货"
    industry: "股指期货"
    mult: 200
  IM:
    name: "中证1000股指期货"
    industry: "股指期货"
    mult: 200
  TS:
    name: "2年期国债期货"
    industry: "国债期货"
    mult: 20000
  TF:
    name: "5年期国债期货"
    industry: "国债期货"
    mult: 10000
  T:
    name: "10年期国债期货"
    industry: "国债期货"
    mult: 10000
  TL:
    name: "30年期国债期货"
    industry: "国债期货"
    mult: 10000
  CY:
    name: "棉纱"
    industry: "软商品"
    mult: 5
  FG:
    name: "玻璃"
    industry: "非金属建材"
    mult: 20
  LG:
    name: "原木"
    industry: "非金属建材"
    mult: 90
  PX:
    name: "对二甲苯"
    industry: "化工类"
    mult: 5
  RS:
    name: "菜籽"
    industry: "油脂油料"
    mult: 10
  SH:
    name: "烧碱"
    industry: "化工类"
    mult: 30
  ZC:
    name: "动力煤"
    industry: "能源类"
    mult: 100
  L:
    name: "塑料"
    industry: "化工类"
    mult: 5
  RR:
    name: "粳米"
    industry: "谷物"
    mult: 10
  BR:
    name: "合成橡胶"
    industry: "化工类"
    mult: 5
  SP:
    name: "纸浆"
    industry: "化工类"
    mult: 10
  WR:
    name: "线材"
    industry: "黑色金属"
    mult: 10
  PR:
    name: "瓶片"
    industry: "化工类"
    mult: 15
  PS:
    name: "多晶硅"
    industry: "有色金属"
    mult: 3