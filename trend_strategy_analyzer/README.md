# 趋势策略交易明细数据分析系统

## 系统概述

本系统是一个高度模块化的趋势策略交易明细数据分析工具，能够对原始交易数据进行端到端分析，包括数据预处理、开平仓匹配、盈亏计算、绩效指标分析和可视化报告生成。

## 核心特性

### 智能信号合并匹配
- **信号识别**：按策略名称+合约+方向自动识别交易信号
- **连续开仓合并**：同一信号的多次开仓自动合并，计算加权平均开仓价格
- **精确平仓匹配**：支持部分平仓，计算加权平均平仓价格
- **FIFO匹配原则**：按时间优先原则进行开平仓匹配

### 系统架构

系统采用模块化设计，包含以下核心模块：

1. **配置管理模块** (config_manager.py) - 集中管理所有可配置参数
2. **数据源适配模块** (data_adapter.py) - 统一的数据加载接口
3. **数据预处理模块** (data_preprocessor.py) - 数据清洗和特征工程
4. **头寸匹配与盈亏计算模块** (position_matcher.py) - 智能信号合并和开平仓匹配
5. **绩效指标计算模块** (performance_calculator.py) - 核心绩效指标计算
6. **时序分析模块** (time_series_analyzer.py) - 时间维度动态分析
7. **报告与可视化模块** (report_generator.py) - 分析报告和图表生成
8. **日志管理模块** (logger_config.py) - 统一日志记录

## 目录结构

```
trend_strategy_analyzer/
├── README.md
├── requirements.txt
├── main.py                    # 主程序入口
├── config/
│   └── config.yaml           # 配置文件
├── src/
│   ├── __init__.py
│   ├── config_manager.py     # 配置管理模块
│   ├── data_adapter.py       # 数据源适配模块
│   ├── data_preprocessor.py  # 数据预处理模块
│   ├── position_matcher.py   # 头寸匹配与盈亏计算模块
│   ├── performance_calculator.py # 绩效指标计算模块
│   ├── time_series_analyzer.py   # 时序分析模块
│   ├── report_generator.py   # 报告与可视化模块
│   └── logger_config.py      # 日志配置模块
├── input/
│   └── merged_trades.csv     # 输入数据文件
├── output/
│   ├── reports/              # 分析报告输出目录
│   ├── charts/               # 图表输出目录
│   └── data/                 # 处理后数据输出目录
├── logs/                     # 日志文件目录
```

## 使用方法

1. 将交易明细数据文件 `merged_trades.csv` 放入 `input/` 目录
2. 根据需要修改 `config/config.yaml` 配置文件
3. 运行主程序：`python main.py`
4. 查看 `output/` 目录下的分析结果

## 输入数据格式

系统接收包含以下字段的CSV文件：
- strategy_name: 策略名称
- orderid: 交易单号
- symbol: 合约代码
- exchange: 交易所
- direction: 交易方向（多/空）
- offset: 开平仓类型（开/平）
- price: 成交价格
- volume: 成交量
- commission: 手续费
- datetime: 交易时间
- gateway_name: 下单系统

## 输出结果

系统生成完整的分析报告，包含以下18个文件：

### 数据文件
- **completed_signals.csv** - 已完成信号明细（包含total_price_difference字段）
- **uncompleted_signals.csv** - 未完成信号明细
- **performance_metrics.csv** - 绩效指标汇总
- **pnl_rate_summary.csv** - 盈亏率汇总报告
- **pnl_multidimensional_analysis.xlsx** - 多维度PnL汇总Excel报告（按开仓信号分工作表）
- **time_series_daily.csv** - 日度时序数据
- **time_series_weekly.csv** - 周度时序数据
- **time_series_monthly.csv** - 月度时序数据
- **time_series_rolling.csv** - 滚动时序数据

### 报告文件
- **analysis_report.html** - 综合分析报告（HTML格式）
- **summary_report.md** - 分析摘要报告（Markdown格式）

### 可视化图表（charts目录）
- **cumulative_pnl.html** - 累计盈亏趋势图
- **rolling_metrics.html** - 滚动指标趋势图
- **periodic_performance.html** - 周期性绩效柱状图
- **daily_pnl_contribution.html** - 每日盈亏贡献图
- **daily_signal_occupancy.html** - 每日信号占用折线图
- **performance_heatmap.html** - 绩效热力图
- **risk_return_scatter.html** - 风险收益散点图

## 核心功能特性

### 1. 智能信号合并匹配
- **信号自动识别**：按策略名称+合约+方向组合识别独立交易信号
- **连续开仓合并**：同一信号的多次开仓自动合并，计算加权平均开仓价格
- **精确平仓匹配**：支持部分平仓，按FIFO原则匹配，计算加权平均平仓价格
- **智能处理未匹配**：对于数据截取导致的未匹配平仓，系统会记录警告但不影响整体分析

### 2. 全面绩效分析
- 计算总收益、胜率、盈亏比等核心指标
- 生成日度、周度、月度时序分析
- 提供滚动窗口绩效评估
- **多维度PnL汇总分析**：按开仓信号(strategy_signal + signal_freq)和品种类别(symbol_category)进行深度绩效分析，包含12项详细指标（平均盈利率、平均亏损率、胜率、盈亏比、中位数盈亏率、标准差、最大/最小盈亏率、偏度、峰度、夏普比率等）

### 3. 多维度可视化
- 交互式HTML图表，支持缩放和筛选
- 累计盈亏趋势、绩效热力图、风险收益分析
- 每日信号占用率分析，优化策略容量管理

### 4. 灵活配置管理
- YAML配置文件，支持策略参数、匹配规则等自定义
- 模块化设计，易于扩展和维护

## 依赖库

- pandas: 数据处理和分析
- numpy: 数值计算
- plotly: 交互式图表绘制
- openpyxl: Excel文件读写
- pyyaml: 配置文件解析
- pathlib: 路径处理
- logging: 日志记录