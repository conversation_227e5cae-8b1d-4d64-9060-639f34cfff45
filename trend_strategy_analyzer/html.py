# -*- coding: utf-8 -*-

import dash
import dash_bootstrap_components as dbc
from dash import dcc, html, dash_table
from dash.dependencies import Input, Output, State
import plotly.graph_objects as go
import pandas as pd
import numpy as np
import os

# =============================================================================
# 数据加载与预处理
# =============================================================================
# 注意：请将所有CSV文件与此Python脚本放在同一目录下。
# 或者，您可以修改下面的路径来指向您的文件位置。

def load_data(file_path="."):
    """
    加载所有CSV数据并进行预处理。
    """
    data = {
        'summary': None,
        'industry': {},
        'products': {}
    }
    
    try:
        # 加载主汇总文件
        data['summary'] = pd.read_csv(os.path.join(file_path, 'pnl_multidimensional_analysis.xlsx - 汇总.csv'))
        
        strategies = data['summary']['开仓信号'].unique()
        
        all_products_list = []

        for strategy in strategies:
            # 加载行业分析数据
            industry_file = os.path.join(file_path, f'pnl_multidimensional_analysis.xlsx - {strategy}_行业分析.csv')
            if os.path.exists(industry_file):
                data['industry'][strategy] = pd.read_csv(industry_file)

            # 加载品种分析数据
            product_file = os.path.join(file_path, f'pnl_multidimensional_analysis.xlsx - {strategy}.csv')
            if os.path.exists(product_file):
                df_prod = pd.read_csv(product_file)
                data['products'][strategy] = df_prod
                
                # 为“明星与问题品种”部分准备数据
                df_temp = df_prod[['symbol_category', '盈亏率夏普比率']].copy()
                df_temp['strategy'] = strategy
                df_temp.rename(columns={'symbol_category': 'product', '盈亏率夏普比率': 'sharpe'}, inplace=True)
                all_products_list.append(df_temp)

        # 合并所有品种数据并排序
        if all_products_list:
            all_products_df = pd.concat(all_products_list, ignore_index=True)
            all_products_df.dropna(subset=['sharpe'], inplace=True)
            all_products_df = all_products_df.sort_values(by='sharpe', ascending=False)
            data['all_products_top'] = all_products_df.head(10)
            data['all_products_bottom'] = all_products_df.tail(10).sort_values(by='sharpe', ascending=True)

        # 提取所有行业名称
        all_industries_list = []
        for df in data['industry'].values():
            all_industries_list.extend(df['industry'].unique())
        data['all_industries'] = sorted(list(set(all_industries_list)))

    except FileNotFoundError as e:
        print(f"错误：找不到数据文件 {e.filename}。请确保所有CSV文件都在正确的路径下。")
        return None
        
    return data

# 加载数据
data = load_data()

if data is None:
    # 如果数据加载失败，则不启动应用
    exit()

# =============================================================================
# 应用初始化
# =============================================================================
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP, dbc.icons.FONT_AWESOME])
app.title = "交互式CTA策略分析仪表板"

# =============================================================================
# 辅助函数 - 创建组件
# =============================================================================
def create_kpi_card(strategy, sharpe):
    """创建单个KPI卡片"""
    return dbc.Card(
        dbc.CardBody([
            html.H4(strategy, className="card-title text-sm font-weight-bold text-secondary"),
            html.P(f"{sharpe:.4f}", className="card-text h2 font-weight-bolder text-dark"),
            html.P("夏普比率", className="card-text text-muted small")
        ]),
        className="text-center shadow-sm"
    )

def format_number(num, digits=2):
    """格式化数字用于表格显示"""
    if pd.isna(num) or np.isinf(num):
        return 'N/A'
    return f"{num:.{digits}f}"

# =============================================================================
# 应用布局
# =============================================================================
app.layout = dbc.Container([
    # -- 页眉 --
    html.Header([
        html.H1("CTA策略组合多维度绩效分析", className="text-center font-weight-bold text-dark mt-4"),
        html.P("一个交互式仪表板，用于诊断和优化交易策略", className="text-center text-muted mb-4")
    ]),
    
    # -- 导航栏 --
    dbc.NavbarSimple(
        id='navigation-tabs',
        children=[
            dbc.Button("📈 宏观概览", id="btn-overview", color="link", className="text-secondary active"),
            dbc.Button("🔬 策略深潜", id="btn-strategy-dive", color="link", className="text-secondary"),
            dbc.Button("🏭 行业透视", id="btn-industry-insight", color="link", className="text-secondary"),
            dbc.Button("🏆 明星与问题品种", id="btn-product-insight", color="link", className="text-secondary"),
            dbc.Button("💡 诊断与建议", id="btn-recommendations", color="link", className="text-secondary"),
        ],
        brand=None,
        color="white",
        sticky="top",
        className="shadow-sm mb-4 rounded"
    ),

    # -- 内容区域 --
    html.Main([
        # -- Section 1: 宏观概览 --
        html.Div(id='section-overview', children=[
            dbc.Card(dbc.CardBody([
                html.H2("执行摘要", className="font-weight-bold"),
                html.P("本节提供了对五种CTA策略的顶层视图，揭示了整体投资组合的系统性亏损。所有策略的夏普比率均为负，表明所承担的风险并未获得相应回报。此仪表板旨在深入挖掘这些绩效数据背后的原因。")
            ]), className="mb-4 shadow-sm"),
            
            dbc.Row(
                [dbc.Col(create_kpi_card(row['开仓信号'], row['整体夏普比率']), width=12, lg=2, md=4, sm=6, className="mb-3") 
                 for _, row in data['summary'].iterrows()],
                 className="mb-4"
            ),
            
            dbc.Card(dbc.CardBody([
                html.H3("各策略风险调整后收益 (夏普比率) 对比", className="text-center font-weight-bold mb-4"),
                dcc.Graph(id='overview-sharpe-chart'),
                dbc.Alert([
                    html.Strong("核心洞察："),
                    "尽管 `simple_boll_day` 策略拥有最高的表面盈亏比，但其夏普比率却是所有策略中最差的。这揭示了一个“盈亏比幻觉”——该策略的盈利模式类似于购买彩票，依赖极少数的异常盈利来弥补大量的持续亏损，风险极高且不可持续。"
                ], color="warning", className="mt-4")
            ]), className="shadow-sm")
        ]),

        # -- Section 2: 策略深潜 --
        html.Div(id='section-strategy-dive', children=[
            dbc.Card(dbc.CardBody([
                dbc.Row([
                    dbc.Col([
                        html.H2("策略表现深潜", className="font-weight-bold"),
                        html.P("选择一个策略，以交互方式探索其在不同行业的表现，并查看其具体的“明星”和“问题”交易品种。这有助于理解每个策略的适用场景和内在缺陷。")
                    ], width=12, md=8),
                    dbc.Col([
                        html.Label("选择策略:", htmlFor="strategy-selector"),
                        dcc.Dropdown(
                            id='strategy-selector',
                            options=[{'label': s, 'value': s} for s in data['summary']['开仓信号']],
                            value=data['summary']['开仓信号'][0]
                        )
                    ], width=12, md=4, className="align-self-center")
                ])
            ]), className="mb-4 shadow-sm"),

            dbc.Row([
                dbc.Col(dbc.Card(dbc.CardBody([
                    html.H3("策略行业表现雷达图 (夏普比率)", className="text-center font-weight-bold mb-4"),
                    dcc.Graph(id='strategy-industry-radar-chart', style={'height': '450px'})
                ]), className="shadow-sm h-100"), lg=6, className="mb-4"),
                
                dbc.Col(dbc.Card(dbc.CardBody([
                    html.H3("表现最佳品种 (Top 5)", className="text-center font-weight-bold mb-3"),
                    dash_table.DataTable(id='strategy-top-products-table', style_cell={'textAlign': 'left'}, style_header={'fontWeight': 'bold'}),
                    html.H3("表现最差品种 (Bottom 5)", className="text-center font-weight-bold mt-5 mb-3"),
                    dash_table.DataTable(id='strategy-bottom-products-table', style_cell={'textAlign': 'left'}, style_header={'fontWeight': 'bold'}),
                ]), className="shadow-sm h-100"), lg=6, className="mb-4"),
            ])
        ], style={'display': 'none'}),

        # -- Section 3: 行业透视 --
        html.Div(id='section-industry-insight', children=[
             dbc.Card(dbc.CardBody([
                dbc.Row([
                    dbc.Col([
                        html.H2("行业表现透视", className="font-weight-bold"),
                        html.P("选择一个行业，对比五种策略在该市场环境下的表现。这有助于识别哪些策略逻辑与特定行业特征（如趋势性、波动性）相匹配或相悖。")
                    ], width=12, md=8),
                    dbc.Col([
                        html.Label("选择行业:", htmlFor="industry-selector"),
                        dcc.Dropdown(
                            id='industry-selector',
                            options=[{'label': i, 'value': i} for i in data['all_industries']],
                            value=data['all_industries'][0]
                        )
                    ], width=12, md=4, className="align-self-center")
                ])
            ]), className="mb-4 shadow-sm"),
            
            dbc.Card(dbc.CardBody([
                html.H3(id="industry-chart-title", className="text-center font-weight-bold mb-4"),
                dcc.Graph(id='industry-performance-chart'),
                dbc.Alert(id="industry-insight-text", color="warning", className="mt-4")
            ]), className="shadow-sm")
        ], style={'display': 'none'}),
        
        # -- Section 4: 明星与问题品种 --
        html.Div(id='section-product-insight', children=[
            dbc.Card(dbc.CardBody([
                html.H2("明星与问题品种", className="font-weight-bold"),
                html.P(["本节列出了整个投资组合中表现最好和最差的策略-品种组合。这直观地揭示了组合的", html.Strong("绩效集中度风险"), "：少数“明星”品种的巨额盈利掩盖了大量“问题”品种的持续亏损，暴露出策略组合的内在脆弱性。"])
            ]), className="mb-4 shadow-sm"),
            dbc.Row([
                dbc.Col(dbc.Card(dbc.CardBody([
                    html.H3("🏆 策略名人堂 (Top 10)", className="text-center font-weight-bold text-success mb-3"),
                    dash_table.DataTable(
                        id='top-products-table',
                        columns=[
                            {'name': '策略', 'id': 'strategy'},
                            {'name': '品种', 'id': 'product'},
                            {'name': '夏普比率', 'id': 'sharpe', 'type': 'numeric', 'format': dash_table.Format.Format(precision=3)}
                        ],
                        data=data['all_products_top'].to_dict('records'),
                        style_cell={'textAlign': 'left'}, style_header={'fontWeight': 'bold'}
                    )
                ]), className="shadow-sm h-100"), lg=6, className="mb-4"),
                dbc.Col(dbc.Card(dbc.CardBody([
                    html.H3("💣 策略黑名单 (Bottom 10)", className="text-center font-weight-bold text-danger mb-3"),
                     dash_table.DataTable(
                        id='bottom-products-table',
                        columns=[
                            {'name': '策略', 'id': 'strategy'},
                            {'name': '品种', 'id': 'product'},
                            {'name': '夏普比率', 'id': 'sharpe', 'type': 'numeric', 'format': dash_table.Format.Format(precision=3)}
                        ],
                        data=data['all_products_bottom'].to_dict('records'),
                        style_cell={'textAlign': 'left'}, style_header={'fontWeight': 'bold'}
                    )
                ]), className="shadow-sm h-100"), lg=6, className="mb-4"),
            ])
        ], style={'display': 'none'}),

        # -- Section 5: 诊断与建议 --
        html.Div(id='section-recommendations', children=[
            dbc.Card(dbc.CardBody([
                html.H2("诊断综合与优化路线图", className="font-weight-bold"),
                html.P("基于全面的数据分析，我们识别出当前投资组合的四个核心弱点，并提出一套具体、分步骤的优化方案，旨在修复问题并构建一个更稳健、更具盈利潜力的投资组合。")
            ]), className="mb-4 shadow-sm"),
            dbc.Row([
                dbc.Col(dbc.Card(dbc.CardBody([
                    html.H3("核心弱点诊断", className="font-weight-bold mb-3"),
                    html.Ul([
                        html.Li("⚠️ 绩效脆弱性: 盈利过度依赖少数“彩票式”交易，缺乏稳健性。"),
                        html.Li("⚠️ 风险管理缺失: 未能有效截断亏损，亏损头寸持有时间过长。"),
                        html.Li("⚠️ 信号低效性: 高频策略制造大量“噪音”交易，低频策略胜率过低。"),
                        html.Li("⚠️ 负期望值: 在非强趋势市场（如农产品）中持续失效。"),
                    ], className="list-unstyled")
                ]), className="shadow-sm h-100"), lg=6, className="mb-4"),
                dbc.Col(dbc.Card(dbc.CardBody([
                    html.H3("战略优化路线图", className="font-weight-bold mb-3"),
                    html.Ul([
                        html.Li("➡️ 立即剔除: 停用 `simple_boll_day` 策略，建立策略-行业和品种黑名单。"),
                        html.Li("➡️ 精炼潜力组合: 对少数有潜力的组合引入动态止损和时间止损。"),
                        html.Li("➡️ 构建核心组合: 将资本集中于少数已验证的、具有正期望值的策略-品种对。"),
                        html.Li("➡️ 建立治理框架: 实施强制性市场状态分析和集中度限制。"),
                    ], className="list-unstyled")
                ]), className="shadow-sm h-100"), lg=6, className="mb-4"),
            ])
        ], style={'display': 'none'}),
    ])
], fluid=True)


# =============================================================================
# 回调函数
# =============================================================================
@app.callback(
    [Output('section-overview', 'style'),
     Output('section-strategy-dive', 'style'),
     Output('section-industry-insight', 'style'),
     Output('section-product-insight', 'style'),
     Output('section-recommendations', 'style'),
     Output('btn-overview', 'className'),
     Output('btn-strategy-dive', 'className'),
     Output('btn-industry-insight', 'className'),
     Output('btn-product-insight', 'className'),
     Output('btn-recommendations', 'className')],
    [Input('btn-overview', 'n_clicks'),
     Input('btn-strategy-dive', 'n_clicks'),
     Input('btn-industry-insight', 'n_clicks'),
     Input('btn-product-insight', 'n_clicks'),
     Input('btn-recommendations', 'n_clicks')]
)
def display_section(btn1, btn2, btn3, btn4, btn5):
    """根据导航按钮点击，切换显示的内容区域"""
    ctx = dash.callback_context
    if not ctx.triggered:
        button_id = 'btn-overview'
    else:
        button_id = ctx.triggered[0]['prop_id'].split('.')[0]

    sections = ['overview', 'strategy-dive', 'industry-insight', 'product-insight', 'recommendations']
    # FIX: Corrected the dictionary creation for styles
    styles = [{'display': 'block' if f'btn-{s}' == button_id else 'none'} for s in sections]
    
    base_class = "text-secondary"
    active_class = "text-secondary active"
    btn_classes = [active_class if f'btn-{s}' == button_id else base_class for s in sections]
    
    return styles + btn_classes


@app.callback(
    Output('overview-sharpe-chart', 'figure'),
    Input('section-overview', 'style') # 仅在section可见时触发一次
)
def update_overview_chart(style):
    """更新宏观概览的夏普比率图"""
    if style.get('display') == 'none':
        return go.Figure()

    df = data['summary']
    colors = ['#dc3545' if x < 0 else '#28a745' for x in df['整体夏普比率']]
    
    fig = go.Figure(go.Bar(
        x=df['开仓信号'],
        y=df['整体夏普比率'],
        marker_color=colors,
        text=df['整体夏普比率'].apply(lambda x: f'{x:.4f}'),
        textposition='auto'
    ))
    fig.update_layout(
        xaxis_title="策略",
        yaxis_title="夏普比率",
        showlegend=False,
        margin=dict(l=20, r=20, t=20, b=20),
        plot_bgcolor='white'
    )
    return fig


@app.callback(
    [Output('strategy-industry-radar-chart', 'figure'),
     Output('strategy-top-products-table', 'data'),
     Output('strategy-top-products-table', 'columns'),
     Output('strategy-bottom-products-table', 'data'),
     Output('strategy-bottom-products-table', 'columns')],
    Input('strategy-selector', 'value')
)
def update_strategy_dive(selected_strategy):
    """更新策略深潜部分的所有图表和表格"""
    # 雷达图
    df_industry = data['industry'].get(selected_strategy, pd.DataFrame())
    radar_fig = go.Figure()
    if not df_industry.empty:
        radar_fig.add_trace(go.Scatterpolar(
            r=df_industry['夏普比率'],
            theta=df_industry['industry'],
            fill='toself',
            name='夏普比率',
            line_color='#f59e0b'
        ))
        min_val = df_industry['夏普比率'].min()
        max_val = df_industry['夏普比率'].max()
        radar_fig.update_layout(
            polar=dict(radialaxis=dict(visible=True, range=[min_val - 0.1, max_val + 0.1])),
            showlegend=False,
            margin=dict(l=60, r=60, t=40, b=40)
        )

    # 品种表格
    df_products = data['products'].get(selected_strategy, pd.DataFrame())
    if not df_products.empty:
        df_products = df_products.sort_values(by='盈亏率夏普比率', ascending=False)
        top5 = df_products.head(5)
        bottom5 = df_products.tail(5).sort_values(by='盈亏率夏普比率', ascending=True)

        cols = [
            {'name': '品种', 'id': 'symbol_category'},
            {'name': '胜率', 'id': '胜率'},
            {'name': '盈亏比', 'id': '盈亏比'},
            {'name': '夏普比率', 'id': '盈亏率夏普比率'}
        ]
        
        # 格式化数据
        top5_data = [{
            'symbol_category': row['symbol_category'],
            '胜率': format_number(row['胜率'], 2),
            '盈亏比': format_number(row['盈亏比'], 2),
            '盈亏率夏普比率': format_number(row['盈亏率夏普比率'], 3)
        } for _, row in top5.iterrows()]
        
        bottom5_data = [{
            'symbol_category': row['symbol_category'],
            '胜率': format_number(row['胜率'], 2),
            '盈亏比': format_number(row['盈亏比'], 2),
            '盈亏率夏普比率': format_number(row['盈亏率夏普比率'], 3)
        } for _, row in bottom5.iterrows()]
        
        return radar_fig, top5_data, cols, bottom5_data, cols

    return radar_fig, [], [], [], []


@app.callback(
    [Output('industry-performance-chart', 'figure'),
     Output('industry-chart-title', 'children'),
     Output('industry-insight-text', 'children')],
    Input('industry-selector', 'value')
)
def update_industry_insight(selected_industry):
    """更新行业透视部分的图表和文本"""
    perf_data = []
    for strategy in data['summary']['开仓信号']:
        df_industry = data['industry'].get(strategy, pd.DataFrame())
        if not df_industry.empty:
            perf = df_industry[df_industry['industry'] == selected_industry]
            sharpe = perf['夏普比率'].iloc[0] if not perf.empty else 0
            perf_data.append({'strategy': strategy, 'sharpe': sharpe})
    
    df_perf = pd.DataFrame(perf_data)
    
    colors = ['#dc3545' if x < 0 else '#28a745' for x in df_perf['sharpe']]
    
    fig = go.Figure(go.Bar(
        y=df_perf['strategy'],
        x=df_perf['sharpe'],
        orientation='h',
        marker_color=colors
    ))
    fig.update_layout(
        xaxis_title="夏普比率",
        yaxis_title="策略",
        showlegend=False,
        margin=dict(l=20, r=20, t=20, b=20),
        plot_bgcolor='white'
    )
    
    title = f"各策略在“{selected_industry}”行业的夏普比率表现"
    
    insight_text = ""
    if selected_industry in ['农产品', '化工类', '油脂油料']:
        insight_text = "核心洞察：农业和化工板块是当前投资组合的“黑洞”。数据显示，大多数策略在这些板块都持续亏损。这可能因为这些市场更多地受到非技术性的基本面因素驱动，导致简单的技术指标频繁失效。"
    elif selected_industry in ['贵金属', '能源类']:
        insight_text = "核心洞察：贵金属和能源板块展现出一定的盈利潜力，尤其对于高频策略 `simple_boll_min`。这表明这些市场的短期波动性可能为策略提供了盈利机会，但仍需谨慎评估其稳定性。"
    else:
        insight_text = "核心洞察：该行业的表现参差不齐，没有一个策略展现出绝对的统治力。这表明需要更精细化的品种选择和风险管理，而不是“一刀切”地应用策略。"
        
    return fig, title, insight_text


# =============================================================================
# 运行应用
# =============================================================================
if __name__ == '__main__':
    # 运行前请确保已安装所需库:
    # pip install dash dash-bootstrap-components pandas
    app.run_server(debug=True)
