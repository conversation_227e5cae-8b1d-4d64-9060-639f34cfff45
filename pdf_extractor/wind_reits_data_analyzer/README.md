# Wind REITs 数据分析器

## 项目简介

这是一个基于Wind API的REITs（房地产投资信托基金）数据获取和分析工具。该程序能够自动获取REITs的财务数据和市场价格数据，并计算相关的分析指标。

## 主要功能

### 1. 数据获取
- **财务数据获取**: 获取REITs的季度财务报表数据
- **价格数据获取**: 获取REITs的日度市场价格数据
- **基础信息获取**: 获取REITs的基本信息（上市日期、资产类型等）

### 2. 数据处理
- **数据清洗**: 自动处理缺失值和异常数据
- **时间序列处理**: 按时间顺序整理数据
- **同比增长率计算**: 自动计算收入和可供分配金额的同比增长率

### 3. 数据导出
- **CSV格式导出**: 将处理后的数据保存为CSV文件
- **时间戳命名**: 自动添加时间戳避免文件覆盖

## 数据字段说明

### 财务数据字段
- `qanal_reits_ebit`: 当期EBITDA
- `qanal_reits_distributedamounts`: 当期可供分配金额
- `qanal_reits_income`: 本期收入
- `qanal_reits_ri`: 租金收入
- `qanal_reits_PRI`: 物业收入
- `qanal_reits_pi`: 停车费收入
- `stm_is_reits_operatingcost`: 营业成本
- `stm_bs_reits_totaloperatingrevenue`: 营业总收入
- `anal_reits_depreciationandamortization`: 折旧摊销
- `stm_is_reits_taxandsurcharges`: 税金及附加
- `stm_is_reits_managementfee`: 管理费用
- `reits_unitdistributableamount`: 近一年单位可供分配金额
- `reits_pffo`: P/FFO比率

### 市场数据字段
- `val_mv_ARD`: 市值
- `reits_distributionratio`: 年化派息率
- `reits_cbvalyield`: IRR（中债估值）
- `close`: 收盘价
- `nav`: 净值
- `turn`: 换手率

### 计算指标
- `distributable_amount_yoy_growth`: 可供分配金额同比增长率
- `current_period_revenue_yoy_growth`: 收入同比增长率
- `nav_premium_rate`: 净值溢价率

## 文件结构

```
wind_reits_data_analyzer/
├── wind_api_data.py              # 主程序文件
├── REITs基本资料20250717.xlsx     # REITs基础数据文件
├── README.md                     # 项目说明文档
└── reits_*_data_*.csv           # 生成的数据文件
```

## 使用方法

### 环境要求
- Python 3.7+
- Wind API (WindPy)
- pandas
- openpyxl

### 安装依赖
```bash
pip install pandas WindPy openpyxl
```

### 运行程序
```bash
python wind_api_data.py
```

### 程序执行流程
1. 读取REITs基础资料Excel文件
2. 筛选园区基础设施类型的REITs
3. 获取REITs上市日期信息
4. 获取季度财务数据
5. 获取日度价格数据
6. 计算衍生指标
7. 保存数据到CSV文件

## 输出文件

程序运行后会生成两个CSV文件：
- `reits_financial_data_YYYYMMDD_HHMMSS.csv`: 财务数据
- `reits_price_data_YYYYMMDD_HHMMSS.csv`: 价格数据

## 注意事项

1. **Wind API连接**: 确保Wind终端已启动并且API连接正常
2. **数据权限**: 确保有相应的Wind数据权限
3. **网络连接**: 数据获取需要稳定的网络连接
4. **文件路径**: 确保`REITs基本资料20250717.xlsx`文件在同一目录下

## 错误处理

程序包含完善的错误处理机制：
- 网络连接异常处理
- 数据获取失败重试
- 文件读写异常处理
- 详细的错误日志输出

## 更新日志

- **2025-08-13**: 初始版本发布
  - 实现基础数据获取功能
  - 添加同比增长率计算
  - 完善错误处理机制

## 技术支持

如有问题或建议，请联系开发团队。