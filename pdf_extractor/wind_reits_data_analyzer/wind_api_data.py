import pandas as pd
from WindPy import w
from datetime import datetime
import os

w.start()
w.isconnected()
'''
qanal_reits_ebit 当期EBITDA,
qanal_reits_distributedamounts 当期可供分配金额,
qanal_reits_income 本期收入,
qanal_reits_ri 租金收入,
qanal_reits_PRI 物业收入,
qanal_reits_pi 停车费收入,
stm_is_reits_operatingcost 营业成本,
stm_bs_reits_totaloperatingrevenue 营业总收入,
anal_reits_depreciationandamortization 折旧摊销,
stm_is_reits_taxandsurcharges 税金及附加,
stm_is_reits_managementfee 管理费用,
reits_unitdistributableamount 近一年单位可供分配金额 Y,
val_mv_ARD 市值 D,
reits_distributionratio 年化派息率 D,
reits_pffo p/ffo D,
reits_cbvalyield IRR(中债估值,当前日期-1；如果是周一，-3) D,
name_official 官方简称,
fund__reitslisteddate REITs上市日期,
fund__reitstype 资产类型,
fund__reitsoriginal 原始权益人,
fund_reitsrproperty 项目属性,
fund__reitsorcom 原始权益人性质,
distributable_amount_yoy_growth 派息率同比增长,
current_period_revenue_yoy_growth 收入同比增长
'''


# 输入Dataframe数据，筛选index，每一类再按照日期列升序，向后填充空白数据
def fillna_by_date(df):
    if 'DateTime' in df.columns:
        df['DateTime'] = pd.to_datetime(df['DateTime'])
        df = df.sort_values('DateTime')
    elif df.index.name == 'DateTime' or isinstance(df.index, pd.DatetimeIndex):
        df = df.sort_index()
    
    # 使用新的填充方法替代已弃用的method参数
    df = df.bfill()
    return df


# 根据输入初始年份，获取初始到最新日的季度末的日期，格式为yyyy-mm-dd
def get_last_day_of_quarter(year):
    current_month = datetime.now().month
    # 计算当前是第几个季度（1-4）
    current_quarter = (current_month ) // 3
    quarter_list = list()
    
    for i in range(year, datetime.now().year + 1):
        quarter_list += [f"{i}-03-31", f"{i}-06-30", f"{i}-09-30", f"{i}-12-31"]
    
    # 删除未来的季度
    quarters_to_remove = 4 - current_quarter
    if quarters_to_remove > 0:
        del quarter_list[-quarters_to_remove:]
    
    return quarter_list


def get_reits_date(code):
    error_code, data = w.wss(code, "fund__reitslisteddate", "", "", "", usedf=True)
    data['FUND__REITSLISTEDDATE'] = pd.to_datetime(data['FUND__REITSLISTEDDATE'])
    date = data['FUND__REITSLISTEDDATE'].min()
    return date,data


def reits_data(code, date):
    # 财报季度数据
    today = datetime.today().strftime('%Y%m%d')
    time_list = get_last_day_of_quarter(date)
    data_list = list()
    
    for i in time_list:
        try:
            # 静态数据，fund__reitsoriginal/fund__reitsorcom有order参数，选择第1个权益人
            error_code, data = w.wss(code,
                                     "name_official,fund__reitslisteddate,fund__reitstype,fund__reitsoriginal,fund_reitsrproperty,fund__reitsorcom,qanal_reits_ebit,qanal_reits_distributedamounts,qanal_reits_income,qanal_reits_ri,qanal_reits_PRI,qanal_reits_pi,stm_is_reits_operatingcost,stm_bs_reits_totaloperatingrevenue,anal_reits_depreciationandamortization,stm_is_reits_taxandsurcharges,stm_is_reits_managementfee,reits_unitdistributableamount,reits_pffo",
                                     "unit=1;rptDate=" + i + ";rptType=1;tradeDate=" + today + ";order=1"
                                     , usedf=True)
            
            if error_code == 0 and not data.empty:
                data['date'] = pd.to_datetime(i)
                # 重置索引，将WindCodes作为列
                data = data.reset_index()
                data_list.append(data)
                print(f"成功获取 {i} 的数据，形状: {data.shape}")
            else:
                print(f"获取 {i} 的数据失败，错误代码: {error_code}")
                
        except Exception as e:
            print(f"获取 {i} 的数据时发生异常: {e}")
            continue
    
    if not data_list:
        print("没有获取到任何数据")
        return pd.DataFrame()
    
    # 合并数据时忽略空数据的警告
    result = pd.concat(data_list, ignore_index=True, sort=False)
    result = result.sort_values(['index', 'date'])  # 先按代码排序，再按日期排序
    #删除result中date值小于FUND__REITSLISTEDDATE的行，注意FUND__REITSLISTEDDATE是一个时间戳
    result = result[result['date'] >= result['FUND__REITSLISTEDDATE']]
    
    # 打印列名以便调试
    print("数据列名:", result.columns.tolist())
    
    # 计算同比增长率（使用正确的列名）
    if 'index' in result.columns:  # Wind数据通常将代码存储在index列中
        groupby_col = 'index'
    elif 'WindCodes' in result.columns:
        groupby_col = 'WindCodes'
    else:
        print("未找到合适的分组列")
        return result
    
    # 检查数据分布情况
    print(f"\n=== 同比增长率计算调试信息 ===")
    print(f"分组列: {groupby_col}")
    print(f"每个代码的数据点数量:")
    print(result.groupby(groupby_col).size())
    
    if 'QANAL_REITS_DISTRIBUTEDAMOUNTS' in result.columns:
        print(f"\n可供分配金额数据概览:")
        print(result.groupby(groupby_col)['QANAL_REITS_DISTRIBUTEDAMOUNTS'].describe())
        
        # 计算同比增长率（4个季度前的数据）
        result['distributable_amount_yoy_growth'] = result.groupby(groupby_col)['QANAL_REITS_DISTRIBUTEDAMOUNTS'].pct_change(periods=4, fill_method=None)
        
        # 检查计算结果
        non_null_growth = result['distributable_amount_yoy_growth'].notna().sum()
        print(f"可供分配金额同比增长率非空值数量: {non_null_growth}")
        
    if 'QANAL_REITS_INCOME' in result.columns:
        print(f"\n本期收入数据概览:")
        print(result.groupby(groupby_col)['QANAL_REITS_INCOME'].describe())
        
        # 计算同比增长率（4个季度前的数据）
        result['current_period_revenue_yoy_growth'] = result.groupby(groupby_col)['QANAL_REITS_INCOME'].pct_change(periods=4, fill_method=None)
        
        # 检查计算结果
        non_null_growth = result['current_period_revenue_yoy_growth'].notna().sum()
        print(f"本期收入同比增长率非空值数量: {non_null_growth}")

    return result


def price_data(df):
    # 市场价格相关的日序列数据
    today = datetime.today().strftime('%Y-%m-%d')
    df['FUND__REITSLISTEDDATE'] = df['FUND__REITSLISTEDDATE'].dt.strftime('%Y-%m-%d')
    all_data = list()
    for code,date in df.iterrows():
        try:
            print(f"正在获取 {code} 的价格数据...")
            # 先尝试基础价格数据       
            error_code, data = w.wsd(code.strip(),
                                     "name_official,fund__reitslisteddate,fund__reitstype,fund__reitsoriginal,fund_reitsrproperty,fund__reitsorcom,val_mv_ARD,reits_distributionratio,reits_pffo,nav,turn,close,reits_cbvalyield",
                                     date.values[0], today, "unit=1;order=1;PriceAdj=F", usedf=True)
            print(f"基础价格数据获取结果: {error_code}")
            if error_code == 0 and not data.empty:
                # 添加代码列
                data['code'] = code.strip()
                all_data.append(data)
                print(f"成功获取 {code} 的价格数据，形状: {data.shape}")
            else:
                print(f"获取 {code} 价格数据失败，错误代码: {error_code}")
                
        except Exception as e:
            print(f"获取 {code} 价格数据时发生异常: {e}")
            continue
    
    if not all_data:
        print("没有获取到任何价格数据")
        return pd.DataFrame()
    
    # 合并所有数据
    result = pd.concat(all_data, sort=True)
    
    print(f"合并后价格数据形状: {result.shape}")
    print("价格数据列名:", result.columns.tolist())
    
    # 计算衍生指标（确保列存在）
    if 'NAV' in result.columns and 'CLOSE' in result.columns:
        result['nav_premium_rate'] = result['NAV'] / result['CLOSE'] - 1
    
    result = fillna_by_date(result)
    return result


def save_data_to_csv(df1, df2):
    """保存数据到CSV文件"""
    try:
        # 获取当前脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 获取当前时间戳
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存财报数据
        if not df1.empty:
            filename1 = os.path.join(script_dir, f'reits_financial_data_{timestamp}.csv')
            df1.to_csv(filename1, index=False, encoding='utf-8-sig')
            print(f"财报数据已保存到: {filename1}")
            print(f"文件大小: {os.path.getsize(filename1)} 字节")
        
        # 保存价格数据
        if not df2.empty:
            filename2 = os.path.join(script_dir, f'reits_price_data_{timestamp}.csv')
            df2.to_csv(filename2, index=True, encoding='utf-8-sig')  # 价格数据保留日期索引
            print(f"价格数据已保存到: {filename2}")
            print(f"文件大小: {os.path.getsize(filename2)} 字节")
        
        return filename1 if not df1.empty else None, filename2 if not df2.empty else None
        
    except Exception as e:
        print(f"保存CSV文件时发生异常: {e}")
        return None, None


def analyze_growth_data(df):
    """分析同比增长率数据"""
    if df.empty:
        return
    
    print("\n=== 同比增长率数据分析 ===")
    
    # 分析可供分配金额同比增长率
    if 'distributable_amount_yoy_growth' in df.columns:
        growth_col = df['distributable_amount_yoy_growth']
        print(f"可供分配金额同比增长率:")
        print(f"  - 总数据点: {len(growth_col)}")
        print(f"  - 非空数据点: {growth_col.notna().sum()}")
        print(f"  - 空值数据点: {growth_col.isna().sum()}")
        
        if growth_col.notna().sum() > 0:
            print(f"  - 最小值: {growth_col.min():.4f}")
            print(f"  - 最大值: {growth_col.max():.4f}")
            print(f"  - 平均值: {growth_col.mean():.4f}")
            
            # 显示有数据的样本
            valid_data = df[growth_col.notna()][['index', 'date', 'QANAL_REITS_DISTRIBUTEDAMOUNTS', 'distributable_amount_yoy_growth']].head(10)
            print(f"  - 前10个有效数据样本:")
            print(valid_data)
    
    # 分析本期收入同比增长率
    if 'current_period_revenue_yoy_growth' in df.columns:
        growth_col = df['current_period_revenue_yoy_growth']
        print(f"\n本期收入同比增长率:")
        print(f"  - 总数据点: {len(growth_col)}")
        print(f"  - 非空数据点: {growth_col.notna().sum()}")
        print(f"  - 空值数据点: {growth_col.isna().sum()}")
        
        if growth_col.notna().sum() > 0:
            print(f"  - 最小值: {growth_col.min():.4f}")
            print(f"  - 最大值: {growth_col.max():.4f}")
            print(f"  - 平均值: {growth_col.mean():.4f}")
            
            # 显示有数据的样本
            valid_data = df[growth_col.notna()][['index', 'date', 'QANAL_REITS_INCOME', 'current_period_revenue_yoy_growth']].head(10)
            print(f"  - 前10个有效数据样本:")
            print(valid_data)


def main():
    try:
        print("开始获取REITs数据...")
        
        # 获取REITs上市日期
        # 使用相对路径确保能找到Excel文件
        excel_file_path = os.path.join(os.path.dirname(__file__), 'REITs基本资料20250717.xlsx')
        code_df = pd.read_excel(excel_file_path)
        code_data = ','.join(code_df[code_df['资产类型'].str.contains('园区基础设施', na=False)]['代码'].to_list())
        date,data = get_reits_date(code_data)
        print(f"REITs最早上市日期: {date}")
        
        # 获取财报数据
        print("\n获取财报数据...")
        df1 = reits_data(code_data, date.year)
        
        # 获取价格数据
        print("\n获取价格数据...")
        df2 = price_data(data)
        
        print("\n=== 财报数据概览 ===")
        if not df1.empty:
            print(f"财报数据形状: {df1.shape}")
            print("前5行数据:")
            print(df1.head())
            
            # 分析同比增长率数据
            analyze_growth_data(df1)
        else:
            print("财报数据为空")
        
        print("\n=== 价格数据概览 ===")
        if not df2.empty:
            print(f"价格数据形状: {df2.shape}")
            print("前5行数据:")
            print(df2.head())
        else:
            print("价格数据为空")
        
        # 保存数据到CSV文件
        print("\n=== 保存数据到CSV文件 ===")
        file1, file2 = save_data_to_csv(df1, df2)
        
        if file1 or file2:
            print("数据保存完成！")
        else:
            print("数据保存失败！")
            
    except Exception as e:
        print(f"主程序执行异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
