<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>园区基础设施REITs分析框架</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Noto Sans SC', sans-serif; background-color: #f8fafc; }
        .chart-container { position: relative; width: 100%; height: 380px; }
        .brilliant-blues-bg { background-color: #004AAD; }
        .brilliant-blues-text { color: #004AAD; }
        .card { background-color: #FFFFFF; border-radius: 0.75rem; box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); padding: 1.5rem; display: flex; flex-direction: column; height: 100%; }
        .control-panel { background-color: #f1f5f9; padding: 0.75rem; border-radius: 0.5rem; margin-bottom: 1rem; }
        table { width: 100%; font-size: 0.875rem; }
        th, td { padding: 0.5rem 0.75rem; text-align: left; border-bottom: 1px solid #e5e7eb; }
        th { background-color: #f9fafb; font-weight: 500; }
        .date-slider-container { padding-top: 1.5rem; }
        .slider-track { position: relative; width: 100%; height: 6px; background-color: #e5e7eb; border-radius: 3px; }
        .slider-range { position: absolute; height: 100%; background-color: #004AAD; border-radius: 3px; }
        .slider-handle { position: absolute; top: -5px; width: 16px; height: 16px; background-color: #fff; border: 2px solid #004AAD; border-radius: 50%; cursor: grab; }
        .slider-handle:active { cursor: grabbing; }
        .date-labels { display: flex; justify-content: space-between; font-size: 0.75rem; color: #6b7280; margin-top: 0.5rem; }
        #global-selector-panel label { display: block; padding: 0.5rem; cursor: pointer; border-radius: 0.25rem; }
        #global-selector-panel label:hover { background-color: #f3f4f6; }
    </style>
</head>
<body class="text-gray-800">

    <header class="brilliant-blues-bg text-white p-6 text-center">
        <h1 class="text-3xl md:text-4xl">园区基础设施REITs分析框架</h1>
    </header>

    <div class="container mx-auto p-4 md:p-6">
        <div class="grid grid-cols-1 lg:grid-cols-4 lg:items-start gap-6">
            
            <main class="lg:col-span-3 flex flex-col gap-6">
                <section id="recent-performance" class="card">
                    <h2 class="text-2xl text-center mb-4 brilliant-blues-text">标的近期表现</h2>
                    <div class="overflow-x-auto">
                        <table id="performance-table"></table>
                    </div>
                </section>

                <section id="market-performance" class="card">
                    <h2 class="text-2xl text-center mb-4 brilliant-blues-text">市场表现：价格与换手率</h2>
                    <div class="chart-container"><canvas id="price-turnover-chart"></canvas></div>
                    <div id="price-slider-container"></div>
                </section>

                <section id="financials" class="card">
                    <h2 class="text-2xl text-center mb-4 brilliant-blues-text">核心财务数据对比</h2>
                    <div class="control-panel flex flex-wrap items-center gap-4">
                        <div class="flex items-center space-x-4">
                            <label><input type="radio" name="financials-period" value="q" checked> 季度</label>
                            <label><input type="radio" name="financials-period" value="stm"> 半年/年度</label>
                        </div>
                         <div>
                            <label for="financials-metric-select" class="text-sm font-medium">指标:</label>
                            <select id="financials-metric-select" class="p-1 border rounded"></select>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table id="financials-table"></table>
                    </div>
                    <div class="chart-container mt-4"><canvas id="financials-chart"></canvas></div>
                </section>

                <section id="income-structure" class="card">
                    <h2 class="text-2xl text-center mb-4 brilliant-blues-text">收入结构分析</h2>
                    <div class="control-panel flex flex-wrap items-center justify-end gap-4">
                        <div class="flex items-center space-x-4">
                            <label><input type="radio" name="income-view" value="absolute" checked> 绝对值</label>
                            <label><input type="radio" name="income-view" value="percentage"> 占比</label>
                        </div>
                    </div>
                    <div class="chart-container"><canvas id="income-chart"></canvas></div>
                </section>

                <section id="valuation" class="card">
                    <h2 class="text-2xl text-center mb-4 brilliant-blues-text">多维估值透视</h2>
                    <div class="control-panel">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div><label for="bubble-x-axis" class="text-sm font-medium">X轴:</label><select id="bubble-x-axis" class="p-1 border rounded w-full"></select></div>
                            <div><label for="bubble-y-axis" class="text-sm font-medium">Y轴:</label><select id="bubble-y-axis" class="p-1 border rounded w-full"></select></div>
                            <div><label for="bubble-size" class="text-sm font-medium">气泡大小:</label><select id="bubble-size" class="p-1 border rounded w-full"></select></div>
                        </div>
                    </div>
                    <div class="chart-container"><canvas id="reits-bubble-chart"></canvas></div>
                </section>

                <section id="comparison" class="card">
                    <h2 class="text-2xl text-center mb-4 brilliant-blues-text">横向对比分析</h2>
                    <div class="control-panel">
                         <div class="grid grid-cols-2 md:grid-cols-4 gap-4" id="radar-metrics-selectors"></div>
                    </div>
                    <div class="chart-container"><canvas id="radar-chart"></canvas></div>
                </section>
            </main>

            <aside class="lg:col-span-1">
                <div class="sticky top-6 card">
                    <h3 class="text-lg font-semibold border-b pb-2 mb-3">选择分析标的</h3>
                    <div id="global-selector-panel" class="space-y-1"></div>
                </div>
            </aside>
        </div>
    </footer>

    <script type="module">
        const MOCK_DATA = {
            reits: [
                { id: '180101', name: '博时蛇口' }, { id: '180102', name: '华夏合肥' },
                { id: '180103', name: '华夏和达' }, { id: '508000', name: '华安张江' },
                { id: '508027', name: '东吴苏园' }, { id: '508099', name: '建信中关村' }
            ],
            daily: {}, financials_q: {}, financials_stm: {}, valuation: {}
        };

        function generateDailyData() {
            const data = { labels: [], prices: [], turnovers: [] };
            let price = 2.5 + (Math.random() - 0.5);
            for (let i = 240; i > 0; i--) {
                const date = new Date('2025-08-17T00:00:00'); date.setDate(date.getDate() - i);
                data.labels.push(date.toISOString().split('T')[0]);
                price *= (1 + (Math.random() - 0.5) * 0.03);
                data.prices.push(parseFloat(price.toFixed(2)));
                data.turnovers.push(parseFloat((Math.random() * 2 + 0.5).toFixed(2)));
            }
            return data;
        }

        function generateFinancials() {
            const q_labels = ['2024Q2', '2024Q3', '2024Q4', '2025Q1', '2025Q2'];
            const q_data = { labels: q_labels, datasets: {} };
            const stm_data = { labels: ['2023H2', '2024H1', '2024H2'], datasets: {} };
            MOCK_DATA.reits.forEach(reit => {
                q_data.datasets[reit.id] = {}; stm_data.datasets[reit.id] = {};
                q_labels.forEach(p => {
                    const income = 2000 + Math.random() * 600, ri = income * (0.8 + Math.random() * 0.1), pri = income * (0.05 + Math.random() * 0.05), pi = income - ri - pri;
                    q_data.datasets[reit.id][p] = { ebit: 1500 + Math.random() * 500, distributed: 1200 + Math.random() * 400, income, ri, pri, pi };
                });
                stm_data.labels.forEach(p => { stm_data.datasets[reit.id][p] = { op_revenue: 4000 + Math.random() * 1000, op_cost: 800 + Math.random() * 200, depreciation: 200 + Math.random() * 50, tax: 50 + Math.random() * 20, manage_fee: 100 + Math.random() * 50 }; });
            });
            MOCK_DATA.financials_q = q_data; MOCK_DATA.financials_stm = stm_data;
        }

        MOCK_DATA.reits.forEach(reit => {
            MOCK_DATA.daily[reit.id] = generateDailyData();
            MOCK_DATA.valuation[reit.id] = { pffo: 20 + Math.random() * 10, dist_ratio: 3 + Math.random() * 3, mv: 20 + Math.random() * 20, nav_premium: (Math.random() - 0.2) * 20 };
        });
        generateFinancials();

        let priceTurnoverChart, financialsChart, bubbleChart, radarChart, incomeChart;
        const PALETTE = ['#004AAD', '#0094D1', '#78C5E8', '#F7931E', '#F15A24', '#D4145A'];
        const INCOME_PALETTE = { ri: '#004AAD', pri: '#78C5E8', pi: '#F7931E' };
        
        function createDateSlider(containerId, allLabels, onChangeCallback) {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="date-slider-container"><div class="slider-track"><div class="slider-range"></div><div class="slider-handle" data-handle="start"></div><div class="slider-handle" data-handle="end"></div></div><div class="date-labels"><span class="start-date-label"></span><span class="end-date-label"></span></div></div>`;
            const track = container.querySelector('.slider-track'), range = container.querySelector('.slider-range'), startHandle = container.querySelector('[data-handle="start"]'), endHandle = container.querySelector('[data-handle="end"]'), startDateLabel = container.querySelector('.start-date-label'), endDateLabel = container.querySelector('.end-date-label');
            let min = 0, max = allLabels.length - 1, currentMin = max > 90 ? max - 90 : min, currentMax = max;
            const updateSliderUI = () => {
                const minPercent = (currentMin / max) * 100, maxPercent = (currentMax / max) * 100;
                startHandle.style.left = `calc(${minPercent}% - 8px)`; endHandle.style.left = `calc(${maxPercent}% - 8px)`;
                range.style.left = `${minPercent}%`; range.style.width = `${maxPercent - minPercent}%`;
                startDateLabel.textContent = allLabels[currentMin]; endDateLabel.textContent = allLabels[currentMax];
            };
            const makeDraggable = (handle) => {
                handle.onmousedown = (e) => {
                    e.preventDefault();
                    document.onmousemove = (moveEvent) => {
                        const rect = track.getBoundingClientRect(); let newPos = (moveEvent.clientX - rect.left) / rect.width; newPos = Math.max(0, Math.min(1, newPos));
                        const newIndex = Math.round(newPos * max);
                        if (handle.dataset.handle === 'start') { currentMin = Math.min(newIndex, currentMax - 1); } else { currentMax = Math.max(newIndex, currentMin + 1); }
                        updateSliderUI();
                    };
                    document.onmouseup = () => { document.onmousemove = document.onmouseup = null; onChangeCallback({ startIndex: currentMin, endIndex: currentMax }); };
                };
            };
            makeDraggable(startHandle); makeDraggable(endHandle); updateSliderUI();
            return { getRange: () => ({ startIndex: currentMin, endIndex: currentMax }) };
        }

        const commonChartOptions = { responsive: true, maintainAspectRatio: false, interaction: { mode: 'index', intersect: false }, plugins: { legend: { position: 'top' } } };

        function updateRecentPerformanceTable(selectedIds) {
            const table = document.getElementById('performance-table');
            let tableHTML = `<thead><tr><th>标的</th><th>周涨跌幅</th><th>月涨跌幅</th><th>年初至今</th></tr></thead><tbody>`;
            selectedIds.forEach(id => {
                const p = MOCK_DATA.daily[id].prices, l = MOCK_DATA.daily[id].labels;
                const today = p[p.length - 1], week = p[p.length - 8] || p[0], month = p[p.length - 31] || p[0];
                const ytdIdx = l.findIndex(d => d.startsWith('2025-01')), ytd = ytdIdx > -1 ? p[ytdIdx] : p[0];
                const wc = ((today / week - 1) * 100).toFixed(2), mc = ((today / month - 1) * 100).toFixed(2), yc = ((today / ytd - 1) * 100).toFixed(2);
                tableHTML += `<tr><td>${MOCK_DATA.reits.find(r=>r.id===id).name}</td><td>${wc}%</td><td>${mc}%</td><td>${yc}%</td></tr>`;
            });
            table.innerHTML = tableHTML + '</tbody>';
        }

        function updateFinancials(selectedIds) {
            const periodType = document.querySelector('input[name="financials-period"]:checked').value;
            const table = document.getElementById('financials-table');
            const canvas = document.getElementById('financials-chart');
            const metricSelect = document.getElementById('financials-metric-select');
            if (!canvas) { console.error('Canvas for financials chart not found'); return; }

            const data = periodType === 'q' ? MOCK_DATA.financials_q : MOCK_DATA.financials_stm;
            const metrics = periodType === 'q' ? {income: '收入', ebit: 'EBITDA', distributed: '可供分配'} : {op_revenue: '总收入', op_cost: '成本', manage_fee: '管理费', depreciation: '折旧', tax: '税金'};
            
            const currentMetric = metricSelect.value;
            metricSelect.innerHTML = Object.keys(metrics).map(key => `<option value="${key}" ${key === currentMetric ? 'selected' : ''}>${metrics[key]}</option>`).join('');
            const selectedMetric = metricSelect.value;

            let tableHTML = `<thead><tr><th>标的</th><th>期间</th>${Object.values(metrics).map(n => `<th>${n}</th>`).join('')}</tr></thead><tbody>`;
            selectedIds.forEach(id => {
                const reitName = MOCK_DATA.reits.find(r => r.id === id).name;
                data.labels.forEach((label, i) => {
                    tableHTML += `<tr>${i === 0 ? `<td rowspan="${data.labels.length}">${reitName}</td>` : ''}<td>${label}</td>`;
                    Object.keys(metrics).forEach(m => { tableHTML += `<td>${data.datasets[id][label][m].toFixed(0)}</td>`; });
                    tableHTML += `</tr>`;
                });
            });
            table.innerHTML = tableHTML + '</tbody>';

            const chartDatasets = selectedIds.map((id, index) => ({ label: MOCK_DATA.reits.find(r=>r.id===id).name, data: data.labels.map(l => data.datasets[id][l][selectedMetric]), backgroundColor: PALETTE[index % PALETTE.length] }));
            if (financialsChart) financialsChart.destroy();
            financialsChart = new Chart(canvas, { type: 'bar', data: { labels: data.labels, datasets: chartDatasets }, options: { ...commonChartOptions, plugins: { ...commonChartOptions.plugins, title: { display: true, text: `${metrics[selectedMetric]}对比(百万)` } } } });
        }
        
        function updateIncomeStructureChart(selectedIds) {
            const canvas = document.getElementById('income-chart');
            if (!canvas) { console.error('Canvas for income chart not found'); return; }
            const viewType = document.querySelector('input[name="income-view"]:checked').value;
            const incomeTypes = { ri: '租金收入', pri: '物业收入', pi: '停车费收入' };
            const datasets = [];

            selectedIds.forEach(id => {
                const reitData = MOCK_DATA.financials_q.datasets[id];
                const reitName = MOCK_DATA.reits.find(r => r.id === id).name;
                Object.keys(incomeTypes).forEach(key => {
                    datasets.push({
                        label: incomeTypes[key],
                        data: MOCK_DATA.financials_q.labels.map(label => {
                            const periodData = reitData[label];
                            return viewType === 'percentage' ? (periodData[key] / periodData.income * 100) : periodData[key];
                        }),
                        stack: reitName,
                        backgroundColor: INCOME_PALETTE[key]
                    });
                });
            });

            if (incomeChart) incomeChart.destroy();
            incomeChart = new Chart(canvas, { 
                type: 'bar', 
                data: { labels: MOCK_DATA.financials_q.labels, datasets }, 
                options: { 
                    ...commonChartOptions, 
                    scales: { 
                        x: { stacked: true }, 
                        y: { stacked: true, ticks: { callback: v => viewType === 'percentage' ? `${v.toFixed(0)}%` : v } } 
                    }, 
                    plugins: { 
                        ...commonChartOptions.plugins, 
                        title: { display: true, text: `收入结构对比` },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const reitName = context.dataset.stack;
                                    const value = context.formattedValue;
                                    return `${reitName}: ${value}`;
                                }
                            }
                        },
                        legend: {
                            labels: {
                                generateLabels: function(chart) {
                                    return Object.keys(incomeTypes).map(key => ({
                                        text: incomeTypes[key],
                                        fillStyle: INCOME_PALETTE[key],
                                        strokeStyle: INCOME_PALETTE[key],
                                        hidden: false,
                                        index: key
                                    }));
                                }
                            }
                        }
                    } 
                } 
            });
        }

        function updatePriceTurnoverChart(selectedIds, range) {
            const canvas = document.getElementById('price-turnover-chart');
            if (!canvas) { console.error('Canvas for price chart not found'); return; }
            if (selectedIds.length === 0) { if (priceTurnoverChart) priceTurnoverChart.destroy(); return; }
            const { startIndex, endIndex } = range;
            const labels = MOCK_DATA.daily[selectedIds[0]].labels.slice(startIndex, endIndex + 1);
            const datasets = [];
            selectedIds.forEach((id, index) => {
                const color = PALETTE[index % PALETTE.length];
                const reitName = MOCK_DATA.reits.find(r=>r.id===id).name;
                datasets.push({ type: 'line', label: `${reitName} 价格`, data: MOCK_DATA.daily[id].prices.slice(startIndex, endIndex + 1), borderColor: color, yAxisID: 'yPrice', tension: 0.1, pointRadius: 0, borderWidth: 2 });
                datasets.push({ type: 'bar', label: `${reitName} 换手率`, data: MOCK_DATA.daily[id].turnovers.slice(startIndex, endIndex + 1), backgroundColor: color + '40', yAxisID: 'yTurnover' });
            });
            if (priceTurnoverChart) priceTurnoverChart.destroy();
            priceTurnoverChart = new Chart(canvas, { 
                type: 'bar', 
                data: { labels, datasets }, 
                options: { 
                    ...commonChartOptions, 
                    scales: { 
                        yPrice: { type: 'linear', position: 'left', title: { display: true, text: '价格 (元)' } }, 
                        yTurnover: { type: 'linear', position: 'right', title: { display: true, text: '换手率 (%)' }, grid: { drawOnChartArea: false } } 
                    },
                    plugins: {
                        ...commonChartOptions.plugins,
                        legend: {
                            labels: {
                                filter: (item) => item.text.includes('价格')
                            }
                        }
                    }
                } 
            });
        }

        function updateBubbleChart(selectedIds) {
            const canvas = document.getElementById('reits-bubble-chart');
            if (!canvas) { console.error('Canvas for bubble chart not found'); return; }
            const xDim = document.getElementById('bubble-x-axis').value, yDim = document.getElementById('bubble-y-axis').value, sizeDim = document.getElementById('bubble-size').value;
            const datasets = [{ label: '园区REITs', data: selectedIds.map(id => { const val = MOCK_DATA.valuation[id]; return { x: val[xDim], y: val[yDim], r: val[sizeDim] / 2, name: MOCK_DATA.reits.find(r => r.id === id).name }; }), backgroundColor: PALETTE.map(c => c + '80') }];
            if (bubbleChart) bubbleChart.destroy();
            bubbleChart = new Chart(canvas, { type: 'bubble', data: { datasets }, options: { ...commonChartOptions, scales: { x: { title: { display: true, text: document.querySelector(`#bubble-x-axis option[value=${xDim}]`).textContent } }, y: { title: { display: true, text: document.querySelector(`#bubble-y-axis option[value=${yDim}]`).textContent } } }, plugins: { legend: { display: false }, tooltip: { callbacks: { label: (c) => `${c.raw.name}: ${c.raw.x.toFixed(2)}, ${c.raw.y.toFixed(2)}, Size: ${(c.raw.r*2).toFixed(1)}` } } } } });
        }

        function updateRadarChart(selectedIds) {
            const canvas = document.getElementById('radar-chart');
            if (!canvas) { console.error('Canvas for radar chart not found'); return; }
            const selectors = document.querySelectorAll('#radar-metrics-selectors select');
            const selectedMetrics = Array.from(selectors).map(s => s.value);
            const labels = Array.from(selectors).map(s => s.options[s.selectedIndex].text);
            const metricNormalizers = { nav_premium: (v) => 10 - (v / 5), dist_ratio: (v) => v * 1.5, pffo: (v) => v / 3, mv: (v) => v / 5 };
            const datasets = selectedIds.map((id, index) => {
                const val = MOCK_DATA.valuation[id];
                return {
                    label: MOCK_DATA.reits.find(r => r.id === id).name,
                    data: selectedMetrics.map(metric => metricNormalizers[metric](val[metric])),
                    borderColor: PALETTE[index % PALETTE.length],
                    backgroundColor: PALETTE[index % PALETTE.length] + '40',
                };
            });
            if (radarChart) radarChart.destroy();
            radarChart = new Chart(canvas, { type: 'radar', data: { labels, datasets }, options: { ...commonChartOptions, scales: { r: { suggestedMin: 0, suggestedMax: 10 } } } });
        }

        document.addEventListener('DOMContentLoaded', () => {
            const globalSelectorPanel = document.getElementById('global-selector-panel');
            const defaultSelection = ['180101', '180102', '508099'];
            MOCK_DATA.reits.forEach(reit => {
                globalSelectorPanel.innerHTML += `<label><input type="checkbox" value="${reit.id}" ${defaultSelection.includes(reit.id) ? 'checked' : ''}> ${reit.name}</label>`;
            });

            const priceSlider = createDateSlider('price-slider-container', MOCK_DATA.daily[MOCK_DATA.reits[0].id].labels, (range) => {
                const selectedIds = Array.from(globalSelectorPanel.querySelectorAll('input:checked')).map(el => el.value);
                updatePriceTurnoverChart(selectedIds, range);
            });

            const updateAllVisualizations = () => {
                const selectedIds = Array.from(globalSelectorPanel.querySelectorAll('input:checked')).map(el => el.value);
                updateRecentPerformanceTable(selectedIds);
                updateFinancials(selectedIds);
                updateIncomeStructureChart(selectedIds);
                updatePriceTurnoverChart(selectedIds, priceSlider.getRange());
                updateBubbleChart(selectedIds);
                updateRadarChart(selectedIds);
            };

            globalSelectorPanel.addEventListener('change', updateAllVisualizations);
            
            const bubbleDims = [{ value: 'pffo', text: 'P/FFO' }, { value: 'dist_ratio', text: '年化派息率 (%)' }, { value: 'mv', text: '市值 (亿元)' }, { value: 'nav_premium', text: 'NAV溢价率 (%)' }];
            const xSelect = document.getElementById('bubble-x-axis'), ySelect = document.getElementById('bubble-y-axis'), sizeSelect = document.getElementById('bubble-size');
            bubbleDims.forEach(d => { xSelect.innerHTML += `<option value="${d.value}">${d.text}</option>`; ySelect.innerHTML += `<option value="${d.value}">${d.text}</option>`; sizeSelect.innerHTML += `<option value="${d.value}">${d.text}</option>`; });
            xSelect.value = 'pffo'; ySelect.value = 'dist_ratio'; sizeSelect.value = 'mv';
            
            const radarMetricsContainer = document.getElementById('radar-metrics-selectors');
            const radarDims = [{ value: 'nav_premium', text: '价值(折价)' }, { value: 'dist_ratio', text: '收益(分派率)' }, { value: 'pffo', text: '成长(P/FFO)' }, { value: 'mv', text: '规模(市值)' }];
            for (let i = 0; i < 4; i++) {
                const selectorWrapper = document.createElement('div');
                let optionsHTML = radarDims.map(d => `<option value="${d.value}" ${ i === radarDims.indexOf(d) ? 'selected' : ''}>${d.text}</option>`).join('');
                selectorWrapper.innerHTML = `<label class="text-sm">维度 ${i+1}:</label><select class="p-1 border rounded w-full">${optionsHTML}</select>`;
                radarMetricsContainer.appendChild(selectorWrapper);
            }
            
            document.querySelectorAll('input[name="financials-period"], input[name="income-view"]').forEach(el => el.addEventListener('change', updateAllVisualizations));
            document.getElementById('financials-metric-select').addEventListener('change', updateAllVisualizations);
            [xSelect, ySelect, sizeSelect].forEach(sel => sel.addEventListener('change', updateAllVisualizations));
            radarMetricsContainer.addEventListener('change', updateAllVisualizations);

            updateAllVisualizations();
        });
    </script>
</body>
</html>
