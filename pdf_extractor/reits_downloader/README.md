# REITs报告下载器

## 文件说明

### 核心文件
- `report_download.py` - 主程序，包含所有下载和筛选功能
- `REITs基本资料20250717.xlsx` - 基础数据文件，包含证券代码和询价时间
- `CONFIG_GUIDE.md` - 详细配置指南
- `test_config.py` - 配置功能测试脚本

### 输出目录
- `Download/` - 所有下载的PDF文件和记录文件都保存在此目录

## 快速开始

1. **运行主程序**：
   ```bash
   python report_download.py
   ```

2. **测试配置功能**：
   ```bash
   python test_config.py
   ```

## 主要功能

### 1. 智能文件管理
- ✅ 自动检查本地文件，避免重复下载
- ✅ 智能文件名处理和PDF格式验证
- ✅ 按基金名称自动分类存储

### 2. 灵活筛选配置
- ✅ **API搜索关键词**：`API_SEARCH_KEYWORD = '度报告'` - 用于从服务器获取报告列表
- ✅ **二次筛选**：`FILTER_TYPE` - 对API返回结果进行精确筛选
  - `'quarterly'`: 只保留季度报告
  - `'annual'`: 只保留年度报告  
  - `'all_reports'`: 保留所有报告（排除提示性）
  - `'custom'`: 使用自定义筛选条件

### 3. 日期范围选择
- ✅ **默认模式**：`DATE_RANGE_TYPE = 'default'` - 使用文件中的询价时间（推荐）
- ✅ **预设范围**：`'last_year'`, `'last_6months'`, `'last_3months'`, `'ytd'`
- ✅ **自定义范围**：`'custom'` - 指定具体日期区间

## 配置说明

### 关键词配置层次
```
API搜索 → 服务器返回结果 → 本地筛选 → 最终下载
   ↓              ↓           ↓
API_SEARCH_KEYWORD  →  FILTER_TYPE  →  PDF文件
```

1. **API_SEARCH_KEYWORD**：向服务器请求时使用的关键词
2. **FILTER_TYPE**：对服务器返回结果进行二次筛选

这样设计避免了关键词冲突，提供了更精确的控制。

## 示例配置

### 默认配置（推荐）
```python
DATE_RANGE_TYPE = 'default'      # 使用询价时间
API_SEARCH_KEYWORD = '度报告'    # API搜索关键词
FILTER_TYPE = 'all_reports'      # 接受所有报告，只排除提示性
SKIP_EXISTING_FILES = True       # 跳过已存在文件
```

### 只下载季度报告
```python
DATE_RANGE_TYPE = 'last_year'    # 最近一年
FILTER_TYPE = 'quarterly'        # 只要季度报告
```

### 自定义筛选
```python
FILTER_TYPE = 'custom'
CUSTOM_KEYWORDS = ['年报', '季报']
CUSTOM_EXCLUDE = ['提示性', '更正', '补充']
```

## 输出文件

### 下载的PDF文件
```
Download/
├── 华安张江产业园封闭式基础设施证券投资基金/
│   ├── 华安张江产业园...2024年第1季度报告.pdf
│   └── 华安张江产业园...2024年年度报告.pdf
└── 华夏合肥高新创新产业园封闭式基础设施证券投资基金/
    └── ...
```

### 记录文件
- `下载失败记录_YYYYMMDD_HHMMSS.xlsx` - 详细的失败记录
- `无报告记录_YYYYMMDD_HHMMSS.xlsx` - 没有找到报告的标的记录

## 注意事项

1. **Excel文件路径**：确保 `REITs基本资料20250717.xlsx` 在同一目录
2. **网络连接**：需要稳定的网络连接访问API
3. **API Token**：确保代码中的refresh_token有效
4. **磁盘空间**：确保有足够空间存储PDF文件

## 故障排除

1. **运行测试**：`python test_config.py`
2. **检查配置**：参考 `CONFIG_GUIDE.md`
3. **查看日志**：程序会输出详细的处理过程
4. **检查记录文件**：查看失败记录了解具体问题