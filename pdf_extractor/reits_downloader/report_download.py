# -*- coding: utf-8 -*-
import requests
import json
import pandas as pd
import os
from datetime import datetime, timedelta
import re
from typing import List, Tuple, Dict, Optional


# 报告筛选器配置
class ReportFilter:
    """报告筛选器，支持多种筛选条件"""
    
    def __init__(self):
        self.filters = {
            'quarterly': {
                'keywords': ['季度报告', '第1季度', '第2季度', '第3季度', '第4季度'],
                'exclude': ['提示性']
            },
            'annual': {
                'keywords': ['年度报告', '年报'],
                'exclude': ['提示性']
            },
            'all_reports': {
                'keywords': [],  # 不进行关键词筛选，接受所有API返回的报告
                'exclude': ['提示性']  # 只排除提示性公告
            },
            'custom': {
                'keywords': [],
                'exclude': []
            }
        }
    
    def filter_reports(self, titles: List[str], urls: List[str], filter_type: str = 'all_reports', 
                      custom_keywords: List[str] = None, custom_exclude: List[str] = None) -> Tuple[List[str], List[str]]:
        """
        筛选报告
        
        Args:
            titles: 报告标题列表
            urls: 报告URL列表
            filter_type: 筛选类型 ('quarterly', 'annual', 'all_reports', 'custom')
            custom_keywords: 自定义包含关键词
            custom_exclude: 自定义排除关键词
        
        Returns:
            筛选后的标题和URL列表
        """
        if filter_type == 'custom':
            keywords = custom_keywords or []
            exclude = custom_exclude or []
        else:
            config = self.filters.get(filter_type, self.filters['all_reports'])
            keywords = config['keywords']
            exclude = config['exclude']
        
        filtered = []
        for title, url in zip(titles, urls):
            # 检查是否包含必需关键词
            if keywords and not any(keyword in title for keyword in keywords):
                continue
            
            # 检查是否包含排除关键词
            if exclude and any(exc in title for exc in exclude):
                continue
            
            filtered.append((title, url))
        
        if filtered:
            return zip(*filtered)
        else:
            return [], []


class DateRangeManager:
    """日期范围管理器"""
    
    @staticmethod
    def get_date_range(range_type: str, custom_start: str = None, custom_end: str = None) -> Tuple[str, str]:
        """
        获取日期范围
        
        Args:
            range_type: 范围类型 ('default', 'last_year', 'last_6months', 'last_3months', 'ytd', 'custom')
            custom_start: 自定义开始日期 (YYYY-MM-DD)
            custom_end: 自定义结束日期 (YYYY-MM-DD)
        
        Returns:
            开始日期和结束日期的元组，如果是default模式则返回None表示使用询价时间
        """
        today = datetime.today()
        
        if range_type == 'default':
            # 返回None表示使用询价时间模式
            return None, None
        elif range_type == 'last_year':
            start_date = today - timedelta(days=365)
        elif range_type == 'last_6months':
            start_date = today - timedelta(days=180)
        elif range_type == 'last_3months':
            start_date = today - timedelta(days=90)
        elif range_type == 'ytd':  # Year to date
            start_date = datetime(today.year, 1, 1)
        elif range_type == 'custom':
            if custom_start and custom_end:
                return custom_start, custom_end
            else:
                raise ValueError("自定义日期范围需要提供开始和结束日期")
        else:
            # 默认使用最近一年
            start_date = today - timedelta(days=365)
        
        return start_date.strftime('%Y-%m-%d'), today.strftime('%Y-%m-%d')


class ths_api:
    def __init__(self):
        # Token accessToken 及权限校验机制
        self.getAccessTokenUrl = 'https://quantapi.51ifind.com/api/v1/get_access_token'
        # 获取refresh_token需下载Windows版本接口包解压，打开超级命令-工具-refresh_token查询
        self.refreshtoken = 'eyJzaWduX3RpbWUiOiIyMDI1LTA4LTEwIDIzOjQ0OjA3In0=.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.7D577D71F4A6F0D53AC7A796147CEFBA099B817A4A3C541CC0B37CD812DA4837'
        self.getAccessTokenHeader = {"Content-Type": "application/json", "refresh_token": self.refreshtoken}
        self.getAccessTokenResponse = requests.post(url=self.getAccessTokenUrl, headers=self.getAccessTokenHeader)
        self.accessToken = json.loads(self.getAccessTokenResponse.content)['data']['access_token']
        self.thsHeaders = {
            "Content-Type": "application/json",
            "access_token": self.accessToken}
    
    def report_download(self, report_title: str, code: str, start_date: str, end_date: str = None) -> Tuple[List[str], List[str]]:
        """
        下载报告
        
        Args:
            report_title: 报告关键词
            code: 证券代码
            start_date: 开始日期
            end_date: 结束日期，如果为None则使用今天
        
        Returns:
            PDF URL列表和标题列表的元组
        """
        try:
            if end_date is None:
                end_date = datetime.today().strftime('%Y-%m-%d')
                
            report_download_url = 'https://quantapi.51ifind.com/api/v1/report_query'
            report_download_para = {
                "codes": code,
                "functionpara": {"keyWord": report_title, "reportType": "906"},
                "beginrDate": start_date,
                "endrDate": end_date,
                "outputpara": "reportTitle:Y,pdfURL:Y"
            }
            
            report_download_response = requests.post(url=report_download_url, json=report_download_para,
                                                     headers=self.thsHeaders)
            report_download_data = json.loads(report_download_response.content)
            pdf_url = report_download_data['tables'][0]['table']['pdfURL']
            pdf_title = report_download_data['tables'][0]['table']['reportTitle']
            return pdf_url, pdf_title
        except Exception as e:
            print(f"下载报告失败: {e}")
            return [], []


def get_filename_from_url_or_header(url, response, title):
    """从URL或响应头中获取文件名，如果都没有则使用标题"""
    filename = None
    
    # 1. 尝试从Content-Disposition头获取文件名
    content_disposition = response.headers.get('content-disposition', '')
    if content_disposition:
        import re
        filename_match = re.search(r'filename[*]?=([^;]+)', content_disposition)
        if filename_match:
            filename = filename_match.group(1).strip('"\'')
    
    # 2. 如果没有，尝试从URL获取文件名
    if not filename:
        from urllib.parse import urlparse, unquote
        parsed_url = urlparse(url)
        url_filename = unquote(parsed_url.path.split('/')[-1])
        if url_filename and '.' in url_filename:
            filename = url_filename
    
    # 3. 如果还是没有，使用标题作为文件名
    if not filename:
        filename = f"{title.strip()}.pdf"
    
    # 4. 确保文件有正确的扩展名
    if not filename.lower().endswith('.pdf'):
        filename += '.pdf'
    
    # 5. 清理文件名中的非法字符
    filename = "".join(c for c in filename if c not in r'\/:*?"<>|')
    
    return filename


def check_file_exists(file_path: str, title: str) -> bool:
    """
    检查文件是否已存在且有效
    
    Args:
        file_path: 文件路径
        title: 报告标题（用于模糊匹配）
    
    Returns:
        文件是否存在且有效
    """
    # 检查精确文件名
    if os.path.exists(file_path) and os.path.getsize(file_path) > 1024:
        with open(file_path, 'rb') as f:
            header = f.read(4)
            if header == b'%PDF':
                return True
    
    # 检查同目录下是否有相似文件名的文件
    directory = os.path.dirname(file_path)
    if os.path.exists(directory):
        # 提取标题中的关键信息用于匹配
        title_clean = re.sub(r'[^\w\u4e00-\u9fff]', '', title)  # 只保留字母数字和中文
        
        for existing_file in os.listdir(directory):
            if existing_file.endswith('.pdf'):
                existing_clean = re.sub(r'[^\w\u4e00-\u9fff]', '', existing_file)
                # 如果文件名相似度很高，认为是同一个文件
                if title_clean in existing_clean or existing_clean in title_clean:
                    existing_path = os.path.join(directory, existing_file)
                    if os.path.getsize(existing_path) > 1024:
                        with open(existing_path, 'rb') as f:
                            header = f.read(4)
                            if header == b'%PDF':
                                return True
    
    return False


def download_pdf(pdf_url: List[str], pdf_title: List[str], folder_name: str, code: str, 
                skip_existing: bool = True) -> Tuple[int, List[Dict]]:
    """
    下载PDF文件
    
    Args:
        pdf_url: PDF URL列表
        pdf_title: PDF标题列表
        folder_name: 文件夹名称
        code: 证券代码
        skip_existing: 是否跳过已存在的文件
    
    Returns:
        成功下载数量和失败记录列表的元组
    """
    # 创建Download主目录
    download_dir = "Download"
    os.makedirs(download_dir, exist_ok=True)
    
    # 在Download目录下创建具体的基金文件夹
    full_path = os.path.join(download_dir, folder_name)
    os.makedirs(full_path, exist_ok=True)
    
    successful_downloads = 0
    failed_records = []
    skipped_count = 0
    
    for url, title in zip(pdf_url, pdf_title):
        try:
            # 获取预期的文件名（不下载，只是为了检查）
            temp_filename = f"{title.strip()}.pdf"
            temp_filename = "".join(c for c in temp_filename if c not in r'\/:*?"<>|')
            temp_save_path = os.path.join(full_path, temp_filename)
            
            # 检查文件是否已存在
            if skip_existing and check_file_exists(temp_save_path, title):
                print(f"跳过已存在: {title}")
                skipped_count += 1
                successful_downloads += 1  # 计入成功数量
                continue
            
            print(f"正在下载: {title}")
            
            # 发送HEAD请求先获取文件信息
            try:
                head_response = requests.head(url, timeout=10, allow_redirects=True)
                content_length_header = head_response.headers.get('content-length')
                if content_length_header and int(content_length_header) == 0:
                    print(f"警告: {title} - 服务器返回文件大小为0，跳过下载")
                    failed_records.append({
                        '代码': code,
                        '报告标题': title,
                        'PDF链接': url,
                        '失败原因': '服务器返回文件大小为0'
                    })
                    continue
            except:
                # 如果HEAD请求失败，继续尝试GET请求
                pass
            
            # 发送GET请求下载文件
            response = requests.get(url, timeout=30, stream=True)
            response.raise_for_status()
            
            # 获取合适的文件名
            filename = get_filename_from_url_or_header(url, response, title)
            save_path = os.path.join(full_path, filename)
            
            # 检查响应内容大小
            content_length = response.headers.get('content-length')
            if content_length and int(content_length) == 0:
                print(f"警告: {title} - 响应头显示文件大小为0，跳过保存")
                failed_records.append({
                    '代码': code,
                    '报告标题': title,
                    'PDF链接': url,
                    '失败原因': '响应头显示文件大小为0'
                })
                continue
            
            # 流式下载文件
            total_size = 0
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        total_size += len(chunk)
            
            # 验证下载的文件
            if total_size == 0:
                print(f"错误: {title} - 下载的文件为空，删除文件")
                os.remove(save_path)
                failed_records.append({
                    '代码': code,
                    '报告标题': title,
                    'PDF链接': url,
                    '失败原因': '下载的文件为空'
                })
                continue
            elif total_size < 1024:
                print(f"警告: {title} - 文件大小异常小 ({total_size} bytes)")
            
            # 验证PDF文件头
            with open(save_path, 'rb') as f:
                header = f.read(4)
                if header != b'%PDF':
                    print(f"警告: {title} - 文件可能不是有效的PDF格式")
                    failed_records.append({
                        '代码': code,
                        '报告标题': title,
                        'PDF链接': url,
                        '失败原因': '文件不是有效的PDF格式'
                    })
                else:
                    print(f"成功: {filename} - 文件大小: {total_size} bytes")
                    successful_downloads += 1
                    
        except requests.exceptions.Timeout:
            print(f"超时: {title} - 下载超时")
            failed_records.append({
                '代码': code,
                '报告标题': title,
                'PDF链接': url,
                '失败原因': '下载超时'
            })
        except requests.exceptions.RequestException as e:
            print(f"网络错误: {title} - {e}")
            failed_records.append({
                '代码': code,
                '报告标题': title,
                'PDF链接': url,
                '失败原因': f'网络错误: {str(e)}'
            })
        except Exception as e:
            print(f"失败: {title} - 错误: {e}")
            failed_records.append({
                '代码': code,
                '报告标题': title,
                'PDF链接': url,
                '失败原因': f'其他错误: {str(e)}'
            })
    
    if skipped_count > 0:
        print(f"本批次跳过已存在文件 {skipped_count} 个，新下载 {successful_downloads - skipped_count} 个，总计 {successful_downloads} 个文件到 {full_path}")
    else:
        print(f"本批次成功下载 {successful_downloads} 个文件到 {full_path}")
    
    return successful_downloads, failed_records


def main():
    """
    主函数 - 支持多种配置选项
    """
    # ==================== 配置区域 ====================
    # 1. 日期范围配置
    DATE_RANGE_TYPE = 'default'  # 可选: 'default', 'last_year', 'last_6months', 'last_3months', 'ytd', 'custom'
    # 'default': 使用文件中的询价时间作为开始日期（默认推荐）
    # 'last_year': 下载最近一年的报告
    # 'last_6months': 下载最近6个月的报告
    # 'last_3months': 下载最近3个月的报告
    # 'ytd': 下载今年至今的报告
    # 'custom': 使用自定义日期范围
    
    CUSTOM_START_DATE = None  # 自定义开始日期，格式: 'YYYY-MM-DD'，仅在DATE_RANGE_TYPE='custom'时使用
    CUSTOM_END_DATE = None    # 自定义结束日期，格式: 'YYYY-MM-DD'，仅在DATE_RANGE_TYPE='custom'时使用
    
    # 2. API搜索配置
    API_SEARCH_KEYWORD = '度报告'   # API搜索关键词（用于从服务器获取报告列表）
    
    # 3. 报告筛选配置（对API返回结果进行二次筛选）
    FILTER_TYPE = 'all_reports'  # 可选: 'quarterly', 'annual', 'all_reports', 'custom'
    CUSTOM_KEYWORDS = []  # 自定义包含关键词，仅在FILTER_TYPE='custom'时使用
    CUSTOM_EXCLUDE = ['提示性']  # 自定义排除关键词，仅在FILTER_TYPE='custom'时使用
    
    # 4. 下载配置
    SKIP_EXISTING_FILES = True  # 是否跳过已存在的文件
    
    # 5. 数据筛选配置
    ASSET_TYPE_FILTER = '园区基础设施'  # 资产类型筛选条件
    # ================================================
    
    print("开始初始化API连接...")
    ths = ths_api()
    
    # 初始化筛选器和日期管理器
    report_filter = ReportFilter()
    date_manager = DateRangeManager()
    
    print("读取Excel文件...")
    code_df = pd.read_excel('REITs基本资料20250717.xlsx')
    code_data = code_df[code_df['资产类型'].str.contains(ASSET_TYPE_FILTER, na=False)][['代码','询价时间']]
    
    print(f"找到 {len(code_data)} 个符合条件的标的")
    print(f"日期范围模式: {DATE_RANGE_TYPE}")
    print(f"筛选配置: {FILTER_TYPE}")
    print(f"跳过已存在文件: {'是' if SKIP_EXISTING_FILES else '否'}")
    
    # 如果不使用默认模式（询价时间），则获取统一的日期范围
    unified_start_date = None
    unified_end_date = None
    if DATE_RANGE_TYPE != 'default':
        try:
            unified_start_date, unified_end_date = date_manager.get_date_range(
                DATE_RANGE_TYPE, CUSTOM_START_DATE, CUSTOM_END_DATE
            )
            print(f"统一下载日期范围: {unified_start_date} 至 {unified_end_date}")
        except ValueError as e:
            print(f"日期配置错误: {e}")
            return
    else:
        print("使用文件中的询价时间作为各标的的开始日期")
    
    total_downloaded = 0
    processed_count = 0
    all_failed_records = []
    no_report_records = []
    
    for row in code_data.itertuples():
        processed_count += 1
        code = row.代码
        inquiry_time = row.询价时间
        
        print(f"\n[{processed_count}/{len(code_data)}] 处理标的: {code}")
        
        # 根据配置决定使用哪个日期范围
        if DATE_RANGE_TYPE == 'default':
            # 使用询价时间作为开始日期
            if pd.notnull(inquiry_time):
                start_date = inquiry_time.strftime('%Y-%m-%d')
                end_date = datetime.today().strftime('%Y-%m-%d')
                print(f"使用询价时间: {start_date} 至 {end_date}")
            else:
                print(f"警告: {code} 没有询价时间，跳过")
                no_report_records.append({
                    '代码': code,
                    '询价时间': '无',
                    '失败原因': '缺少询价时间'
                })
                continue
        else:
            # 使用统一的日期范围
            start_date = unified_start_date
            end_date = unified_end_date
            print(f"使用统一日期范围: {start_date} 至 {end_date}")
        
        pdf_url, pdf_title = ths.report_download(API_SEARCH_KEYWORD, code, start_date, end_date)
        if pdf_url == [] or pdf_title == []:
            print(f'无报告标的：{code}')
            no_report_records.append({
                '代码': code,
                '搜索日期范围': f"{start_date} 至 {end_date}",
                '失败原因': '未找到相关报告'
            })
            continue
            
        print(f"找到 {len(pdf_title)} 个报告")
        
        # 使用筛选器筛选报告
        if FILTER_TYPE == 'custom':
            pdf_title, pdf_url = report_filter.filter_reports(
                pdf_title, pdf_url, FILTER_TYPE, CUSTOM_KEYWORDS, CUSTOM_EXCLUDE
            )
        else:
            pdf_title, pdf_url = report_filter.filter_reports(pdf_title, pdf_url, FILTER_TYPE)
        
        if pdf_title:  # 确保有标题才进行下载
            print(f"筛选后剩余 {len(pdf_title)} 个报告")
            folder_name = pdf_title[0].split('20')[0] if '20' in pdf_title[0] else f"reports_{code}"
            downloaded_count, failed_records = download_pdf(
                pdf_url, pdf_title, folder_name, code, SKIP_EXISTING_FILES
            )
            total_downloaded += downloaded_count
            all_failed_records.extend(failed_records)
        else:
            print(f'筛选后无可用报告：{code}')
            no_report_records.append({
                '代码': code,
                '搜索日期范围': f"{start_date} 至 {end_date}",
                '失败原因': f'筛选后无可用报告（筛选类型：{FILTER_TYPE}）'
            })
    
    # 生成失败记录Excel文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    download_dir = "Download"
    os.makedirs(download_dir, exist_ok=True)
    
    if all_failed_records:
        failed_df = pd.DataFrame(all_failed_records)
        failed_filename = os.path.join(download_dir, f'下载失败记录_{timestamp}.xlsx')
        failed_df.to_excel(failed_filename, index=False)
        print(f"\n已生成下载失败记录文件: {failed_filename}")
        print(f"失败记录数量: {len(all_failed_records)}")
    
    if no_report_records:
        no_report_df = pd.DataFrame(no_report_records)
        no_report_filename = os.path.join(download_dir, f'无报告记录_{timestamp}.xlsx')
        no_report_df.to_excel(no_report_filename, index=False)
        print(f"已生成无报告记录文件: {no_report_filename}")
        print(f"无报告记录数量: {len(no_report_records)}")
    
    print(f"\n=== 下载完成 ===")
    print(f"处理标的数量: {processed_count}")
    print(f"成功下载文件总数: {total_downloaded}")
    print(f"下载失败文件数量: {len(all_failed_records)}")
    print(f"无报告标的数量: {len(no_report_records)}")


def demo_usage():
    """
    演示不同配置的使用方法
    """
    print("=== 配置示例 ===")
    print("1. 默认模式（推荐）- 使用文件中的询价时间:")
    print("   DATE_RANGE_TYPE = 'default'")
    print()
    print("2. 只下载季度报告:")
    print("   FILTER_TYPE = 'quarterly'")
    print()
    print("3. 只下载年度报告:")
    print("   FILTER_TYPE = 'annual'")
    print()
    print("4. 下载最近3个月的报告:")
    print("   DATE_RANGE_TYPE = 'last_3months'")
    print()
    print("5. 下载最近一年的报告:")
    print("   DATE_RANGE_TYPE = 'last_year'")
    print()
    print("6. 下载今年至今的报告:")
    print("   DATE_RANGE_TYPE = 'ytd'")
    print()
    print("7. 下载自定义日期范围的报告:")
    print("   DATE_RANGE_TYPE = 'custom'")
    print("   CUSTOM_START_DATE = '2024-01-01'")
    print("   CUSTOM_END_DATE = '2024-12-31'")
    print()
    print("8. 自定义筛选条件:")
    print("   FILTER_TYPE = 'custom'")
    print("   CUSTOM_KEYWORDS = ['年报', '季报']")
    print("   CUSTOM_EXCLUDE = ['提示性', '更正']")
    print()
    print("9. 组合配置示例 - 下载最近6个月的季度报告:")
    print("   DATE_RANGE_TYPE = 'last_6months'")
    print("   FILTER_TYPE = 'quarterly'")
    print("   SKIP_EXISTING_FILES = True")


if __name__ == '__main__':
    main()
