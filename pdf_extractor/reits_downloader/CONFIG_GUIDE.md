# REITs报告下载器配置指南

## 概述

本工具支持多种配置选项，可以灵活地下载和筛选REITs报告。

## 主要功能

### 1. 本地文件检查
- 自动检查本地是否已存在相同文件
- 支持文件名模糊匹配
- 验证PDF文件完整性
- 可配置是否跳过已存在文件

### 2. 智能报告筛选
- 支持多种预设筛选模式
- 可自定义筛选关键词
- 自动排除提示性公告

### 3. 灵活日期范围
- 默认使用文件中的询价时间
- 支持多种预设时间范围
- 可自定义日期区间

## 配置选项详解

### 日期范围配置 (DATE_RANGE_TYPE)

```python
DATE_RANGE_TYPE = 'default'  # 推荐设置
```

可选值：
- `'default'`: 使用文件中的询价时间作为开始日期（**默认推荐**）
- `'last_year'`: 下载最近一年的报告
- `'last_6months'`: 下载最近6个月的报告
- `'last_3months'`: 下载最近3个月的报告
- `'ytd'`: 下载今年至今的报告
- `'custom'`: 使用自定义日期范围

### API搜索配置

```python
API_SEARCH_KEYWORD = '度报告'  # API搜索关键词
```

这个关键词用于向服务器请求报告列表，建议使用较宽泛的关键词以获取更多结果。

### 报告筛选配置 (FILTER_TYPE)

```python
FILTER_TYPE = 'all_reports'  # 推荐设置
```

对API返回的结果进行二次筛选：
- `'quarterly'`: 只保留季度报告
- `'annual'`: 只保留年度报告
- `'all_reports'`: 保留所有报告（只排除提示性公告）
- `'custom'`: 使用自定义筛选条件

### 下载配置

```python
SKIP_EXISTING_FILES = True   # 跳过已存在的文件
```

### 数据筛选配置

```python
ASSET_TYPE_FILTER = '园区基础设施'  # 资产类型筛选条件
```

## 使用示例

### 示例1：默认配置（推荐）
```python
DATE_RANGE_TYPE = 'default'      # 使用询价时间
API_SEARCH_KEYWORD = '度报告'    # API搜索关键词
FILTER_TYPE = 'all_reports'      # 保留所有报告（排除提示性）
SKIP_EXISTING_FILES = True       # 跳过已存在文件
```

### 示例2：只下载最近3个月的季度报告
```python
DATE_RANGE_TYPE = 'last_3months'
FILTER_TYPE = 'quarterly'
SKIP_EXISTING_FILES = True
```

### 示例3：下载指定日期范围的年度报告
```python
DATE_RANGE_TYPE = 'custom'
CUSTOM_START_DATE = '2023-01-01'
CUSTOM_END_DATE = '2024-12-31'
FILTER_TYPE = 'annual'
```

### 示例4：自定义筛选条件
```python
FILTER_TYPE = 'custom'
CUSTOM_KEYWORDS = ['年报', '季报']
CUSTOM_EXCLUDE = ['提示性', '更正', '补充']
```

## 文件组织结构

```
pdf_extractor/
├── Download/                           # 主下载目录
│   ├── 华安张江产业园封闭式基础设施证券投资基金/
│   │   ├── 华安张江产业园...2024年第1季度报告.pdf
│   │   ├── 华安张江产业园...2024年年度报告.pdf
│   │   └── ...
│   ├── 华夏合肥高新创新产业园封闭式基础设施证券投资基金/
│   │   └── ...
│   ├── 下载失败记录_20250811_123456.xlsx
│   └── 无报告记录_20250811_123456.xlsx
└── report_download.py
```

## 输出文件说明

### 下载失败记录.xlsx
包含以下字段：
- 代码：证券代码
- 报告标题：报告完整标题
- PDF链接：下载链接
- 失败原因：具体失败原因

### 无报告记录.xlsx
包含以下字段：
- 代码：证券代码
- 搜索日期范围：搜索的日期范围
- 失败原因：无报告的原因

## 注意事项

1. **默认模式推荐**：建议使用 `DATE_RANGE_TYPE = 'default'`，这样会根据每个标的的询价时间来下载报告，更加精准。

2. **文件检查机制**：程序会自动检查本地是否已存在相同文件，避免重复下载。

3. **PDF验证**：下载的文件会自动验证PDF格式和文件完整性。

4. **网络超时**：程序设置了30秒的下载超时，对于大文件可能需要调整。

5. **API限制**：请注意API调用频率限制，避免过于频繁的请求。

## 故障排除

### 常见问题

1. **下载失败**：检查网络连接和API token是否有效
2. **文件为空**：可能是服务器问题，查看失败记录文件
3. **筛选结果为空**：调整筛选条件或检查关键词设置
4. **日期格式错误**：确保自定义日期格式为 'YYYY-MM-DD'

### 调试建议

1. 先运行 `python test_config.py` 测试配置功能
2. 检查Excel文件中的询价时间字段格式
3. 查看生成的失败记录文件了解具体错误
4. 适当调整超时时间和重试机制