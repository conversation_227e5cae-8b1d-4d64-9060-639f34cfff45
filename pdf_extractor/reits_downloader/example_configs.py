# -*- coding: utf-8 -*-
"""
REITs报告下载器 - 配置示例

这个文件展示了不同场景下的配置示例，你可以复制这些配置到 report_download.py 中使用。
"""

# ==================== 配置示例 ====================

# 示例1：默认配置（推荐）- 使用询价时间下载所有报告
DEFAULT_CONFIG = {
    'DATE_RANGE_TYPE': 'default',
    'API_SEARCH_KEYWORD': '度报告',
    'FILTER_TYPE': 'all_reports',
    'CUSTOM_KEYWORDS': [],
    'CUSTOM_EXCLUDE': ['提示性'],
    'SKIP_EXISTING_FILES': True,
    'ASSET_TYPE_FILTER': '园区基础设施'
}

# 示例2：只下载最近一年的季度报告
QUARTERLY_RECENT_CONFIG = {
    'DATE_RANGE_TYPE': 'last_year',
    'API_SEARCH_KEYWORD': '度报告',
    'FILTER_TYPE': 'quarterly',
    'SKIP_EXISTING_FILES': True,
    'ASSET_TYPE_FILTER': '园区基础设施'
}

# 示例3：只下载年度报告
ANNUAL_ONLY_CONFIG = {
    'DATE_RANGE_TYPE': 'default',
    'API_SEARCH_KEYWORD': '度报告',
    'FILTER_TYPE': 'annual',
    'SKIP_EXISTING_FILES': True,
    'ASSET_TYPE_FILTER': '园区基础设施'
}

# 示例4：下载指定日期范围的报告
CUSTOM_DATE_CONFIG = {
    'DATE_RANGE_TYPE': 'custom',
    'CUSTOM_START_DATE': '2024-01-01',
    'CUSTOM_END_DATE': '2024-12-31',
    'API_SEARCH_KEYWORD': '度报告',
    'FILTER_TYPE': 'all_reports',
    'SKIP_EXISTING_FILES': True,
    'ASSET_TYPE_FILTER': '园区基础设施'
}

# 示例5：自定义筛选条件
CUSTOM_FILTER_CONFIG = {
    'DATE_RANGE_TYPE': 'default',
    'API_SEARCH_KEYWORD': '度报告',
    'FILTER_TYPE': 'custom',
    'CUSTOM_KEYWORDS': ['年报', '季报'],  # 只要包含这些关键词的
    'CUSTOM_EXCLUDE': ['提示性', '更正', '补充'],  # 排除这些关键词的
    'SKIP_EXISTING_FILES': True,
    'ASSET_TYPE_FILTER': '园区基础设施'
}

# 示例6：下载最近3个月的所有报告（不跳过已存在文件）
RECENT_ALL_CONFIG = {
    'DATE_RANGE_TYPE': 'last_3months',
    'API_SEARCH_KEYWORD': '度报告',
    'FILTER_TYPE': 'all_reports',
    'SKIP_EXISTING_FILES': False,  # 重新下载所有文件
    'ASSET_TYPE_FILTER': '园区基础设施'
}

# 示例7：下载今年至今的季度报告
YTD_QUARTERLY_CONFIG = {
    'DATE_RANGE_TYPE': 'ytd',
    'API_SEARCH_KEYWORD': '度报告',
    'FILTER_TYPE': 'quarterly',
    'SKIP_EXISTING_FILES': True,
    'ASSET_TYPE_FILTER': '园区基础设施'
}

def print_config_examples():
    """打印所有配置示例"""
    configs = {
        '默认配置（推荐）': DEFAULT_CONFIG,
        '最近一年季度报告': QUARTERLY_RECENT_CONFIG,
        '只下载年度报告': ANNUAL_ONLY_CONFIG,
        '自定义日期范围': CUSTOM_DATE_CONFIG,
        '自定义筛选条件': CUSTOM_FILTER_CONFIG,
        '最近3个月所有报告': RECENT_ALL_CONFIG,
        '今年至今季度报告': YTD_QUARTERLY_CONFIG
    }
    
    print("=== REITs报告下载器配置示例 ===\n")
    
    for name, config in configs.items():
        print(f"## {name}")
        print("```python")
        for key, value in config.items():
            if isinstance(value, str):
                print(f"{key} = '{value}'")
            elif isinstance(value, list):
                print(f"{key} = {value}")
            else:
                print(f"{key} = {value}")
        print("```\n")

def get_config_by_scenario(scenario: str) -> dict:
    """
    根据场景获取配置
    
    Args:
        scenario: 场景名称
        
    Returns:
        配置字典
    """
    scenarios = {
        'default': DEFAULT_CONFIG,
        'quarterly_recent': QUARTERLY_RECENT_CONFIG,
        'annual_only': ANNUAL_ONLY_CONFIG,
        'custom_date': CUSTOM_DATE_CONFIG,
        'custom_filter': CUSTOM_FILTER_CONFIG,
        'recent_all': RECENT_ALL_CONFIG,
        'ytd_quarterly': YTD_QUARTERLY_CONFIG
    }
    
    return scenarios.get(scenario, DEFAULT_CONFIG)

if __name__ == '__main__':
    print_config_examples()