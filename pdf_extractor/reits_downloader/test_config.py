# -*- coding: utf-8 -*-
"""
测试配置功能
"""
from report_download import DateRangeManager, ReportFilter

def test_date_range_manager():
    """测试日期范围管理器"""
    print("=== 测试日期范围管理器 ===")
    
    date_manager = DateRangeManager()
    
    # 测试默认模式
    start, end = date_manager.get_date_range('default')
    print(f"默认模式: {start}, {end}")
    
    # 测试最近一年
    start, end = date_manager.get_date_range('last_year')
    print(f"最近一年: {start} 至 {end}")
    
    # 测试最近3个月
    start, end = date_manager.get_date_range('last_3months')
    print(f"最近3个月: {start} 至 {end}")
    
    # 测试今年至今
    start, end = date_manager.get_date_range('ytd')
    print(f"今年至今: {start} 至 {end}")
    
    # 测试自定义日期
    start, end = date_manager.get_date_range('custom', '2024-01-01', '2024-12-31')
    print(f"自定义日期: {start} 至 {end}")


def test_report_filter():
    """测试报告筛选器"""
    print("\n=== 测试报告筛选器 ===")
    
    filter = ReportFilter()
    
    # 测试数据
    titles = [
        "华安张江产业园封闭式基础设施证券投资基金2024年第1季度报告",
        "华安张江产业园封闭式基础设施证券投资基金2024年年度报告",
        "华安张江产业园封闭式基础设施证券投资基金2024年第2季度报告提示性公告",
        "华安张江产业园封闭式基础设施证券投资基金2023年年度报告"
    ]
    urls = ["url1", "url2", "url3", "url4"]
    
    # 测试季度报告筛选
    filtered_titles, filtered_urls = filter.filter_reports(titles, urls, 'quarterly')
    print(f"季度报告筛选结果: {len(filtered_titles)} 个")
    for title in filtered_titles:
        print(f"  - {title}")
    
    # 测试年度报告筛选
    filtered_titles, filtered_urls = filter.filter_reports(titles, urls, 'annual')
    print(f"年度报告筛选结果: {len(filtered_titles)} 个")
    for title in filtered_titles:
        print(f"  - {title}")
    
    # 测试全部报告筛选（排除提示性）
    filtered_titles, filtered_urls = filter.filter_reports(titles, urls, 'all_reports')
    print(f"全部报告筛选结果: {len(filtered_titles)} 个")
    for title in filtered_titles:
        print(f"  - {title}")


if __name__ == '__main__':
    test_date_range_manager()
    test_report_filter()