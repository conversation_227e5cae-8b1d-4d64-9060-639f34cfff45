#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试新增指数检测功能
"""

import pandas as pd
import yaml
import os

def test_existing_codes():
    """测试现有指数代码检测"""
    file_path = "data/raw/price_data.csv"
    
    if not os.path.exists(file_path):
        print(f"文件 {file_path} 不存在")
        return set()
    
    try:
        df = pd.read_csv(file_path, index_col=0, parse_dates=True)
        if df.empty or 'CODE' not in df.columns:
            print("文件为空或缺少CODE列")
            return set()
        
        existing_codes = set(df['CODE'].unique())
        print(f"现有指数数量: {len(existing_codes)}")
        print(f"现有指数: {sorted(list(existing_codes))}")
        return existing_codes
        
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return set()

def test_config_codes():
    """测试配置中的指数代码"""
    try:
        with open('config/default_config.yaml', 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        
        all_codes = (config['data']['shenwan_industry_indices'] + 
                    config['data']['other_indices'])
        
        print(f"\n配置中指数数量: {len(all_codes)}")
        print(f"配置中指数: {sorted(all_codes)}")
        return set(all_codes)
        
    except Exception as e:
        print(f"读取配置文件时发生错误: {e}")
        return set()

def compare_codes():
    """比较现有指数和配置指数"""
    existing_codes = test_existing_codes()
    config_codes = test_config_codes()
    
    new_codes = config_codes - existing_codes
    removed_codes = existing_codes - config_codes
    
    print(f"\n=== 比较结果 ===")
    if new_codes:
        print(f"新增指数 ({len(new_codes)}个): {sorted(list(new_codes))}")
    else:
        print("没有新增指数")
        
    if removed_codes:
        print(f"移除指数 ({len(removed_codes)}个): {sorted(list(removed_codes))}")
    else:
        print("没有移除指数")
    
    return new_codes, removed_codes

if __name__ == '__main__':
    print("=== 新增指数检测测试 ===")
    new_codes, removed_codes = compare_codes()
    
    if new_codes:
        print(f"\n如果运行 python data.py，系统将为以下新增指数下载完整历史数据:")
        for code in sorted(new_codes):
            print(f"  - {code}")
    else:
        print("\n当前配置与现有数据一致，运行 python data.py 将执行增量更新")