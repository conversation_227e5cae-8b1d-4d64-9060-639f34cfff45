# 股票行业轮动速度量化系统（增强版）

一个模块化的股票行业轮动速度量化分析系统，用于计算和可视化多种行业轮动指标。本版本包含重大功能增强和优化。

## 最新更新

### v2.2 编码和稳定性修复 (2025-06-13)

- ✅ **中文编码问题修复**: 完全解决CSV文件中文字符编码问题，支持多种编码格式自动检测（UTF-8, GBK, GB2312, UTF-8-sig）
- ✅ **混合日期格式处理**: 修复Excel序列号和标准日期格式混合导致的解析错误
- ✅ **数据清理增强**: 自动清理空行和无效数据，提高数据质量
- ✅ **代码清理**: 移除冗余的数据合并脚本，清理注释代码
- ✅ **错误处理优化**: 增强错误日志记录和异常处理机制

### v2.1 系统优化 (2025-06-03)

- ✅ **历史百分位显示修复**: 修复图表中历史百分位数值显示为"N/A"的问题，动态调整计算窗口
- ✅ **统一数据文件结构**: 将三个独立数据文件合并为单一统一数据文件，支持可配置基准指数
- ✅ **代码清理和优化**: 移除重复功能和未使用代码，优化模块依赖关系
- ✅ **综合分析报告**: 新增完整的行业轮动速度量化分析报告，整合所有分析结果
- ✅ **指数占比分析**: 新增指数成交额占比分析功能，支持多指数对比和可视化

### v2.0 增强功能 (2025-06-03)

- ✅ **数据结构优化**: 合并行业价格和成交量数据为单一表格，添加daily_return字段
- ✅ **移除多指标对比分析**: 从可视化模块中移除多指标对比功能
- ✅ **行业涨跌幅表格报告**: 支持多时间段（本周、本月、本年、自定义）涨跌幅分析
- ✅ **按日期组织输出**: 创建YYYYMMDD_HHMMSS格式文件夹，所有输出按日期组织
- ✅ **增强可视化功能**: 行业成交金额集中度分析、前十行业成交额占比、相关性热力图
- ✅ **历史百分位计算**: 为所有指标添加滚动历史百分位计算和水平分类
- ✅ **HHI增强分析**: 添加集中度阈值参考线和详细分析报告
- ✅ **配置文件更新**: 支持所有新功能的配置参数

## 系统特点

- **模块化设计**：各模块独立开发、测试和维护
- **可复用性**：核心算法可应用于其他金融分析场景
- **灵活配置**：通过配置文件管理所有系统参数
- **多种存储格式**：支持CSV、Parquet等存储格式
- **丰富的可视化**：静态图表、交互式图表和分析报告
- **完整的测试覆盖**：包含单元测试和集成测试
- **按日期组织输出**：每次运行创建独立的时间戳文件夹
- **历史百分位分析**：为关键指标提供历史百分位计算
- **增强的集中度分析**：HHI指标包含可配置的集中度阈值
- **行业表现报告**：多时间段涨跌幅对比表格
- **相关性分析**：行业间相关性矩阵和热力图可视化
- **中文数据支持**：完全支持中文行业名称和数据，自动处理编码问题
- **混合数据格式处理**：智能处理Excel序列号和标准日期格式混合数据
- **强健的错误处理**：全面的异常处理和错误恢复机制

## 核心指标

系统计算以下行业轮动速度指标：

1. **行业排名变化指标**：基于行业涨跌幅排名计算每日变动绝对值之和
2. **相对强度指标**：行业指数与基准指数相对强度的每日横截面标准差
3. **行业收益率离散度**：每日所有行业收益率的横截面标准差
4. **行业成交金额集中度**：每日行业成交金额的赫芬达尔-赫希曼指数(HHI)
5. **行业涨跌幅分布特征**：每日所有行业涨跌幅的偏度和峰度
6. **动量因子排名变化率**：基于指定回溯期内的行业累计收益率排名变化

## 新增功能

### 数据结构优化
- **合并数据表**：行业价格和成交量数据合并为单一表格
- **日涨跌幅字段**：自动计算每日涨跌幅（daily_return）

### 行业表现分析
- **多时间段涨跌幅表格**：本周、本月、本年度、自定义时间段
- **排序和格式化**：按指定时间段排序，百分比格式显示
- **Markdown报告**：自动生成表格格式的分析报告

### 增强可视化
- **成交金额集中度可视化**：
  - 堆叠面积图：所有行业占比变化趋势
  - 折线图：前十行业占比变化趋势
- **相关性分析**：
  - 相关性矩阵热力图
  - 高/低相关性行业对识别
- **HHI增强分析**：
  - 集中度阈值参考线
  - 历史百分位区间标识

### 历史百分位分析
- **滚动百分位计算**：为所有指标计算历史百分位
- **水平分类**：极高、高、中等、低、极低五个等级
- **图表注释**：在图表中显示最新值的历史百分位

### 输出管理
- **按日期组织**：每次运行创建YYYYMMDD_HHMMSS格式的文件夹
- **最新结果链接**：自动创建指向最新结果的符号链接
- **运行元数据**：保存每次运行的详细信息
- **旧结果清理**：可配置保留最新N次运行结果

## 项目结构

```
sector_rotation_system/
├── config/                 # 配置管理
│   ├── __init__.py
│   ├── settings.py         # 系统配置
│   └── default_config.yaml # 默认配置文件
├── data/                   # 数据处理
│   ├── __init__.py
│   ├── preprocessor.py     # 数据预处理（增强版）
│   └── storage.py          # 数据存储管理
├── indicators/             # 指标计算
│   ├── __init__.py
│   ├── base.py            # 基础指标类
│   ├── calculator.py      # 指标计算引擎
│   ├── percentile_calculator.py  # 历史百分位计算
│   └── hhi_enhanced.py    # HHI增强分析
├── visualization/          # 可视化
│   ├── __init__.py
│   ├── plotter.py         # 原有图表生成
│   └── enhanced_plotter.py # 增强可视化功能
├── reporting/              # 报告生成（新增）
│   ├── __init__.py
│   ├── sector_performance.py  # 行业表现报告
│   └── table_generator.py     # 表格生成器
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── logger.py          # 日志管理
│   └── output_manager.py  # 输出管理（新增）
├── tests/                  # 测试模块
│   ├── __init__.py
│   ├── test_data.py
│   ├── test_indicators.py
│   └── test_visualization.py
├── main.py                 # 主程序（增强版）
├── requirements.txt        # 依赖包
└── README.md              # 项目文档
```

## 安装和配置

### 1. 环境要求

- Python 3.8+
- 推荐使用虚拟环境

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 数据准备

系统需要包含以下字段的统一数据文件 `data/raw/unified_sector_data.csv`：

- `date`: 日期（支持多种格式，包括Excel序列号）
- `sector_code`: 行业代码
- `sector_close_price`: 行业收盘价
- `sector_volume_amount`: 行业成交金额
- `benchmark_price`: 基准指数价格
- `daily_return`: 日收益率（可选，系统会自动计算）

**注意**: 系统会自动处理中文编码问题，支持GBK、UTF-8等多种编码格式。

### 4. 配置系统

系统会自动创建默认配置文件 `config/default_config.yaml`。您可以根据需要修改配置：

```yaml
indicator:
  momentum_lookback_period: 20
  ranking_window: 5
  min_data_points: 30
  # 历史百分位计算参数
  percentile_window: 252  # 一年交易日
  # HHI集中度阈值参数
  hhi_low_threshold_percentile: 25
  hhi_high_threshold_percentile: 75

data:
  benchmark_index: "000300.SH"
  data_source: "csv"
  storage_format: "parquet"
  date_format: "%Y-%m-%d"
  # 指数列表配置
  index_list:
    - "000300.SH"  # 沪深300
    - "000905.SH"  # 中证500
    - "000852.SH"  # 中证1000

visualization:
  figure_size: [12, 8]
  dpi: 300
  style: "seaborn-v0_8"
  color_palette: "husl"
  # 移除多指标对比分析
  enable_multi_indicator_comparison: false
  # 新增可视化选项
  top_sectors_count: 10  # 成交额占比前十行业
  correlation_threshold_high: 0.7
  correlation_threshold_low: -0.7

storage:
  base_path: "data/output"
  backup_enabled: true
  compression: "snappy"
  # 按日期组织输出
  use_date_folders: true
  date_folder_format: "%Y%m%d_%H%M%S"

# 新增报告配置
reporting:
  # 时间段配置
  time_periods:
    weekly: 5      # 最近5个交易日
    monthly: 22    # 当月交易日（近似）
    yearly: 252    # 当年交易日（近似）
  # 表格排序方式
  sort_by: "weekly"  # 按本周涨跌幅排序
```

## 使用方法

### 1. 数据获取和管理

#### 自动数据下载（推荐）

系统提供智能数据下载功能，支持增量更新和新增指数检测：

```bash
# 首次完整下载（从2015年开始）
python data.py

# 智能更新（自动检测新增指数 + 增量更新）
python data.py

# 强制完整更新
python data.py --force
# 或
python data.py -f

# 检测新增指数（不下载数据）
python demo_new_indices.py
```

**智能更新功能**：
- 🔍 **自动检测新增指数**：比较配置文件与现有数据，识别新增指数
- 📥 **完整历史数据下载**：为新增指数下载从起始日期开始的完整数据
- ⚡ **增量更新**：为现有指数只下载最新数据
- 🔄 **智能合并**：自动合并新增数据和增量数据，去重排序
- 📊 **详细反馈**：提供下载进度和统计信息

#### 数据配置

数据源配置在 `config/default_config.yaml` 中：

```yaml
data:
  start_date: "2015-01-01"  # 数据开始日期
  unified_data_file: "data/raw/price_data.csv"  # 数据文件路径
  
  # 申万行业指数
  shenwan_industry_indices:
    - "801010.SI"  # 申万农林牧渔
    - "801020.SI"  # 申万采掘
    - "801030.SI"  # 申万化工
    # ... 更多行业指数
  
  # 其他重要指数
  other_indices:
    - "000300.SH"  # 沪深300
    - "000905.SH"  # 中证500
    - "000852.SH"  # 中证1000
    - "000016.SH"  # 上证50
```

#### 手动准备数据

如果需要手动准备数据，系统需要以下格式的数据文件（放在 `data/raw/` 目录下）：

```csv
# price_data.csv
date,SEC_NAME,CLOSE,AMT,PCT_CHG,CODE
2023-01-01,沪深300,4000.5,1000000000,1.2,000300.SH
2023-01-01,申万农林牧渔,1200.3,500000000,-0.5,801010.SI
```

数据字段说明：
- `date`: 日期
- `SEC_NAME`: 指数名称
- `CLOSE`: 收盘价
- `AMT`: 成交金额
- `PCT_CHG`: 涨跌幅(%)
- `CODE`: 指数代码

### 2. 创建示例数据

如果没有真实数据，可以创建示例数据进行测试：

```bash
python main.py --mode sample
```

### 3. 运行分析

#### 完整分析（推荐）

```bash
# 基本完整分析
python main.py --mode full --start-date 2024-01-01 --end-date 2025-06-12

# 包含自定义时间段的完整分析
python main.py --mode full --start-date 2023-01-01 --end-date 2023-12-31 --custom-start-date 2023-06-01

# 指定输出前缀
python main.py --mode full --start-date 2023-01-01 --end-date 2023-12-31 --output-prefix my_analysis
```

#### 仅计算指标

```bash
python main.py --mode indicators --start-date 2023-01-01 --end-date 2023-12-31
```

#### 仅生成可视化

```bash
python main.py --mode visualization --date-range 2023-01-01_2023-12-31
```

#### 清理旧结果

```bash
# 清理旧运行结果，保留最新10个
python main.py --cleanup-old 10
```

### 4. 查看结果

分析完成后，结果将按日期组织保存：

#### 输出目录结构
```
data/output/
├── latest/                 # 最新运行结果的符号链接
├── 20240101_143022/       # 具体运行时间戳文件夹
│   ├── charts/            # 图表文件
│   │   ├── sector_concentration_stacked_area_*.png
│   │   ├── top_10_sectors_trend_*.png
│   │   ├── sector_correlation_heatmap_*.png
│   │   ├── hhi_enhanced_analysis_*.png
│   │   └── sector_rotation_*_*.png
│   ├── reports/           # 报告文件
│   │   ├── sector_performance_*.md
│   │   └── hhi_concentration_*.md
│   ├── tables/            # 表格数据
│   │   ├── sector_performance_*.csv
│   │   ├── sector_performance_*.xlsx
│   │   └── correlation_matrix_*.csv
│   ├── data/              # 指标数据
│   │   └── indicators_*.parquet
│   ├── logs/              # 运行日志
│   └── run_metadata.json  # 运行元数据
└── 20240102_091545/       # 其他运行结果
    └── ...
```

#### 主要输出文件

**图表文件**：
- `sector_concentration_stacked_area_*.png`：行业成交额占比堆叠面积图
- `top_10_sectors_trend_*.png`：前十行业成交额占比趋势图
- `sector_correlation_heatmap_*.png`：行业相关性热力图
- `hhi_enhanced_analysis_*.png`：HHI集中度分析图（含阈值线）
- `sector_rotation_*_*.png`：各指标时间序列图（含历史百分位）

**报告文件**：
- `sector_performance_*.md`：行业涨跌幅表现报告
- `hhi_concentration_*.md`：HHI集中度分析报告

**表格数据**：
- `sector_performance_*.csv/xlsx`：行业表现数据表
- `correlation_matrix_*.csv`：相关性矩阵数据

**指标数据**：
- `indicators_*.parquet`：所有计算指标及历史百分位数据

## API使用

### 程序化使用

```python
from main import SectorRotationAnalysisSystem

# 初始化系统
system = SectorRotationAnalysisSystem()

# 运行完整分析
results = system.run_full_analysis('2023-01-01', '2023-12-31')

# 仅计算指标
indicators_results = system.run_indicators_only('2023-01-01', '2023-12-31')

# 仅生成可视化
viz_results = system.run_visualization_only('2023-01-01_2023-12-31')
```

### 单独使用各模块

```python
# 数据预处理
from data.preprocessor import DataPreprocessor
preprocessor = DataPreprocessor()
data = preprocessor.load_and_preprocess('2023-01-01', '2023-12-31')

# 指标计算
from indicators.calculator import IndicatorCalculationEngine
engine = IndicatorCalculationEngine()
indicators = engine.calculate_all_indicators(data)

# 可视化
from visualization.plotter import VisualizationManager
viz_manager = VisualizationManager()
charts = viz_manager.create_comprehensive_analysis(indicators)
```

## 测试

### 增强功能测试

运行增强功能测试脚本：

```bash
python test_enhanced_system.py
```

这个测试脚本会验证：
- 系统初始化
- 配置管理
- 数据预处理（含daily_return字段）
- 指标计算
- 历史百分位计算
- HHI增强分析
- 行业表现报告
- 相关性分析
- 输出管理
- 完整工作流程

### 单元测试

运行所有测试：

```bash
pytest tests/
```

运行特定测试：

```bash
pytest tests/test_indicators.py
pytest tests/test_data.py
pytest tests/test_visualization.py
```

生成测试覆盖率报告：

```bash
pytest --cov=. tests/
```

## 扩展和定制

### 添加新指标

1. 在 `indicators/base.py` 中创建新的指标基类
2. 在 `indicators/calculator.py` 中实现具体指标
3. 在 `IndicatorCalculationEngine` 中注册新指标
4. 添加相应的测试

### 添加新数据源

1. 继承 `data/preprocessor.py` 中的 `DataSource` 抽象类
2. 实现所需的数据加载方法
3. 在 `DataPreprocessor` 中使用新数据源

### 自定义可视化

1. 继承 `visualization/plotter.py` 中的绘图类
2. 实现自定义绘图方法
3. 在 `VisualizationManager` 中集成新功能

## 性能优化

- 使用Parquet格式存储大量数据
- 启用数据压缩减少存储空间
- 合理设置日志级别避免过多输出
- 对于大数据集，考虑分批处理

## 故障排除

### 常见问题

#### 1. 中文编码问题

**问题**: `UnicodeDecodeError: 'utf-8' codec can't decode byte`

**解决方案**: 系统已自动处理多种编码格式。如果仍有问题，请检查：
- 确保CSV文件保存时使用了正确的编码格式
- 推荐使用UTF-8-sig或GBK编码保存中文数据
- 系统会自动尝试多种编码格式：utf-8, gbk, gb2312, utf-8-sig

#### 2. 日期格式问题

**问题**: `ValueError: time data does not match format`

**解决方案**: 系统已支持混合日期格式处理，包括：
- 标准日期格式：2024-01-01, 2024/1/1
- Excel序列号：45295, 45297等
- 如果仍有问题，请确保日期列名为'date'

#### 3. 数据质量问题

**问题**: 空行或无效数据导致计算错误

**解决方案**: 系统会自动：
- 清理空行和无效数据
- 处理NaN值和异常数据
- 记录数据清理过程到日志文件

### 其他故障排除

### 常见问题

1. **数据文件未找到**：确保数据文件在正确的目录下
2. **内存不足**：减少数据量或增加系统内存
3. **图表显示问题**：检查matplotlib后端设置
4. **权限错误**：确保对输出目录有写权限

### 日志查看

系统日志保存在 `logs/` 目录下：

- `sector_rotation_YYYY-MM-DD.log`：常规日志
- `error_YYYY-MM-DD.log`：错误日志

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 添加测试
5. 提交Pull Request

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交Issue
- 发送邮件
- 项目讨论区

---

**注意**：本系统仅用于学术研究和教育目的，不构成投资建议。使用者应当根据自己的判断进行投资决策。
