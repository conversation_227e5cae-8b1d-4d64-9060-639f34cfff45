# 数据管理系统功能说明

## 功能概述

本系统提供了智能的数据管理功能，支持：
- 🔍 **新增指数自动检测**
- 📥 **完整历史数据下载**
- ⚡ **增量数据更新**
- 🔄 **智能数据合并**

## 核心功能

### 1. 新增指数检测

系统会自动比较配置文件中的指数列表与现有数据文件中的指数，识别：
- **新增指数**：配置中有但数据文件中没有的指数
- **移除指数**：数据文件中有但配置中没有的指数

### 2. 智能数据更新策略

#### 对于新增指数：
- 下载从 `start_date`（默认2015-01-01）开始的完整历史数据
- 确保新增指数的数据完整性

#### 对于现有指数：
- 只下载最新日期之后的增量数据
- 最大化效率，最小化API调用

#### 数据合并：
- 自动合并新增指数数据和增量数据
- 去重处理，保留最新数据
- 按日期排序

### 3. 配置管理

#### 指数分类：
```yaml
# 申万行业指数（28个主要行业）
shenwan_industry_indices:
  - "801010.SI"  # 申万农林牧渔
  - "801030.SI"  # 申万基础化工
  # ... 更多行业指数

# 其他重要指数（市场基准）
other_indices:
  - "000300.SH"  # 沪深300
  - "000905.SH"  # 中证500
  - "000852.SH"  # 中证1000
  - "000016.SH"  # 上证50
```

## 使用方法

### 基本使用

```bash
# 智能更新（推荐）
python data.py

# 检测新增指数（不下载）
python demo_new_indices.py

# 强制完整更新
python data.py --force
```

### 添加新指数

1. 编辑 `config/default_config.yaml`
2. 在相应分类下添加新指数代码
3. 运行 `python data.py`
4. 系统自动检测并下载新指数的完整历史数据

### 示例场景

#### 场景1：添加新的申万行业指数
```yaml
shenwan_industry_indices:
  # 现有指数...
  - "801XXX.SI"  # 新增行业指数
```

运行 `python data.py` 后：
- 系统检测到新增指数 801XXX.SI
- 下载该指数从2015-01-01开始的完整数据
- 对其他指数执行增量更新
- 合并所有数据

#### 场景2：日常数据更新
```bash
python data.py
```
- 检测配置变化（通常无变化）
- 对所有指数执行增量更新
- 只下载最新的交易日数据

## 数据文件结构

生成的数据文件 `data/raw/price_data.csv` 包含：

| 字段 | 说明 | 示例 |
|------|------|------|
| date | 交易日期（索引） | 2025-08-15 |
| SEC_NAME | 指数名称 | 农林牧渔(申万) |
| CLOSE | 收盘价 | 2236.45 |
| AMT | 成交金额 | 10610370000.0 |
| PCT_CHG | 涨跌幅(%) | 1.59678372 |
| CODE | 指数代码 | 801010.SI |

## 错误处理

系统包含完善的错误处理机制：

- **Wind API连接失败**：自动重试和错误报告
- **数据文件损坏**：自动检测并提示完整更新
- **配置文件错误**：详细的错误信息和修复建议
- **网络中断**：保存已下载的数据，支持断点续传

## 性能优化

- **增量更新**：只下载必要的新数据
- **批量处理**：一次API调用获取多个字段
- **数据缓存**：避免重复下载相同数据
- **智能检测**：只在配置变化时下载新指数数据

## 监控和日志

系统提供详细的运行信息：

```
=== 运行示例输出 ===
检测到新增指数: ['801950.SI', '801960.SI']
为新增指数下载从 2015-01-01 开始的完整历史数据...
对现有指数执行增量更新，从 2025-08-16 开始...
数据已更新到 data/raw/price_data.csv
新增数据行数: 5164
总数据行数: 87788
新增指数数量: 2
增量更新指数数量: 30
```

## 最佳实践

1. **定期运行**：建议每日运行一次进行数据更新
2. **配置管理**：谨慎修改配置文件，确保指数代码正确
3. **数据备份**：重要数据建议定期备份
4. **监控日志**：关注运行日志，及时发现问题
5. **测试验证**：添加新指数前可先运行 `demo_new_indices.py` 检测

## 故障排除

### 常见问题

1. **新增指数检测失败**
   - 检查配置文件格式是否正确
   - 确认指数代码是否有效

2. **数据下载失败**
   - 检查Wind API连接状态
   - 确认指数代码在Wind系统中存在

3. **数据合并错误**
   - 检查数据文件是否损坏
   - 考虑使用 `--force` 参数重新下载

### 恢复方法

```bash
# 强制完整更新
python data.py --force

# 检查数据完整性
python demo_new_indices.py
```

---

**注意**：本系统需要有效的Wind API许可证才能正常工作。请确保Wind终端已正确安装和配置。