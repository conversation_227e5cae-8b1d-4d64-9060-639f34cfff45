#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示新增指数检测功能
"""

import yaml
import pandas as pd
import os

def demo_new_indices_detection():
    """演示新增指数检测功能"""
    print("=== 新增指数检测功能演示 ===")
    
    # 读取配置
    with open('config/default_config.yaml', 'r', encoding='utf-8') as file:
        config = yaml.safe_load(file)
    
    # 读取现有数据
    file_path = config['data']['unified_data_file']
    if os.path.exists(file_path):
        df = pd.read_csv(file_path, index_col=0, parse_dates=True)
        existing_codes = set(df['CODE'].unique())
        print(f"✓ 现有数据文件中包含 {len(existing_codes)} 个指数")
        
        # 显示最新数据日期
        latest_date = df.index.max().strftime('%Y-%m-%d')
        print(f"✓ 最新数据日期: {latest_date}")
    else:
        existing_codes = set()
        print("✗ 没有找到现有数据文件")
    
    # 获取配置中的指数
    all_codes = (config['data']['shenwan_industry_indices'] + 
                config['data']['other_indices'])
    config_codes = set(all_codes)
    print(f"✓ 配置文件中包含 {len(config_codes)} 个指数")
    
    # 检测差异
    new_codes = config_codes - existing_codes
    removed_codes = existing_codes - config_codes
    
    print(f"\n=== 检测结果 ===")
    
    if new_codes:
        print(f"🆕 发现 {len(new_codes)} 个新增指数:")
        for code in sorted(new_codes):
            # 尝试从配置注释中获取指数名称
            if code in config['data']['shenwan_industry_indices']:
                print(f"   - {code} (申万行业指数)")
            else:
                print(f"   - {code} (其他指数)")
        
        print(f"\n📥 系统将执行以下操作:")
        print(f"   1. 为新增指数下载从 {config['data']['start_date']} 开始的完整历史数据")
        print(f"   2. 为现有指数执行增量更新（如有新数据）")
        print(f"   3. 合并所有数据并保存到 {file_path}")
        
    else:
        print("✅ 没有新增指数，将执行常规增量更新")
    
    if removed_codes:
        print(f"\n⚠️  配置中移除了 {len(removed_codes)} 个指数:")
        for code in sorted(removed_codes):
            print(f"   - {code}")
        print("   注意: 这些指数的历史数据将保留在文件中")
    
    print(f"\n=== 使用说明 ===")
    print("运行 'python data.py' 将自动:")
    print("• 检测新增指数并下载完整历史数据")
    print("• 对现有指数执行增量更新")
    print("• 智能合并数据并去重")
    print("• 提供详细的进度反馈")
    
    return new_codes, removed_codes

if __name__ == '__main__':
    new_codes, removed_codes = demo_new_indices_detection()
    
    if new_codes:
        print(f"\n💡 提示: 发现了 {len(new_codes)} 个新增指数")
        print("运行 'python data.py' 来下载这些新指数的数据")
    else:
        print(f"\n💡 提示: 配置与现有数据一致")
        print("运行 'python data.py' 将执行增量更新")