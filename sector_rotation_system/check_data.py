#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据更新结果
"""

import pandas as pd
import yaml

def check_data_update():
    """检查数据更新结果"""
    print("=== 数据更新结果检查 ===")
    
    # 读取配置
    with open('config/default_config.yaml', 'r', encoding='utf-8') as file:
        config = yaml.safe_load(file)
    
    # 读取数据文件
    file_path = config['data']['unified_data_file']
    try:
        df = pd.read_csv(file_path, index_col=0, parse_dates=True)
        print(f"✓ 成功读取数据文件: {file_path}")
        print(f"✓ 数据行数: {len(df):,}")
        print(f"✓ 数据时间范围: {df.index.min().strftime('%Y-%m-%d')} 到 {df.index.max().strftime('%Y-%m-%d')}")
        
        # 检查指数数量
        actual_codes = set(df['CODE'].unique())
        print(f"✓ 实际指数数量: {len(actual_codes)}")
        
        # 检查配置中的指数
        config_codes = set(config['data']['shenwan_industry_indices'] + 
                          config['data']['other_indices'])
        print(f"✓ 配置指数数量: {len(config_codes)}")
        
        # 检查一致性
        missing_codes = config_codes - actual_codes
        extra_codes = actual_codes - config_codes
        
        if not missing_codes and not extra_codes:
            print("✅ 数据与配置完全一致")
        else:
            if missing_codes:
                print(f"⚠️  配置中有但数据中缺失的指数: {sorted(list(missing_codes))}")
            if extra_codes:
                print(f"⚠️  数据中有但配置中没有的指数: {sorted(list(extra_codes))}")
        
        # 检查新增指数
        new_indices = ['801950.SI', '801960.SI', '801970.SI', '801980.SI']
        print(f"\n=== 新增指数检查 ===")
        for code in new_indices:
            if code in actual_codes:
                code_data = df[df['CODE'] == code]
                print(f"✅ {code}: {len(code_data)} 行数据 ({code_data.index.min().strftime('%Y-%m-%d')} 到 {code_data.index.max().strftime('%Y-%m-%d')})")
            else:
                print(f"❌ {code}: 未找到数据")
        
        # 检查移除指数
        removed_index = '801020.SI'
        print(f"\n=== 移除指数检查 ===")
        if removed_index in actual_codes:
            print(f"❌ {removed_index}: 仍存在于数据中（应该已被移除）")
        else:
            print(f"✅ {removed_index}: 已成功移除")
        
        # 显示指数列表
        print(f"\n=== 当前指数列表 ===")
        for i, code in enumerate(sorted(actual_codes), 1):
            code_data = df[df['CODE'] == code]
            sec_name = code_data['SEC_NAME'].iloc[0] if not code_data.empty else "未知"
            print(f"{i:2d}. {code} - {sec_name}")
            
    except Exception as e:
        print(f"❌ 读取数据文件时发生错误: {e}")

if __name__ == '__main__':
    check_data_update()