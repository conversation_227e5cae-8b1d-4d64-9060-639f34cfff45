import pandas as pd
from WindPy import w
from datetime import datetime, timedelta
import os
import yaml
from pathlib import Path

# 初始化Wind API
w.start()
w.isconnected()


def load_config(config_path="config/default_config.yaml"):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as file:
        config = yaml.safe_load(file)
    return config


def get_latest_date_from_file(file_path):
    """从现有文件中获取最新日期"""
    if not os.path.exists(file_path):
        return None
    
    try:
        df = pd.read_csv(file_path, index_col=0, parse_dates=True)
        if df.empty:
            return None
        return df.index.max().strftime('%Y-%m-%d')
    except Exception as e:
        print(f"读取文件 {file_path} 时发生错误: {e}")
        return None


def get_existing_codes_from_file(file_path):
    """从现有文件中获取已有的指数代码列表"""
    if not os.path.exists(file_path):
        return set()
    
    try:
        df = pd.read_csv(file_path, index_col=0, parse_dates=True)
        if df.empty or 'CODE' not in df.columns:
            return set()
        return set(df['CODE'].unique())
    except Exception as e:
        print(f"读取文件 {file_path} 中的指数代码时发生错误: {e}")
        return set()


def detect_new_codes(config_codes, existing_codes):
    """检测新增的指数代码"""
    config_codes_set = set(config_codes)
    new_codes = config_codes_set - existing_codes
    removed_codes = existing_codes - config_codes_set
    
    return new_codes, removed_codes


def price_data(start_date, code_list, end_date=None):
    """获取市场价格相关的日序列数据"""
    if end_date is None:
        end_date = datetime.today().strftime('%Y-%m-%d')
    
    all_data = list()
    for code in code_list:
        try:
            print(f"正在获取 {code} 的价格数据 ({start_date} 到 {end_date})...")
            # 获取基础价格数据       
            error_code, data = w.wsd(code.strip(),
                                     "sec_name,close,amt,pct_chg",
                                     start_date, end_date, "unit=1;PriceAdj=F", usedf=True)
            print(f"基础价格数据获取结果: {error_code}")
            if error_code == 0 and not data.empty:
                # 添加代码列
                data['CODE'] = code.strip()
                all_data.append(data)
                print(f"成功获取 {code} 的价格数据，形状: {data.shape}")
            else:
                print(f"获取 {code} 价格数据失败，错误代码: {error_code}")
                
        except Exception as e:
            print(f"获取 {code} 价格数据时发生异常: {e}")
            continue
    
    if not all_data:
        print("没有获取到任何价格数据")
        return pd.DataFrame()
    
    # 合并所有数据
    result = pd.concat(all_data, sort=True)
    
    print(f"合并后价格数据形状: {result.shape}")
    print("价格数据列名:", result.columns.tolist())
    
    return result


def convert_to_system_format(df_wind, benchmark_code='000300.SH'):
    """将Wind数据转换为系统期望的格式"""
    print("正在转换数据格式为系统兼容格式...")
    
    converted_data = []
    
    # 获取所有日期
    dates = df_wind.index.unique()
    
    for date in dates:
        date_data = df_wind[df_wind.index == date]
        
        for _, row in date_data.iterrows():
            # 计算日收益率
            code = row['CODE']
            close_price = row['CLOSE']
            
            # 获取前一日价格计算收益率
            prev_date_data = df_wind[(df_wind.index < date) & (df_wind['CODE'] == code)]
            if not prev_date_data.empty:
                prev_price = prev_date_data.iloc[-1]['CLOSE']
                daily_return = (close_price - prev_price) / prev_price
            else:
                daily_return = 0.0
            
            converted_row = {
                'date': date.strftime('%Y-%m-%d'),
                'sector_code': code,
                'sector_name': row['SEC_NAME'],
                'sector_close_price': close_price,
                'sector_volume_amount': row['AMT'],
                'daily_return': daily_return
            }
            converted_data.append(converted_row)
    
    # 添加基准数据（使用沪深300作为基准）
    if benchmark_code in df_wind['CODE'].values:
        benchmark_data = df_wind[df_wind['CODE'] == benchmark_code].copy()
        
        for date, row in benchmark_data.iterrows():
            # 计算基准收益率
            prev_benchmark = benchmark_data[benchmark_data.index < date]
            if not prev_benchmark.empty:
                prev_price = prev_benchmark.iloc[-1]['CLOSE']
                daily_return = (row['CLOSE'] - prev_price) / prev_price
            else:
                daily_return = 0.0
                
            benchmark_row = {
                'date': date.strftime('%Y-%m-%d'),
                'sector_code': '000510.CSI',  # 系统期望的基准代码
                'sector_name': '基准指数',
                'sector_close_price': row['CLOSE'],
                'sector_volume_amount': row['AMT'],
                'daily_return': daily_return
            }
            converted_data.append(benchmark_row)
    
    # 创建DataFrame并排序
    df_converted = pd.DataFrame(converted_data)
    df_converted = df_converted.sort_values(['date', 'sector_code'])
    
    print(f"数据格式转换完成，转换后数据行数: {len(df_converted)}")
    return df_converted


def update_price_data(config, force_full_update=False):
    """增量更新价格数据，支持新增指数检测"""
    # 确保数据目录存在
    data_dir = Path("data/raw")
    data_dir.mkdir(parents=True, exist_ok=True)
    
    file_path = config['data']['wind_data_file']
    start_date = config['data']['start_date']
    
    # 合并所有指数代码
    all_codes = (config['data']['shenwan_industry_indices'] + 
                config['data']['other_indices'])
    
    if force_full_update or not os.path.exists(file_path):
        print("执行完整数据更新...")
        # 完整更新
        df_new = price_data(start_date, all_codes)
        if not df_new.empty:
            df_new.to_csv(file_path)
            print(f"完整数据已保存到 {file_path}")
        return df_new
    
    # 检测新增和移除的指数
    existing_codes = get_existing_codes_from_file(file_path)
    new_codes, removed_codes = detect_new_codes(all_codes, existing_codes)
    
    # 读取现有数据
    df_existing = pd.read_csv(file_path, index_col=0, parse_dates=True)
    
    # 处理移除的指数
    if removed_codes:
        print(f"检测到已移除的指数: {list(removed_codes)}")
        print("正在从数据文件中移除这些指数的数据...")
        
        # 移除已删除指数的数据
        original_rows = len(df_existing)
        df_existing = df_existing[~df_existing['CODE'].isin(removed_codes)]
        removed_rows = original_rows - len(df_existing)
        
        print(f"已移除 {len(removed_codes)} 个指数的 {removed_rows} 行数据")
        
        # 更新现有指数列表
        existing_codes = existing_codes - removed_codes
    
    # 处理新增指数
    df_new_codes = pd.DataFrame()
    if new_codes:
        print(f"检测到新增指数: {list(new_codes)}")
        print(f"为新增指数下载从 {start_date} 开始的完整历史数据...")
        df_new_codes = price_data(start_date, list(new_codes))
        if not df_new_codes.empty:
            print(f"新增指数数据获取完成，数据行数: {len(df_new_codes)}")
    
    # 获取最新日期进行增量更新
    latest_date = get_latest_date_from_file(file_path)
    if latest_date is None:
        print("无法获取最新日期，执行完整更新...")
        df_new = price_data(start_date, all_codes)
        if not df_new.empty:
            df_new.to_csv(file_path)
            print(f"完整数据已保存到 {file_path}")
        return df_new
    
    # 计算增量更新的开始日期（最新日期的下一个交易日）
    latest_datetime = datetime.strptime(latest_date, '%Y-%m-%d')
    increment_start = (latest_datetime + timedelta(days=1)).strftime('%Y-%m-%d')
    today = datetime.today().strftime('%Y-%m-%d')
    
    # 对现有指数进行增量更新
    df_increment = pd.DataFrame()
    existing_codes_list = list(existing_codes.intersection(set(all_codes)))
    
    # 检查是否需要增量更新（考虑周末和节假日）
    increment_date = datetime.strptime(increment_start, '%Y-%m-%d')
    today_date = datetime.today()
    
    # 如果增量开始日期是今天或未来，且今天是周末，则无需更新
    if increment_date.date() >= today_date.date():
        print("现有指数数据已是最新，无需增量更新")
    elif existing_codes_list:
        print(f"对现有指数执行增量更新，从 {increment_start} 开始...")
        df_increment = price_data(increment_start, existing_codes_list)
        if not df_increment.empty:
            print(f"增量数据获取完成，数据行数: {len(df_increment)}")
        else:
            print("增量更新期间没有新的交易数据（可能是周末或节假日）")
    
    # 合并所有数据
    data_frames = [df_existing]
    
    if not df_new_codes.empty:
        data_frames.append(df_new_codes)
        
    if not df_increment.empty:
        data_frames.append(df_increment)
    
    # 检查是否有数据变化（移除指数或新增数据）
    has_changes = removed_codes or not df_new_codes.empty or not df_increment.empty
    
    if not has_changes:
        print("没有新数据需要更新")
        return df_existing
    
    # 合并数据
    if len(data_frames) > 1:
        df_combined = pd.concat(data_frames)
        # 去重并排序
        df_combined = df_combined[~df_combined.index.duplicated(keep='last')]
        df_combined = df_combined.sort_index()
    else:
        df_combined = df_existing
    
    # 转换为系统格式并保存
    df_system_format = convert_to_system_format(df_combined, config['data']['benchmark_index'])
    
    # 保存Wind格式数据
    df_combined.to_csv(file_path)
    print(f"Wind格式数据已保存到 {file_path}")
    
    # 保存系统格式数据
    system_file_path = config['data']['unified_data_file']
    df_system_format.to_csv(system_file_path, index=False, encoding='utf-8')
    print(f"系统格式数据已保存到 {system_file_path}")
    
    # 统计信息
    print(f"Wind格式数据行数: {len(df_combined)}")
    print(f"系统格式数据行数: {len(df_system_format)}")
    
    if removed_codes:
        print(f"移除指数数量: {len(removed_codes)}")
    if new_codes:
        print(f"新增指数数量: {len(new_codes)}")
    if not df_increment.empty:
        print(f"增量更新指数数量: {len(existing_codes_list)}")
    
    return df_combined


def main(force_full_update=False):
    """主函数"""
    try:
        # 加载配置
        config = load_config()
        
        # 更新价格数据
        df = update_price_data(config, force_full_update)
        
        if not df.empty:
            print("数据更新完成!")
            print(f"数据时间范围: {df.index.min()} 到 {df.index.max()}")
            print(f"包含指数数量: {df['CODE'].nunique()}")
            print("指数列表:", df['CODE'].unique().tolist())
        else:
            print("未获取到任何数据")
            
    except Exception as e:
        print(f"执行过程中发生错误: {e}")


if __name__ == '__main__':
    import sys
    
    # 检查是否需要强制完整更新
    force_update = '--force' in sys.argv or '-f' in sys.argv
    
    if force_update:
        print("强制执行完整数据更新...")
    
    main(force_full_update=force_update)