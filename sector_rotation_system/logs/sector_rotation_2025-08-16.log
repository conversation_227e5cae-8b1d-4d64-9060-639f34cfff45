2025-08-16 20:22:13 | INFO     | indicators.calculator:_initialize_indicators:204 - Initialized 6 indicators
2025-08-16 20:22:13 | INFO     | __main__:__init__:49 - Sector Rotation Analysis System initialized with enhanced features
2025-08-16 20:22:13 | INFO     | __main__:run_indicators_only:286 - Running indicators calculation from 2024-01-01 to 2024-12-31
2025-08-16 20:22:13 | INFO     | data.preprocessor:load_and_preprocess:200 - Loading data from 2024-01-01 to 2024-12-31
2025-08-16 20:22:13 | INFO     | data.preprocessor:load_and_preprocess:204 - Using unified data file
2025-08-16 20:22:13 | INFO     | data.preprocessor:_load_unified_data:243 - Successfully loaded unified data file with utf-8 encoding: data/raw/price_data.csv, shape: (90370, 6)
2025-08-16 20:22:13 | ERROR    | __main__:run_indicators_only:315 - Indicators calculation failed: 'date'
2025-08-16 20:27:00 | INFO     | indicators.calculator:_initialize_indicators:204 - Initialized 6 indicators
2025-08-16 20:27:00 | INFO     | __main__:__init__:49 - Sector Rotation Analysis System initialized with enhanced features
2025-08-16 20:27:00 | INFO     | __main__:run_indicators_only:286 - Running indicators calculation from 2024-01-01 to 2024-12-31
2025-08-16 20:27:00 | INFO     | data.preprocessor:load_and_preprocess:200 - Loading data from 2024-01-01 to 2024-12-31
2025-08-16 20:27:00 | INFO     | data.preprocessor:load_and_preprocess:204 - Using unified data file
2025-08-16 20:27:00 | INFO     | data.preprocessor:_load_unified_data:243 - Successfully loaded unified data file with utf-8 encoding: data/raw/unified_sector_data.csv, shape: (92952, 6)
2025-08-16 20:27:01 | INFO     | data.preprocessor:_convert_mixed_date_formats:319 - Converted 92952 dates, 0 failed conversions
2025-08-16 20:27:01 | INFO     | data.preprocessor:_load_unified_data:284 - Unified data loaded successfully. Sector data: 8470, Benchmark data: 242
2025-08-16 20:27:01 | INFO     | data.preprocessor:_load_unified_data:285 - Final merged data shape: (8470, 7)
2025-08-16 20:27:01 | INFO     | data.preprocessor:remove_outliers:183 - Removed 260 outliers from column sector_close_price
2025-08-16 20:27:01 | INFO     | data.preprocessor:remove_outliers:183 - Removed 783 outliers from column sector_volume_amount
2025-08-16 20:27:01 | INFO     | data.preprocessor:remove_outliers:183 - Removed 20 outliers from column benchmark_price
2025-08-16 20:27:01 | INFO     | data.preprocessor:load_and_preprocess:225 - Data preprocessing completed. Final shape: (7407, 7)
2025-08-16 20:27:01 | INFO     | indicators.calculator:calculate_all_indicators:209 - Starting calculation of all indicators
2025-08-16 20:27:02 | INFO     | indicators.calculator:calculate_all_indicators:257 - Indicator calculation completed. Result shape: (241, 8)
2025-08-16 20:27:02 | INFO     | data.storage:save:62 - Data saved to data/output/indicators_2024-01-01_2024-12-31.parquet
2025-08-16 20:27:02 | INFO     | data.storage:save:62 - Data saved to data/output/indicators_2024-01-01_2024-12-31_backup_20250816_202702.parquet
2025-08-16 20:27:12 | INFO     | indicators.calculator:_initialize_indicators:204 - Initialized 6 indicators
2025-08-16 20:27:12 | INFO     | __main__:__init__:49 - Sector Rotation Analysis System initialized with enhanced features
2025-08-16 20:27:12 | INFO     | __main__:run_full_analysis:55 - Starting enhanced full analysis from 2024-01-01 to 2024-12-31
2025-08-16 20:27:12 | INFO     | __main__:run_full_analysis:59 - Step 0: Creating output structure
2025-08-16 20:27:12 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_202712/charts
2025-08-16 20:27:12 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_202712/reports
2025-08-16 20:27:12 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_202712/tables
2025-08-16 20:27:12 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_202712/data
2025-08-16 20:27:12 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_202712/logs
2025-08-16 20:27:12 | INFO     | utils.output_manager:create_output_structure:72 - Created output structure at: data/output/20250816_202712
2025-08-16 20:27:12 | INFO     | __main__:run_full_analysis:63 - Step 1: Data preprocessing
2025-08-16 20:27:12 | INFO     | data.preprocessor:load_and_preprocess:200 - Loading data from 2024-01-01 to 2024-12-31
2025-08-16 20:27:12 | INFO     | data.preprocessor:load_and_preprocess:204 - Using unified data file
2025-08-16 20:27:12 | INFO     | data.preprocessor:_load_unified_data:243 - Successfully loaded unified data file with utf-8 encoding: data/raw/unified_sector_data.csv, shape: (92952, 6)
2025-08-16 20:27:13 | INFO     | data.preprocessor:_convert_mixed_date_formats:319 - Converted 92952 dates, 0 failed conversions
2025-08-16 20:27:13 | INFO     | data.preprocessor:_load_unified_data:284 - Unified data loaded successfully. Sector data: 8470, Benchmark data: 242
2025-08-16 20:27:13 | INFO     | data.preprocessor:_load_unified_data:285 - Final merged data shape: (8470, 7)
2025-08-16 20:27:13 | INFO     | data.preprocessor:remove_outliers:183 - Removed 260 outliers from column sector_close_price
2025-08-16 20:27:13 | INFO     | data.preprocessor:remove_outliers:183 - Removed 783 outliers from column sector_volume_amount
2025-08-16 20:27:13 | INFO     | data.preprocessor:remove_outliers:183 - Removed 20 outliers from column benchmark_price
2025-08-16 20:27:13 | INFO     | data.preprocessor:load_and_preprocess:225 - Data preprocessing completed. Final shape: (7407, 7)
2025-08-16 20:27:13 | INFO     | __main__:run_full_analysis:70 - Loaded 7407 records with daily_return field
2025-08-16 20:27:13 | INFO     | __main__:run_full_analysis:73 - Step 2: Indicator calculation
2025-08-16 20:27:13 | INFO     | indicators.calculator:validate_input_data:346 - Data validation passed: 7407 records, 241 dates, 34 sectors
2025-08-16 20:27:13 | INFO     | indicators.calculator:calculate_all_indicators:209 - Starting calculation of all indicators
2025-08-16 20:27:13 | INFO     | indicators.calculator:calculate_all_indicators:257 - Indicator calculation completed. Result shape: (241, 8)
2025-08-16 20:27:13 | INFO     | __main__:run_full_analysis:84 - Calculated indicators for 241 periods
2025-08-16 20:27:13 | INFO     | __main__:run_full_analysis:87 - Step 3: Historical percentile calculation
2025-08-16 20:27:13 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:66 - Using effective window size: 241 (original: 252, data length: 241)
2025-08-16 20:27:13 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for sector_ranking_change
2025-08-16 20:27:13 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for relative_strength_dispersion
2025-08-16 20:27:13 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for sector_return_dispersion
2025-08-16 20:27:13 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for volume_hhi
2025-08-16 20:27:13 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for return_skewness
2025-08-16 20:27:13 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for return_kurtosis
2025-08-16 20:27:13 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for momentum_ranking_change
2025-08-16 20:27:13 | INFO     | __main__:run_full_analysis:93 - Step 4: Enhanced HHI analysis
2025-08-16 20:27:13 | INFO     | indicators.hhi_enhanced:calculate_hhi_with_thresholds:85 - Calculated HHI with thresholds: low=0.0565, high=0.0646
2025-08-16 20:27:13 | INFO     | __main__:run_full_analysis:97 - Step 5: Sector performance reporting
2025-08-16 20:27:13 | INFO     | reporting.sector_performance:generate_sector_performance_table:137 - Generated sector performance table with 34 sectors
2025-08-16 20:27:13 | INFO     | __main__:run_full_analysis:103 - Step 6: Correlation analysis
2025-08-16 20:27:13 | INFO     | reporting.table_generator:create_correlation_table:57 - Generated correlation matrix for 34 sectors
2025-08-16 20:27:13 | INFO     | reporting.table_generator:identify_high_correlation_pairs:97 - Found 277 high correlation pairs and 0 low correlation pairs
2025-08-16 20:27:13 | INFO     | __main__:run_full_analysis:108 - Step 7: Index concentration analysis
2025-08-16 20:27:13 | INFO     | indicators.index_concentration:calculate_index_concentration:71 - Calculated index concentration for 241 trading days
2025-08-16 20:27:13 | INFO     | __main__:run_full_analysis:112 - Step 8: Data storage
2025-08-16 20:27:13 | INFO     | data.storage:save:62 - Data saved to data/output/indicators_2024-01-01_2024-12-31.parquet
2025-08-16 20:27:13 | INFO     | data.storage:save:62 - Data saved to data/output/indicators_2024-01-01_2024-12-31_backup_20250816_202713.parquet
2025-08-16 20:27:14 | INFO     | reporting.sector_performance:save_performance_table:206 - Performance table saved to data/output/20250816_202712/tables/sector_performance_20250816_202712.xlsx
2025-08-16 20:27:14 | INFO     | reporting.sector_performance:save_performance_table:210 - Performance table saved to data/output/20250816_202712/tables/sector_performance_20250816_202712.csv
2025-08-16 20:27:14 | INFO     | reporting.table_generator:save_table_to_multiple_formats:188 - Table saved as CSV: data/output/20250816_202712/tables/correlation_matrix_20250816_202712.csv
2025-08-16 20:27:14 | INFO     | reporting.table_generator:save_table_to_multiple_formats:196 - Table saved as Excel: data/output/20250816_202712/tables/correlation_matrix_20250816_202712.xlsx
2025-08-16 20:27:14 | INFO     | indicators.index_concentration:save_concentration_data:331 - Index concentration data saved to data/output/20250816_202712/tables/index_concentration_20250816_202712.xlsx
2025-08-16 20:27:14 | INFO     | indicators.index_concentration:save_concentration_data:335 - Index concentration data saved to data/output/20250816_202712/tables/index_concentration_20250816_202712.csv
2025-08-16 20:27:14 | INFO     | __main__:run_full_analysis:134 - Step 9: Enhanced visualization
2025-08-16 20:27:14 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_202712/charts/sector_rotation_sector_ranking_change_20250816_202712.png
2025-08-16 20:27:14 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_202712/charts/sector_rotation_relative_strength_dispersion_20250816_202712.png
2025-08-16 20:27:14 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_202712/charts/sector_rotation_sector_return_dispersion_20250816_202712.png
2025-08-16 20:27:15 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_202712/charts/sector_rotation_volume_hhi_20250816_202712.png
2025-08-16 20:27:15 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_202712/charts/sector_rotation_return_skewness_20250816_202712.png
2025-08-16 20:27:15 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_202712/charts/sector_rotation_return_kurtosis_20250816_202712.png
2025-08-16 20:27:15 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_202712/charts/sector_rotation_momentum_ranking_change_20250816_202712.png
2025-08-16 20:27:16 | INFO     | visualization.enhanced_plotter:plot_stacked_area_chart:121 - Stacked area chart saved to data/output/20250816_202712/charts/sector_concentration_stacked_area_20250816_202715.png
2025-08-16 20:27:16 | INFO     | visualization.enhanced_plotter:plot_top_sectors_trend:210 - Top sectors trend chart saved to data/output/20250816_202712/charts/top_10_sectors_trend_20250816_202716.png
2025-08-16 20:27:16 | INFO     | visualization.enhanced_plotter:plot_correlation_heatmap:287 - Correlation heatmap saved to data/output/20250816_202712/charts/sector_correlation_heatmap_20250816_202716.png
2025-08-16 20:27:17 | INFO     | indicators.hhi_enhanced:plot_hhi_with_thresholds:188 - Enhanced HHI chart saved to data/output/20250816_202712/charts/hhi_enhanced_analysis_20250816_202716.png
2025-08-16 20:27:17 | INFO     | indicators.index_concentration:plot_index_concentration_trend:173 - Index concentration trend chart saved to data/output/20250816_202712/charts/index_concentration_trend_20250816_202717.png
2025-08-16 20:27:17 | INFO     | indicators.index_concentration:plot_index_concentration_stacked:252 - Index concentration stacked chart saved to data/output/20250816_202712/charts/index_concentration_stacked_20250816_202717.png
2025-08-16 20:27:17 | INFO     | __main__:run_full_analysis:201 - Step 10: Report generation
2025-08-16 20:27:17 | INFO     | visualization.plotter:save_report:415 - Report saved to data/output/20250816_202712/reports/sector_performance_20250816_202712.md
2025-08-16 20:27:17 | INFO     | indicators.hhi_enhanced:generate_concentration_summary:238 - Generated concentration summary
2025-08-16 20:27:17 | INFO     | visualization.plotter:save_report:415 - Report saved to data/output/20250816_202712/reports/hhi_concentration_20250816_202712.md
2025-08-16 20:27:17 | INFO     | indicators.hhi_enhanced:generate_concentration_summary:238 - Generated concentration summary
2025-08-16 20:27:17 | INFO     | indicators.index_concentration:generate_concentration_summary:291 - Generated index concentration summary
2025-08-16 20:27:17 | INFO     | reporting.comprehensive_report:generate_comprehensive_report:48 - Comprehensive report saved to: data/output/20250816_202712/reports/comprehensive_analysis_report_20250816_202712.md
2025-08-16 20:27:17 | WARNING  | utils.output_manager:create_latest_symlinks:101 - Failed to create symlink, copying instead: [Errno 17] File exists: '20250816_202712' -> 'data/output/latest'
2025-08-16 20:27:17 | ERROR    | __main__:run_full_analysis:281 - Enhanced analysis failed: [Errno 17] File exists: 'data/output/latest'
2025-08-16 20:36:59 | INFO     | indicators.calculator:_initialize_indicators:204 - Initialized 6 indicators
2025-08-16 20:36:59 | INFO     | __main__:__init__:49 - Sector Rotation Analysis System initialized with enhanced features
2025-08-16 20:36:59 | INFO     | __main__:run_full_analysis:55 - Starting enhanced full analysis from 2024-08-01 to 2025-08-15
2025-08-16 20:36:59 | INFO     | __main__:run_full_analysis:59 - Step 0: Creating output structure
2025-08-16 20:36:59 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_203659/charts
2025-08-16 20:36:59 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_203659/reports
2025-08-16 20:36:59 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_203659/tables
2025-08-16 20:36:59 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_203659/data
2025-08-16 20:36:59 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_203659/logs
2025-08-16 20:36:59 | INFO     | utils.output_manager:create_output_structure:72 - Created output structure at: data/output/20250816_203659
2025-08-16 20:36:59 | INFO     | __main__:run_full_analysis:63 - Step 1: Data preprocessing
2025-08-16 20:36:59 | INFO     | data.preprocessor:load_and_preprocess:200 - Loading data from 2024-08-01 to 2025-08-15
2025-08-16 20:36:59 | INFO     | data.preprocessor:load_and_preprocess:204 - Using unified data file
2025-08-16 20:36:59 | INFO     | data.preprocessor:_load_unified_data:243 - Successfully loaded unified data file with utf-8 encoding: data/raw/unified_sector_data.csv, shape: (92952, 6)
2025-08-16 20:37:00 | INFO     | data.preprocessor:_convert_mixed_date_formats:319 - Converted 92952 dates, 0 failed conversions
2025-08-16 20:37:00 | INFO     | data.preprocessor:_load_unified_data:284 - Unified data loaded successfully. Sector data: 8855, Benchmark data: 253
2025-08-16 20:37:00 | INFO     | data.preprocessor:_load_unified_data:285 - Final merged data shape: (8855, 7)
2025-08-16 20:37:00 | INFO     | data.preprocessor:remove_outliers:183 - Removed 262 outliers from column sector_close_price
2025-08-16 20:37:00 | INFO     | data.preprocessor:remove_outliers:183 - Removed 839 outliers from column sector_volume_amount
2025-08-16 20:37:00 | INFO     | data.preprocessor:remove_outliers:183 - Removed 1308 outliers from column benchmark_price
2025-08-16 20:37:00 | INFO     | data.preprocessor:load_and_preprocess:225 - Data preprocessing completed. Final shape: (6446, 7)
2025-08-16 20:37:00 | INFO     | __main__:run_full_analysis:70 - Loaded 6446 records with daily_return field
2025-08-16 20:37:00 | INFO     | __main__:run_full_analysis:73 - Step 2: Indicator calculation
2025-08-16 20:37:00 | INFO     | indicators.calculator:validate_input_data:346 - Data validation passed: 6446 records, 214 dates, 33 sectors
2025-08-16 20:37:00 | INFO     | indicators.calculator:calculate_all_indicators:209 - Starting calculation of all indicators
2025-08-16 20:37:01 | INFO     | indicators.calculator:calculate_all_indicators:257 - Indicator calculation completed. Result shape: (214, 8)
2025-08-16 20:37:01 | INFO     | __main__:run_full_analysis:84 - Calculated indicators for 214 periods
2025-08-16 20:37:01 | INFO     | __main__:run_full_analysis:87 - Step 3: Historical percentile calculation
2025-08-16 20:37:01 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:66 - Using effective window size: 214 (original: 252, data length: 214)
2025-08-16 20:37:01 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for sector_ranking_change
2025-08-16 20:37:01 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for relative_strength_dispersion
2025-08-16 20:37:01 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for sector_return_dispersion
2025-08-16 20:37:01 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for volume_hhi
2025-08-16 20:37:01 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for return_skewness
2025-08-16 20:37:01 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for return_kurtosis
2025-08-16 20:37:01 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for momentum_ranking_change
2025-08-16 20:37:01 | INFO     | __main__:run_full_analysis:93 - Step 4: Enhanced HHI analysis
2025-08-16 20:37:01 | INFO     | indicators.hhi_enhanced:calculate_hhi_with_thresholds:85 - Calculated HHI with thresholds: low=0.0581, high=0.0639
2025-08-16 20:37:01 | INFO     | __main__:run_full_analysis:97 - Step 5: Sector performance reporting
2025-08-16 20:37:01 | INFO     | reporting.sector_performance:generate_sector_performance_table:137 - Generated sector performance table with 33 sectors
2025-08-16 20:37:01 | INFO     | __main__:run_full_analysis:103 - Step 6: Correlation analysis
2025-08-16 20:37:01 | INFO     | reporting.table_generator:create_correlation_table:57 - Generated correlation matrix for 33 sectors
2025-08-16 20:37:01 | INFO     | reporting.table_generator:identify_high_correlation_pairs:97 - Found 265 high correlation pairs and 5 low correlation pairs
2025-08-16 20:37:01 | INFO     | __main__:run_full_analysis:108 - Step 7: Index concentration analysis
2025-08-16 20:37:01 | INFO     | indicators.index_concentration:calculate_index_concentration:71 - Calculated index concentration for 214 trading days
2025-08-16 20:37:01 | INFO     | __main__:run_full_analysis:112 - Step 8: Data storage
2025-08-16 20:37:01 | INFO     | data.storage:save:62 - Data saved to data/output/indicators_2024-08-01_2025-08-15.parquet
2025-08-16 20:37:01 | INFO     | data.storage:save:62 - Data saved to data/output/indicators_2024-08-01_2025-08-15_backup_20250816_203701.parquet
2025-08-16 20:37:01 | INFO     | reporting.sector_performance:save_performance_table:206 - Performance table saved to data/output/20250816_203659/tables/sector_performance_20250816_203659.xlsx
2025-08-16 20:37:01 | INFO     | reporting.sector_performance:save_performance_table:210 - Performance table saved to data/output/20250816_203659/tables/sector_performance_20250816_203659.csv
2025-08-16 20:37:01 | INFO     | reporting.table_generator:save_table_to_multiple_formats:188 - Table saved as CSV: data/output/20250816_203659/tables/correlation_matrix_20250816_203659.csv
2025-08-16 20:37:01 | INFO     | reporting.table_generator:save_table_to_multiple_formats:196 - Table saved as Excel: data/output/20250816_203659/tables/correlation_matrix_20250816_203659.xlsx
2025-08-16 20:37:01 | INFO     | indicators.index_concentration:save_concentration_data:331 - Index concentration data saved to data/output/20250816_203659/tables/index_concentration_20250816_203659.xlsx
2025-08-16 20:37:01 | INFO     | indicators.index_concentration:save_concentration_data:335 - Index concentration data saved to data/output/20250816_203659/tables/index_concentration_20250816_203659.csv
2025-08-16 20:37:01 | INFO     | __main__:run_full_analysis:134 - Step 9: Enhanced visualization
2025-08-16 20:37:01 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_203659/charts/sector_rotation_sector_ranking_change_20250816_203659.png
2025-08-16 20:37:01 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_203659/charts/sector_rotation_relative_strength_dispersion_20250816_203659.png
2025-08-16 20:37:02 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_203659/charts/sector_rotation_sector_return_dispersion_20250816_203659.png
2025-08-16 20:37:02 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_203659/charts/sector_rotation_volume_hhi_20250816_203659.png
2025-08-16 20:37:02 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_203659/charts/sector_rotation_return_skewness_20250816_203659.png
2025-08-16 20:37:02 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_203659/charts/sector_rotation_return_kurtosis_20250816_203659.png
2025-08-16 20:37:02 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_203659/charts/sector_rotation_momentum_ranking_change_20250816_203659.png
2025-08-16 20:37:03 | INFO     | visualization.enhanced_plotter:plot_stacked_area_chart:121 - Stacked area chart saved to data/output/20250816_203659/charts/sector_concentration_stacked_area_20250816_203702.png
2025-08-16 20:37:03 | INFO     | visualization.enhanced_plotter:plot_top_sectors_trend:210 - Top sectors trend chart saved to data/output/20250816_203659/charts/top_10_sectors_trend_20250816_203703.png
2025-08-16 20:37:04 | INFO     | visualization.enhanced_plotter:plot_correlation_heatmap:287 - Correlation heatmap saved to data/output/20250816_203659/charts/sector_correlation_heatmap_20250816_203703.png
2025-08-16 20:37:04 | INFO     | indicators.hhi_enhanced:plot_hhi_with_thresholds:188 - Enhanced HHI chart saved to data/output/20250816_203659/charts/hhi_enhanced_analysis_20250816_203704.png
2025-08-16 20:37:04 | INFO     | indicators.index_concentration:plot_index_concentration_trend:173 - Index concentration trend chart saved to data/output/20250816_203659/charts/index_concentration_trend_20250816_203704.png
2025-08-16 20:37:04 | INFO     | indicators.index_concentration:plot_index_concentration_stacked:252 - Index concentration stacked chart saved to data/output/20250816_203659/charts/index_concentration_stacked_20250816_203704.png
2025-08-16 20:37:04 | INFO     | __main__:run_full_analysis:201 - Step 10: Report generation
2025-08-16 20:37:04 | INFO     | visualization.plotter:save_report:415 - Report saved to data/output/20250816_203659/reports/sector_performance_20250816_203659.md
2025-08-16 20:37:04 | INFO     | indicators.hhi_enhanced:generate_concentration_summary:238 - Generated concentration summary
2025-08-16 20:37:04 | INFO     | visualization.plotter:save_report:415 - Report saved to data/output/20250816_203659/reports/hhi_concentration_20250816_203659.md
2025-08-16 20:37:04 | INFO     | indicators.hhi_enhanced:generate_concentration_summary:238 - Generated concentration summary
2025-08-16 20:37:04 | INFO     | indicators.index_concentration:generate_concentration_summary:291 - Generated index concentration summary
2025-08-16 20:37:04 | INFO     | reporting.comprehensive_report:generate_comprehensive_report:48 - Comprehensive report saved to: data/output/20250816_203659/reports/comprehensive_analysis_report_20250816_203659.md
2025-08-16 20:37:04 | WARNING  | utils.output_manager:create_latest_symlinks:101 - Failed to create symlink, copying instead: [Errno 17] File exists: '20250816_203659' -> 'data/output/latest'
2025-08-16 20:37:04 | ERROR    | __main__:run_full_analysis:281 - Enhanced analysis failed: [Errno 17] File exists: 'data/output/latest'
2025-08-16 21:01:01 | INFO     | indicators.calculator:_initialize_indicators:204 - Initialized 6 indicators
2025-08-16 21:01:01 | INFO     | __main__:__init__:49 - Sector Rotation Analysis System initialized with enhanced features
2025-08-16 21:01:01 | INFO     | __main__:run_full_analysis:55 - Starting enhanced full analysis from 2024-01-01 to 2024-12-31
2025-08-16 21:01:01 | INFO     | __main__:run_full_analysis:59 - Step 0: Creating output structure
2025-08-16 21:01:01 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_210101/charts
2025-08-16 21:01:01 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_210101/reports
2025-08-16 21:01:01 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_210101/tables
2025-08-16 21:01:01 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_210101/data
2025-08-16 21:01:01 | INFO     | utils.output_manager:create_output_structure:59 - Created directory: data/output/20250816_210101/logs
2025-08-16 21:01:01 | INFO     | utils.output_manager:create_output_structure:72 - Created output structure at: data/output/20250816_210101
2025-08-16 21:01:01 | INFO     | __main__:run_full_analysis:63 - Step 1: Data preprocessing
2025-08-16 21:01:01 | INFO     | data.preprocessor:load_and_preprocess:200 - Loading data from 2024-01-01 to 2024-12-31
2025-08-16 21:01:01 | INFO     | data.preprocessor:load_and_preprocess:204 - Using unified data file
2025-08-16 21:01:01 | INFO     | data.preprocessor:_load_unified_data:243 - Successfully loaded unified data file with utf-8 encoding: data/raw/unified_sector_data.csv, shape: (92952, 6)
2025-08-16 21:01:02 | INFO     | data.preprocessor:_convert_mixed_date_formats:319 - Converted 92952 dates, 0 failed conversions
2025-08-16 21:01:02 | INFO     | data.preprocessor:_load_unified_data:284 - Unified data loaded successfully. Sector data: 8470, Benchmark data: 242
2025-08-16 21:01:02 | INFO     | data.preprocessor:_load_unified_data:285 - Final merged data shape: (8470, 7)
2025-08-16 21:01:02 | INFO     | data.preprocessor:remove_outliers:183 - Removed 260 outliers from column sector_close_price
2025-08-16 21:01:02 | INFO     | data.preprocessor:remove_outliers:183 - Removed 783 outliers from column sector_volume_amount
2025-08-16 21:01:02 | INFO     | data.preprocessor:remove_outliers:183 - Removed 20 outliers from column benchmark_price
2025-08-16 21:01:02 | INFO     | data.preprocessor:load_and_preprocess:225 - Data preprocessing completed. Final shape: (7407, 7)
2025-08-16 21:01:02 | INFO     | __main__:run_full_analysis:70 - Loaded 7407 records with daily_return field
2025-08-16 21:01:02 | INFO     | __main__:run_full_analysis:73 - Step 2: Indicator calculation
2025-08-16 21:01:02 | INFO     | indicators.calculator:validate_input_data:346 - Data validation passed: 7407 records, 241 dates, 34 sectors
2025-08-16 21:01:02 | INFO     | indicators.calculator:calculate_all_indicators:209 - Starting calculation of all indicators
2025-08-16 21:01:02 | INFO     | indicators.calculator:calculate_all_indicators:257 - Indicator calculation completed. Result shape: (241, 8)
2025-08-16 21:01:02 | INFO     | __main__:run_full_analysis:84 - Calculated indicators for 241 periods
2025-08-16 21:01:02 | INFO     | __main__:run_full_analysis:87 - Step 3: Historical percentile calculation
2025-08-16 21:01:02 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:66 - Using effective window size: 241 (original: 252, data length: 241)
2025-08-16 21:01:02 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for sector_ranking_change
2025-08-16 21:01:02 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for relative_strength_dispersion
2025-08-16 21:01:02 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for sector_return_dispersion
2025-08-16 21:01:02 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for volume_hhi
2025-08-16 21:01:02 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for return_skewness
2025-08-16 21:01:02 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for return_kurtosis
2025-08-16 21:01:02 | INFO     | indicators.percentile_calculator:calculate_rolling_percentiles:79 - Calculated percentiles for momentum_ranking_change
2025-08-16 21:01:02 | INFO     | __main__:run_full_analysis:93 - Step 4: Enhanced HHI analysis
2025-08-16 21:01:02 | INFO     | indicators.hhi_enhanced:calculate_hhi_with_thresholds:85 - Calculated HHI with thresholds: low=0.0565, high=0.0646
2025-08-16 21:01:02 | INFO     | __main__:run_full_analysis:97 - Step 5: Sector performance reporting
2025-08-16 21:01:02 | INFO     | reporting.sector_performance:generate_sector_performance_table:137 - Generated sector performance table with 34 sectors
2025-08-16 21:01:02 | INFO     | __main__:run_full_analysis:103 - Step 6: Correlation analysis
2025-08-16 21:01:02 | INFO     | reporting.table_generator:create_correlation_table:57 - Generated correlation matrix for 34 sectors
2025-08-16 21:01:02 | INFO     | reporting.table_generator:identify_high_correlation_pairs:97 - Found 277 high correlation pairs and 0 low correlation pairs
2025-08-16 21:01:02 | INFO     | __main__:run_full_analysis:108 - Step 7: Index concentration analysis
2025-08-16 21:01:02 | INFO     | indicators.index_concentration:calculate_index_concentration:71 - Calculated index concentration for 241 trading days
2025-08-16 21:01:02 | INFO     | __main__:run_full_analysis:112 - Step 8: Data storage
2025-08-16 21:01:02 | INFO     | data.storage:save:62 - Data saved to data/output/indicators_2024-01-01_2024-12-31.parquet
2025-08-16 21:01:02 | INFO     | data.storage:save:62 - Data saved to data/output/indicators_2024-01-01_2024-12-31_backup_20250816_210102.parquet
2025-08-16 21:01:03 | INFO     | reporting.sector_performance:save_performance_table:206 - Performance table saved to data/output/20250816_210101/tables/sector_performance_20250816_210101.xlsx
2025-08-16 21:01:03 | INFO     | reporting.sector_performance:save_performance_table:210 - Performance table saved to data/output/20250816_210101/tables/sector_performance_20250816_210101.csv
2025-08-16 21:01:03 | INFO     | reporting.table_generator:save_table_to_multiple_formats:188 - Table saved as CSV: data/output/20250816_210101/tables/correlation_matrix_20250816_210101.csv
2025-08-16 21:01:03 | INFO     | reporting.table_generator:save_table_to_multiple_formats:196 - Table saved as Excel: data/output/20250816_210101/tables/correlation_matrix_20250816_210101.xlsx
2025-08-16 21:01:03 | INFO     | indicators.index_concentration:save_concentration_data:331 - Index concentration data saved to data/output/20250816_210101/tables/index_concentration_20250816_210101.xlsx
2025-08-16 21:01:03 | INFO     | indicators.index_concentration:save_concentration_data:335 - Index concentration data saved to data/output/20250816_210101/tables/index_concentration_20250816_210101.csv
2025-08-16 21:01:03 | INFO     | __main__:run_full_analysis:134 - Step 9: Enhanced visualization
2025-08-16 21:01:03 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_210101/charts/sector_rotation_sector_ranking_change_20250816_210101.png
2025-08-16 21:01:03 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_210101/charts/sector_rotation_relative_strength_dispersion_20250816_210101.png
2025-08-16 21:01:03 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_210101/charts/sector_rotation_sector_return_dispersion_20250816_210101.png
2025-08-16 21:01:03 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_210101/charts/sector_rotation_volume_hhi_20250816_210101.png
2025-08-16 21:01:04 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_210101/charts/sector_rotation_return_skewness_20250816_210101.png
2025-08-16 21:01:04 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_210101/charts/sector_rotation_return_kurtosis_20250816_210101.png
2025-08-16 21:01:04 | INFO     | visualization.plotter:save_figure:46 - Chart saved to data/output/20250816_210101/charts/sector_rotation_momentum_ranking_change_20250816_210101.png
2025-08-16 21:01:04 | INFO     | visualization.enhanced_plotter:plot_stacked_area_chart:126 - Stacked area chart saved to data/output/20250816_210101/charts/sector_concentration_stacked_area_20250816_210104.png
2025-08-16 21:01:05 | INFO     | visualization.enhanced_plotter:plot_top_sectors_trend:220 - Top sectors trend chart saved to data/output/20250816_210101/charts/top_10_sectors_trend_20250816_210105.png
2025-08-16 21:01:05 | INFO     | visualization.enhanced_plotter:plot_correlation_heatmap:297 - Correlation heatmap saved to data/output/20250816_210101/charts/sector_correlation_heatmap_20250816_210105.png
2025-08-16 21:01:05 | INFO     | indicators.hhi_enhanced:plot_hhi_with_thresholds:188 - Enhanced HHI chart saved to data/output/20250816_210101/charts/hhi_enhanced_analysis_20250816_210105.png
2025-08-16 21:01:06 | INFO     | indicators.index_concentration:plot_index_concentration_trend:173 - Index concentration trend chart saved to data/output/20250816_210101/charts/index_concentration_trend_20250816_210106.png
2025-08-16 21:01:06 | INFO     | indicators.index_concentration:plot_index_concentration_stacked:252 - Index concentration stacked chart saved to data/output/20250816_210101/charts/index_concentration_stacked_20250816_210106.png
2025-08-16 21:01:06 | INFO     | __main__:run_full_analysis:201 - Step 10: Report generation
2025-08-16 21:01:06 | INFO     | visualization.plotter:save_report:415 - Report saved to data/output/20250816_210101/reports/sector_performance_20250816_210101.md
2025-08-16 21:01:06 | INFO     | indicators.hhi_enhanced:generate_concentration_summary:238 - Generated concentration summary
2025-08-16 21:01:06 | INFO     | visualization.plotter:save_report:415 - Report saved to data/output/20250816_210101/reports/hhi_concentration_20250816_210101.md
2025-08-16 21:01:06 | INFO     | indicators.hhi_enhanced:generate_concentration_summary:238 - Generated concentration summary
2025-08-16 21:01:06 | INFO     | indicators.index_concentration:generate_concentration_summary:291 - Generated index concentration summary
2025-08-16 21:01:06 | INFO     | reporting.comprehensive_report:generate_comprehensive_report:48 - Comprehensive report saved to: data/output/20250816_210101/reports/comprehensive_analysis_report_20250816_210101.md
2025-08-16 21:01:06 | WARNING  | utils.output_manager:create_latest_symlinks:101 - Failed to create symlink, copying instead: [Errno 17] File exists: '20250816_210101' -> 'data/output/latest'
2025-08-16 21:01:06 | ERROR    | __main__:run_full_analysis:281 - Enhanced analysis failed: [Errno 17] File exists: 'data/output/latest'
