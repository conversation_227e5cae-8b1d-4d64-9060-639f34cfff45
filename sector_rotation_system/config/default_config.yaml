data:
  benchmark_index: 000300.SH
  benchmark_name: 沪深300
  data_source: csv
  date_format: '%Y-%m-%d'
  start_date: '2015-01-01'
  storage_format: parquet
  unified_data_file: data/raw/unified_sector_data.csv
  wind_data_file: data/raw/price_data.csv
  use_unified_file: true
  other_indices:
  - 000300.SH
  - 000905.SH
  - 000852.SH
  - 000016.SH
  shenwan_industry_indices:
  - 801010.SI
  - 801030.SI
  - 801040.SI
  - 801050.SI
  - 801080.SI
  - 801110.SI
  - 801120.SI
  - 801130.SI
  - 801140.SI
  - 801150.SI
  - 801160.SI
  - 801170.SI
  - 801180.SI
  - 801200.SI
  - 801210.SI
  - 801230.SI
  - 801710.SI
  - 801720.SI
  - 801730.SI
  - 801740.SI
  - 801750.SI
  - 801760.SI
  - 801770.SI
  - 801780.SI
  - 801790.SI
  - 801880.SI
  - 801890.SI
  - 801950.SI
  - 801960.SI
  - 801970.SI
  - 801980.SI
  start_date: '2015-01-01'
  storage_format: parquet
  unified_data_file: data/raw/unified_sector_data.csv
  use_unified_file: true
indicator:
  hhi_high_threshold_percentile: 75
  hhi_low_threshold_percentile: 25
  min_data_points: 30
  momentum_lookback_period: 20
  percentile_window: 252
  ranking_window: 5
reporting:
  sort_by: weekly
  time_periods:
    monthly: 22
    weekly: 5
    yearly: 252
storage:
  backup_enabled: true
  base_path: data/output
  compression: snappy
  date_folder_format: '%Y%m%d_%H%M%S'
  use_date_folders: true
visualization:
  color_palette: husl
  correlation_threshold_high: 0.7
  correlation_threshold_low: -0.7
  dpi: 300
  enable_multi_indicator_comparison: false
  figure_size:
  - 12
  - 8
  style: seaborn-v0_8
  top_sectors_count: 10
  # 图表数据源配置
  chart_data_sources:
    # 申万行业指数相关图表
    shenwan_charts:
      - sector_rotation_*
      - sector_concentration_stacked_area
      - top_10_sectors_trend
      - sector_correlation_heatmap
      - hhi_enhanced_analysis
    # 其他指数相关图表  
    other_indices_charts:
      - index_concentration_stacked
      - index_concentration_trend
  top_sectors_count: 10
