"""
配置管理模块
统一管理系统参数，确保系统的灵活性和可配置性
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class IndicatorConfig:
    """指标计算配置"""
    momentum_lookback_period: int = 20  # 动量回溯期
    ranking_window: int = 5  # 排名变化时间窗口
    min_data_points: int = 30  # 最小数据点数量
    percentile_window: int = 252  # 历史百分位计算窗口
    hhi_low_threshold_percentile: int = 25  # HHI低集中度阈值百分位
    hhi_high_threshold_percentile: int = 75  # HHI高集中度阈值百分位


@dataclass
class DataConfig:
    """数据配置"""
    benchmark_index: str = "000300.SH"  # 基准指数代码
    benchmark_name: str = "沪深300"  # 基准指数名称
    data_source: str = "csv"  # 数据源类型
    storage_format: str = "parquet"  # 存储格式
    date_format: str = "%Y-%m-%d"  # 日期格式
    start_date: str = "2015-01-01"  # 数据开始日期
    unified_data_file: str = "data/raw/unified_sector_data.csv"  # 系统格式数据文件路径
    wind_data_file: str = "data/raw/price_data.csv"  # Wind格式数据文件路径
    use_unified_file: bool = True  # 是否使用统一数据文件
    index_list: list = None  # 指数列表（向后兼容）
    shenwan_industry_indices: list = None  # 申万行业指数
    other_indices: list = None  # 其他指数

    def __post_init__(self):
        if self.index_list is None:
            self.index_list = ["000300.SH", "000905.SH", "000852.SH"]
        if self.shenwan_industry_indices is None:
            self.shenwan_industry_indices = []
        if self.other_indices is None:
            self.other_indices = ["000300.SH", "000905.SH", "000852.SH", "000016.SH"]


@dataclass
class VisualizationConfig:
    """可视化配置"""
    figure_size: tuple = (12, 8)  # 图表尺寸
    dpi: int = 300  # 图像分辨率
    style: str = "seaborn-v0_8"  # 图表样式
    color_palette: str = "husl"  # 颜色调色板
    enable_multi_indicator_comparison: bool = False  # 是否启用多指标对比
    top_sectors_count: int = 10  # 成交额占比前十行业数量
    correlation_threshold_high: float = 0.7  # 高相关性阈值
    correlation_threshold_low: float = -0.7  # 低相关性阈值
    chart_data_sources: dict = None  # 图表数据源配置

    def __post_init__(self):
        if self.chart_data_sources is None:
            self.chart_data_sources = {
                'shenwan_charts': [
                    'sector_rotation_*',
                    'sector_concentration_stacked_area',
                    'top_10_sectors_trend',
                    'sector_correlation_heatmap',
                    'hhi_enhanced_analysis'
                ],
                'other_indices_charts': [
                    'index_concentration_stacked',
                    'index_concentration_trend'
                ]
            }
    

@dataclass
class StorageConfig:
    """存储配置"""
    base_path: str = "data/output"  # 基础存储路径
    backup_enabled: bool = True  # 是否启用备份
    compression: str = "snappy"  # 压缩格式
    use_date_folders: bool = True  # 是否使用日期文件夹
    date_folder_format: str = "%Y%m%d_%H%M%S"  # 日期文件夹格式


@dataclass
class ReportingConfig:
    """报告配置"""
    time_periods: Dict[str, int] = None  # 时间段配置
    sort_by: str = "weekly"  # 表格排序方式

    def __post_init__(self):
        if self.time_periods is None:
            self.time_periods = {
                "weekly": 5,      # 最近5个交易日
                "monthly": 22,    # 当月交易日（近似）
                "yearly": 252     # 当年交易日（近似）
            }


@dataclass
class SystemConfig:
    """系统总配置"""
    indicator: IndicatorConfig
    data: DataConfig
    visualization: VisualizationConfig
    storage: StorageConfig
    reporting: ReportingConfig
    
    @classmethod
    def from_yaml(cls, config_path: str) -> 'SystemConfig':
        """从YAML文件加载配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)

        return cls(
            indicator=IndicatorConfig(**config_dict.get('indicator', {})),
            data=DataConfig(**config_dict.get('data', {})),
            visualization=VisualizationConfig(**config_dict.get('visualization', {})),
            storage=StorageConfig(**config_dict.get('storage', {})),
            reporting=ReportingConfig(**config_dict.get('reporting', {}))
        )

    def to_yaml(self, config_path: str) -> None:
        """保存配置到YAML文件"""
        config_dict = {
            'indicator': asdict(self.indicator),
            'data': asdict(self.data),
            'visualization': asdict(self.visualization),
            'storage': asdict(self.storage),
            'reporting': asdict(self.reporting)
        }

        Path(config_path).parent.mkdir(parents=True, exist_ok=True)
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config/default_config.yaml"
        self._config = None
        
    @property
    def config(self) -> SystemConfig:
        """获取配置实例"""
        if self._config is None:
            self._config = self._load_config()
        return self._config
    
    def _load_config(self) -> SystemConfig:
        """加载配置"""
        if Path(self.config_path).exists():
            return SystemConfig.from_yaml(self.config_path)
        else:
            # 创建默认配置
            default_config = SystemConfig(
                indicator=IndicatorConfig(),
                data=DataConfig(),
                visualization=VisualizationConfig(),
                storage=StorageConfig(),
                reporting=ReportingConfig()
            )
            default_config.to_yaml(self.config_path)
            return default_config

    def update_config(self, **kwargs) -> None:
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)

        # 保存更新后的配置
        self.config.to_yaml(self.config_path)

    def get_indicator_config(self) -> IndicatorConfig:
        """获取指标配置"""
        return self.config.indicator

    def get_data_config(self) -> DataConfig:
        """获取数据配置"""
        return self.config.data

    def get_visualization_config(self) -> VisualizationConfig:
        """获取可视化配置"""
        return self.config.visualization

    def get_storage_config(self) -> StorageConfig:
        """获取存储配置"""
        return self.config.storage

    def get_reporting_config(self) -> ReportingConfig:
        """获取报告配置"""
        return self.config.reporting


# 全局配置管理器实例
config_manager = ConfigManager()
