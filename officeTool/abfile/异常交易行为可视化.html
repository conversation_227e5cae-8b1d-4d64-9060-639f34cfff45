<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>异常交易行为监控规则 - 思维导图可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
        }
        
        .mindmap-container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .mindmap-content {
            padding: 40px;
            background: white;
        }
        
        .usage-guide {
            background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
            border: 2px solid #3498db;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.1);
        }
        
        .usage-guide h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
        }
        
        .usage-guide h3::before {
            content: "📖";
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .usage-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        
        .usage-section {
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .usage-section h4 {
            color: #34495e;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .color-legend {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 8px;
        }
        
        .color-item {
            display: flex;
            align-items: center;
            font-size: 0.9em;
        }
        
        .color-dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 6px;
            border: 1px solid #ddd;
        }
        
        .usage-list {
            list-style: none;
            padding: 0;
        }
        
        .usage-list li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
            font-size: 0.95em;
            line-height: 1.4;
        }
        
        .usage-list li::before {
            content: "•";
            color: #3498db;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .branches-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 50px;
            margin-top: 30px;
            justify-items: center;
        }
        
        .category-branch {
            width: 100%;
            max-width: 500px;
            position: relative;
        }
        
        .category-node {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 18px 30px;
            border-radius: 30px;
            font-size: 1.4em;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
            margin-bottom: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .category-node:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(231, 76, 60, 0.4);
        }
        
        .risk-items-container {
            margin-left: 25px;
            border-left: 4px solid #e74c3c;
            padding-left: 25px;
        }
        
        .risk-item {
            margin-bottom: 18px;
            position: relative;
        }
        
        .risk-node {
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            position: relative;
            font-size: 1.05em;
        }
        
        .risk-node:hover {
            transform: translateX(8px);
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
        }
        
        /* 监控项目颜色方案 - 调整优先级描述 */
        .risk-node.priority-high {
            background: #FF0000;
            color: white;
        }
        
        .risk-node.priority-medium {
            background: #FFFF00;
            color: #333;
        }
        
        .risk-node.priority-low {
            background: #008000;
            color: white;
        }
        
        .triggers-container {
            display: none;
            margin-top: 15px;
            margin-left: 25px;
            border-left: 3px solid #ddd;
            padding-left: 20px;
        }
        
        .risk-item.expanded .triggers-container {
            display: block;
        }
        
        .trigger-node {
            margin: 12px 0;
            padding: 12px 18px;
            border-radius: 18px;
            font-size: 0.95em;
            color: #333;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            cursor: pointer;
            line-height: 1.4;
            border: 1px solid #ddd;
        }
        
        .trigger-node:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
        }
        
        /* 触发器颜色 - 柔和的颜色方案，避免与监控项目颜色冲突 */
        .trigger-color-0 { background: #e8f4f8; border-color: #3498db; }
        .trigger-color-1 { background: #f0f3ff; border-color: #6c5ce7; }
        .trigger-color-2 { background: #e8f5e8; border-color: #27ae60; }
        .trigger-color-3 { background: #fff3e0; border-color: #f39c12; }
        .trigger-color-4 { background: #f8e8ff; border-color: #9b59b6; }
        .trigger-color-5 { background: #e0f7fa; border-color: #1abc9c; }
        .trigger-color-6 { background: #fce4ec; border-color: #e91e63; }
        .trigger-color-7 { background: #f5f5f5; border-color: #95a5a6; }
        .trigger-color-8 { background: #fff8e1; border-color: #f1c40f; }
        .trigger-color-9 { background: #e3f2fd; border-color: #2196f3; }
        .trigger-color-10 { background: #f3e5f5; border-color: #8e24aa; }
        
        .expand-indicator {
            float: right;
            font-size: 1em;
            transition: transform 0.3s ease;
            margin-left: 10px;
        }
        
        .risk-item.expanded .expand-indicator {
            transform: rotate(180deg);
        }
        
        .search-controls {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 35px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        
        .search-box {
            width: 100%;
            max-width: 400px;
            padding: 12px 20px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1em;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .search-box:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .filter-buttons {
            margin-top: 15px;
        }
        
        .filter-btn {
            background: #e9ecef;
            border: none;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .filter-btn.active {
            background: #3498db;
            color: white;
        }
        
        .control-buttons {
            margin: 20px 0;
            text-align: left;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
        }
        
        .control-btn::before {
            content: "📂";
            font-size: 1.1em;
        }
        
        .control-btn.expanded::before {
            content: "📁";
        }
        
        .stats-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        
        .stat-item {
            flex: 1;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .priority-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .priority-indicator.high { background: #FF0000; }
        .priority-indicator.medium { background: #FFFF00; }
        .priority-indicator.low { background: #008000; }
        
        @media (max-width: 768px) {
            .usage-content {
                grid-template-columns: 1fr;
            }
            
            .branches-container {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .mindmap-content {
                padding: 20px;
            }
            
            .category-branch {
                max-width: 100%;
            }
            
            .stats-info {
                flex-direction: column;
                gap: 15px;
            }
            
            .risk-items-container {
                margin-left: 15px;
                padding-left: 15px;
            }
            
            .triggers-container {
                margin-left: 15px;
                padding-left: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="mindmap-container">
        <div class="header">
            <h1>🧠 异常交易行为监控规则 - 思维导图</h1>
            <p>智能化风险监控规则可视化展示</p>
        </div>
        
        <div class="mindmap-content">
            <div class="usage-guide">
                <h3>使用方法说明</h3>
                <div class="usage-content">
                    <div class="usage-section">
                        <h4>🎨 颜色标注说明</h4>
                        <div class="color-legend">
                            <div class="color-item">
                                <div class="color-dot" style="background: #FF0000;"></div>
                                <span>重点结果类</span>
                            </div>
                            <div class="color-item">
                                <div class="color-dot" style="background: #FFFF00;"></div>
                                <span>重点行为类</span>
                            </div>
                            <div class="color-item">
                                <div class="color-dot" style="background: #008000;"></div>
                                <span>一般行为类</span>
                            </div>
                        </div>
                        <p style="margin-top: 10px; font-size: 0.9em; color: #666;">
                            触发条件采用柔和色彩，根据逻辑表达式分组显示
                        </p>
                    </div>
                    <div class="usage-section">
                        <h4>🖱️ 操作指南</h4>
                        <ul class="usage-list">
                            <li>点击风险类别可展开/收起该类别下的所有项目</li>
                            <li>点击监控项目可查看详细的触发条件</li>
                            <li>使用搜索框快速定位特定风险项目</li>
                            <li>使用过滤按钮按优先级筛选显示内容</li>
                            <li>使用"展开/收起所有"按钮批量操作</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="search-controls">
                <input type="text" class="search-box" placeholder="🔍 搜索风险项目或触发条件..." id="searchInput">
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">全部</button>
                    <button class="filter-btn" data-filter="high">重点结果类</button>
                    <button class="filter-btn" data-filter="medium">重点行为类</button>
                    <button class="filter-btn" data-filter="low">一般行为类</button>
                </div>
            </div>
            
            <div class="control-buttons">
                <button class="control-btn" id="expandAllBtn" onclick="toggleAllNodes()">展开所有节点</button>
            </div>
            
            <div class="stats-info">
                <div class="stat-item">
                    <div class="stat-number" id="total-categories">0</div>
                    <div class="stat-label">风险类别</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="total-items">0</div>
                    <div class="stat-label">监控项目</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="total-triggers">0</div>
                    <div class="stat-label">触发条件</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="high-priority">0</div>
                    <div class="stat-label">重点结果类</div>
                </div>
            </div>
            
            <div class="branches-container" id="mindmap-branches">
                <!-- 动态生成思维导图分支 -->
            </div>
        </div>
    </div>
    
    <script>
        // 数据
        const data = {
    "严重异常波动证券": [
        {
            "name": "严重异常波动股票申报速率超限",
            "originalName": "【实时】严重异常波动股票申报速率超限",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计委托金额",
                    "weight": 1.0,
                    "threshold": "10000000"
                }
            ]
        },
        {
            "name": "严重异常波动股票申报速率超限-北交所",
            "originalName": "【实时】严重异常波动股票申报速率超限-北交所",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计委托金额",
                    "weight": 1.0,
                    "threshold": "8000000"
                }
            ]
        },
        {
            "name": "主板严重异常波动可转债申报速率超限",
            "originalName": "【实时】主板严重异常波动可转债申报速率超限",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计委托金额",
                    "weight": 1.0,
                    "threshold": "5000000"
                }
            ]
        }
    ],
    "买入风险警示股": [
        {
            "name": "超限买入风险警示股",
            "originalName": "【实时】超限买入风险警示股",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计买入数量",
                    "weight": 1.0,
                    "threshold": "500000"
                }
            ]
        }
    ],
    "偏离度": [
        {
            "name": "拉抬打压ETF基金价格偏离IOPV值",
            "originalName": "【实时】拉抬打压ETF基金价格偏离IOPV值",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1&&2&&3",
            "triggers": [
                {
                    "id": 1,
                    "name": "自营账户交易ETF时的偏离度",
                    "weight": 0.3,
                    "threshold": "0.05"
                },
                {
                    "id": 2,
                    "name": "累计成交数量占比",
                    "weight": 0.4,
                    "threshold": "0.4"
                },
                {
                    "id": 3,
                    "name": "拉抬打压幅度",
                    "weight": 0.3,
                    "threshold": "0.04"
                }
            ]
        },
        {
            "name": "拉抬打压债券价格偏离中证估值",
            "originalName": "【实时】拉抬打压债券价格偏离中证估值",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1&&2&&3",
            "triggers": [
                {
                    "id": 1,
                    "name": "委托价偏离度",
                    "weight": 0.3,
                    "threshold": "0.06"
                },
                {
                    "id": 2,
                    "name": "期间成交数量占比",
                    "weight": 0.4,
                    "threshold": "0.1"
                },
                {
                    "id": 3,
                    "name": "成交拉抬打压幅度",
                    "weight": 0.3,
                    "threshold": "0.04"
                }
            ]
        },
        {
            "name": "拉抬打压基金价格偏离上日净值",
            "originalName": "【实时】拉抬打压基金价格偏离上日净值",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1&&3&&4",
            "triggers": [
                {
                    "id": 1,
                    "name": "偏离度",
                    "weight": 0.3,
                    "threshold": "0.06"
                },
                {
                    "id": 3,
                    "name": "累计成交数量占比",
                    "weight": 0.4,
                    "threshold": "0.1"
                },
                {
                    "id": 4,
                    "name": "拉抬打压幅度",
                    "weight": 0.3,
                    "threshold": "0.04"
                }
            ]
        },
        {
            "name": "股票报价偏离最新价",
            "originalName": "【实时】股票报价偏离最新价",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "最小股票报价偏离度",
                    "weight": 0.5,
                    "threshold": "0.06"
                },
                {
                    "id": 2,
                    "name": "累计成交数量",
                    "weight": 0.5,
                    "threshold": "2000000"
                }
            ]
        }
    ],
    "可转债监控": [
        {
            "name": "大额买入重点监控可转债",
            "originalName": "【实时】大额买入重点监控可转债",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1||2",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计成交金额",
                    "weight": 1.0,
                    "threshold": "500000"
                },
                {
                    "id": 2,
                    "name": "累计成交数量",
                    "weight": 1.0,
                    "threshold": "5000"
                }
            ]
        },
        {
            "name": "拉抬重点监控可转债",
            "originalName": "【实时】拉抬重点监控可转债",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "2&&(1||3)",
            "triggers": [
                {
                    "id": 1,
                    "name": "3分钟累计成交金额",
                    "weight": 0.5,
                    "threshold": "600000"
                },
                {
                    "id": 2,
                    "name": "拉抬幅度",
                    "weight": 0.5,
                    "threshold": "0.01"
                },
                {
                    "id": 3,
                    "name": "10分钟累计成交金额",
                    "weight": 0.5,
                    "threshold": "1000000"
                }
            ]
        }
    ],
    "基础交易监控": [
        {
            "name": "大额买入重点公募REITs基金监控",
            "originalName": "【实时】大额买入重点公募REITs基金监控",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1||2",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计成交金额",
                    "weight": 1.0,
                    "threshold": "2100000"
                },
                {
                    "id": 2,
                    "name": "累计成交数量",
                    "weight": 1.0,
                    "threshold": "1050000"
                }
            ]
        },
        {
            "name": "大额频繁申报-北交所",
            "originalName": "【实时】大额频繁申报-北交所",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计委托金额",
                    "weight": 0.5,
                    "threshold": "5000000"
                },
                {
                    "id": 2,
                    "name": "累计委托数量占比",
                    "weight": 0.5,
                    "threshold": "0.006"
                }
            ]
        },
        {
            "name": "证券停牌前大量成交",
            "originalName": "【查询】【日终（T-1）】证券停牌前大量成交",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "单方向累计数量占流盘比例",
                    "weight": 1.0,
                    "threshold": "0.0015"
                }
            ]
        }
    ],
    "大宗交易": [
        {
            "name": "大宗交易与竞价交易日内反向交易",
            "originalName": "【日终（T-1）】大宗交易与竞价交易日内反向交易",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "3||4",
            "triggers": [
                {
                    "id": 3,
                    "name": "反向交易金额",
                    "weight": 1.0,
                    "threshold": "12000000"
                },
                {
                    "id": 4,
                    "name": "反向交易数量",
                    "weight": 1.0,
                    "threshold": "1200000"
                }
            ]
        },
        {
            "name": "大宗交易与竞价交易日内高买低卖",
            "originalName": "【日终（T-1）】大宗交易与竞价交易日内高买低卖",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1&&(4||6)",
            "triggers": [
                {
                    "id": 1,
                    "name": "买入、卖出均价差额占卖出均价比例",
                    "weight": 0.5,
                    "threshold": "0.1"
                },
                {
                    "id": 4,
                    "name": "反向交易金额",
                    "weight": 0.5,
                    "threshold": "2000000"
                },
                {
                    "id": 6,
                    "name": "亏损金额",
                    "weight": 0.5,
                    "threshold": "1000000"
                }
            ]
        },
        {
            "name": "大宗交易高买低卖",
            "originalName": "【日终（T-1）】大宗交易高买低卖",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1&&(4||5)",
            "triggers": [
                {
                    "id": 1,
                    "name": "买入、卖出均价差额占卖出均价比例",
                    "weight": 0.5,
                    "threshold": "0.1"
                },
                {
                    "id": 4,
                    "name": "反向交易金额",
                    "weight": 0.5,
                    "threshold": "2000000"
                },
                {
                    "id": 5,
                    "name": "亏损金额",
                    "weight": 0.5,
                    "threshold": "1000000"
                }
            ]
        },
        {
            "name": "大宗交易与竞价交易隔日反向交易",
            "originalName": "【日终（T）】大宗交易与竞价交易隔日反向交易",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "3||4",
            "triggers": [
                {
                    "id": 3,
                    "name": "反向交易金额",
                    "weight": 1.0,
                    "threshold": "12000000"
                },
                {
                    "id": 4,
                    "name": "反向交易数量",
                    "weight": 1.0,
                    "threshold": "1200000"
                }
            ]
        },
        {
            "name": "大宗交易与竞价交易隔日高买低卖",
            "originalName": "【日终（T）】大宗交易与竞价交易隔日高买低卖",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1&&(4||5)",
            "triggers": [
                {
                    "id": 1,
                    "name": "买卖均价差占卖出均价比例",
                    "weight": 0.5,
                    "threshold": "0.1"
                },
                {
                    "id": 4,
                    "name": "反向交易金额",
                    "weight": 0.5,
                    "threshold": "2000000"
                },
                {
                    "id": 5,
                    "name": "亏损金额",
                    "weight": 0.5,
                    "threshold": "1000000"
                }
            ]
        },
        {
            "name": "大宗交易隔日高买低卖",
            "originalName": "【日终（T）】大宗交易隔日高买低卖",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1&&(4||6)",
            "triggers": [
                {
                    "id": 1,
                    "name": "买卖均价差占卖出均价比例",
                    "weight": 0.5,
                    "threshold": "0.1"
                },
                {
                    "id": 4,
                    "name": "反向交易金额",
                    "weight": 0.5,
                    "threshold": "2000000"
                },
                {
                    "id": 6,
                    "name": "亏损金额",
                    "weight": 0.5,
                    "threshold": "1000000"
                }
            ]
        }
    ],
    "拉抬打压": [
        {
            "name": "打压开盘价并反向买入",
            "originalName": "【实时】打压开盘价并反向买入",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "(1||2)&&3&&4&&(5||6)",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计成交数量",
                    "weight": 0.1,
                    "threshold": "300000"
                },
                {
                    "id": 2,
                    "name": "累计成交金额",
                    "weight": 0.1,
                    "threshold": "3000000"
                },
                {
                    "id": 3,
                    "name": "占期间市场成交总量比",
                    "weight": 0.3,
                    "threshold": "0.3"
                },
                {
                    "id": 4,
                    "name": "开盘价跌幅",
                    "weight": 0.3,
                    "threshold": "0.02"
                },
                {
                    "id": 5,
                    "name": "反向交易成交数量",
                    "weight": 0.3,
                    "threshold": "100000"
                },
                {
                    "id": 6,
                    "name": "反向交易成交金额",
                    "weight": 0.3,
                    "threshold": "1000000"
                }
            ]
        },
        {
            "name": "打压收盘价并反向买入",
            "originalName": "【实时】打压收盘价并反向买入",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "(1||2)&&3&&4&&(5||6)",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计成交数量",
                    "weight": 0.1,
                    "threshold": "300000"
                },
                {
                    "id": 2,
                    "name": "累计成交金额",
                    "weight": 0.1,
                    "threshold": "3000000"
                },
                {
                    "id": 3,
                    "name": "占期间市场成交总量比",
                    "weight": 0.3,
                    "threshold": "0.3"
                },
                {
                    "id": 4,
                    "name": "收盘集合竞价阶段股票跌幅",
                    "weight": 0.3,
                    "threshold": "0.02"
                },
                {
                    "id": 5,
                    "name": "反向交易成交数量",
                    "weight": 0.3,
                    "threshold": "100000"
                },
                {
                    "id": 6,
                    "name": "反向交易成交金额",
                    "weight": 0.3,
                    "threshold": "1000000"
                }
            ]
        },
        {
            "name": "拉抬开盘价并反向卖出",
            "originalName": "【实时】拉抬开盘价并反向卖出",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "(1||2)&&3&&4&&(5||6)",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计成交数量",
                    "weight": 0.1,
                    "threshold": "300000"
                },
                {
                    "id": 2,
                    "name": "累计成交金额",
                    "weight": 0.1,
                    "threshold": "3000000"
                },
                {
                    "id": 3,
                    "name": "占期间市场成交总量比",
                    "weight": 0.3,
                    "threshold": "0.3"
                },
                {
                    "id": 4,
                    "name": "开盘价涨幅",
                    "weight": 0.3,
                    "threshold": "0.02"
                },
                {
                    "id": 5,
                    "name": "反向交易成交数量",
                    "weight": 0.3,
                    "threshold": "100000"
                },
                {
                    "id": 6,
                    "name": "反向交易成交金额",
                    "weight": 0.3,
                    "threshold": "1000000"
                }
            ]
        },
        {
            "name": "拉抬收盘价并反向卖出",
            "originalName": "【实时】拉抬收盘价并反向卖出",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "(1||2)&&3&&4&&(5||6)",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计成交数量",
                    "weight": 0.1,
                    "threshold": "300000"
                },
                {
                    "id": 2,
                    "name": "累计成交金额",
                    "weight": 0.1,
                    "threshold": "3000000"
                },
                {
                    "id": 3,
                    "name": "占期间市场成交总量比",
                    "weight": 0.3,
                    "threshold": "0.3"
                },
                {
                    "id": 4,
                    "name": "收盘集合竞价阶段股票涨幅",
                    "weight": 0.3,
                    "threshold": "0.02"
                },
                {
                    "id": 5,
                    "name": "反向交易成交数量",
                    "weight": 0.3,
                    "threshold": "100000"
                },
                {
                    "id": 6,
                    "name": "反向交易成交金额",
                    "weight": 0.3,
                    "threshold": "1000000"
                }
            ]
        },
        {
            "name": "盘中打压股价",
            "originalName": "【实时】盘中打压股价",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "1&&(2||3)&&5",
            "triggers": [
                {
                    "id": 1,
                    "name": "成交量占期间市场成交量比例",
                    "weight": 0.4,
                    "threshold": "0.3"
                },
                {
                    "id": 2,
                    "name": "累计成交金额",
                    "weight": 0.2,
                    "threshold": "3000000"
                },
                {
                    "id": 3,
                    "name": "累计成交数量",
                    "weight": 0.2,
                    "threshold": "300000"
                },
                {
                    "id": 5,
                    "name": "打压幅度",
                    "weight": 0.4,
                    "threshold": "0.04"
                }
            ]
        },
        {
            "name": "盘中打压股价-北交所",
            "originalName": "【实时】盘中打压股价-北交所",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "1&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "打压幅度",
                    "weight": 0.5,
                    "threshold": "0.05"
                },
                {
                    "id": 2,
                    "name": "成交量占期间市场成交量比例",
                    "weight": 0.5,
                    "threshold": "0.5"
                }
            ]
        },
        {
            "name": "盘中打压股价并反向买入",
            "originalName": "【实时】盘中打压股价并反向买入",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "(1||2)&&3&&4&&(5||6)",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计成交数量",
                    "weight": 0.1,
                    "threshold": "300000"
                },
                {
                    "id": 2,
                    "name": "累计成交金额",
                    "weight": 0.1,
                    "threshold": "3000000"
                },
                {
                    "id": 3,
                    "name": "占期间市场成交总量比",
                    "weight": 0.3,
                    "threshold": "0.3"
                },
                {
                    "id": 4,
                    "name": "打压幅度",
                    "weight": 0.3,
                    "threshold": "0.02"
                },
                {
                    "id": 5,
                    "name": "反向交易成交数量",
                    "weight": 0.3,
                    "threshold": "100000"
                },
                {
                    "id": 6,
                    "name": "反向交易成交金额",
                    "weight": 0.3,
                    "threshold": "1000000"
                }
            ]
        },
        {
            "name": "盘中拉抬股价",
            "originalName": "【实时】盘中拉抬股价",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "1&&(2||3)&&6",
            "triggers": [
                {
                    "id": 1,
                    "name": "成交量占期间市场成交量比例",
                    "weight": 0.4,
                    "threshold": "0.3"
                },
                {
                    "id": 2,
                    "name": "累计成交金额",
                    "weight": 0.2,
                    "threshold": "3000000"
                },
                {
                    "id": 3,
                    "name": "累计成交数量",
                    "weight": 0.2,
                    "threshold": "300000"
                },
                {
                    "id": 6,
                    "name": "拉抬幅度",
                    "weight": 0.4,
                    "threshold": "0.04"
                }
            ]
        },
        {
            "name": "盘中拉抬股价-北交所",
            "originalName": "【实时】盘中拉抬股价-北交所",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "1&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "拉抬幅度",
                    "weight": 0.5,
                    "threshold": "0.05"
                },
                {
                    "id": 2,
                    "name": "成交量占期间市场成交量比例",
                    "weight": 0.5,
                    "threshold": "0.5"
                }
            ]
        },
        {
            "name": "盘中拉抬股价并反向卖出",
            "originalName": "【实时】盘中拉抬股价并反向卖出",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "(1||2)&&3&&4&&(5||6)",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计成交数量",
                    "weight": 0.1,
                    "threshold": "300000"
                },
                {
                    "id": 2,
                    "name": "累计成交金额",
                    "weight": 0.1,
                    "threshold": "3000000"
                },
                {
                    "id": 3,
                    "name": "占期间市场成交总量比",
                    "weight": 0.3,
                    "threshold": "0.3"
                },
                {
                    "id": 4,
                    "name": "拉抬幅度",
                    "weight": 0.3,
                    "threshold": "0.02"
                },
                {
                    "id": 5,
                    "name": "反向交易成交数量",
                    "weight": 0.3,
                    "threshold": "100000"
                },
                {
                    "id": 6,
                    "name": "反向交易成交金额",
                    "weight": 0.3,
                    "threshold": "1000000"
                }
            ]
        },
        {
            "name": "开盘集合竞价打压股价",
            "originalName": "【日终（T）】开盘集合竞价打压股价",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "(2||4)&&3&&1&&5",
            "triggers": [
                {
                    "id": 1,
                    "name": "开盘价相比昨日收盘价跌幅",
                    "weight": 0.3,
                    "threshold": "0.05"
                },
                {
                    "id": 2,
                    "name": "累计成交金额",
                    "weight": 0.3,
                    "threshold": "3000000"
                },
                {
                    "id": 3,
                    "name": "占期间市场成交总量比",
                    "weight": 0.3,
                    "threshold": "0.3"
                },
                {
                    "id": 4,
                    "name": "累计成交数量",
                    "weight": 0.3,
                    "threshold": "300000"
                },
                {
                    "id": 5,
                    "name": "股票跌停时有效申报量占比(非跌停默认取1级阈值)",
                    "weight": 0.1,
                    "threshold": "0.1"
                }
            ]
        },
        {
            "name": "开盘集合竞价打压股价-北交所",
            "originalName": "【日终（T）】开盘集合竞价打压股价-北交所",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "1&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "开盘价相比昨日收盘价跌幅",
                    "weight": 0.5,
                    "threshold": "0.05"
                },
                {
                    "id": 2,
                    "name": "占期间市场成交总量比",
                    "weight": 0.5,
                    "threshold": "0.5"
                }
            ]
        },
        {
            "name": "开盘集合竞价拉抬股价",
            "originalName": "【日终（T）】开盘集合竞价拉抬股价",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "(2||4)&&3&&1&&6",
            "triggers": [
                {
                    "id": 1,
                    "name": "开盘价相比昨日收盘价涨幅",
                    "weight": 0.3,
                    "threshold": "0.05"
                },
                {
                    "id": 2,
                    "name": "累计成交金额",
                    "weight": 0.3,
                    "threshold": "3000000"
                },
                {
                    "id": 3,
                    "name": "占期间市场成交总量比",
                    "weight": 0.3,
                    "threshold": "0.3"
                },
                {
                    "id": 4,
                    "name": "累计成交数量",
                    "weight": 0.3,
                    "threshold": "300000"
                },
                {
                    "id": 6,
                    "name": "股票涨停时有效申报量占比(非涨停默认取1级阈值)",
                    "weight": 0.1,
                    "threshold": "0.1"
                }
            ]
        },
        {
            "name": "开盘集合竞价拉抬股价-北交所",
            "originalName": "【日终（T）】开盘集合竞价拉抬股价-北交所",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "1&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "开盘价相比昨日收盘价涨幅",
                    "weight": 0.5,
                    "threshold": "0.05"
                },
                {
                    "id": 2,
                    "name": "占期间市场成交总量比",
                    "weight": 0.5,
                    "threshold": "0.5"
                }
            ]
        },
        {
            "name": "收盘集合竞价打压股价",
            "originalName": "【日终（T）】收盘集合竞价打压股价",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "(1||4)&&3&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计成交金额",
                    "weight": 0.2,
                    "threshold": "3000000"
                },
                {
                    "id": 2,
                    "name": "收盘集合竞价期间市场成交量比例",
                    "weight": 0.4,
                    "threshold": "0.3"
                },
                {
                    "id": 3,
                    "name": "收盘集合竞价期间价格跌幅",
                    "weight": 0.4,
                    "threshold": "0.03"
                },
                {
                    "id": 4,
                    "name": "累计成交数量",
                    "weight": 0.2,
                    "threshold": "300000"
                }
            ]
        },
        {
            "name": "收盘集合竞价打压股价-北交所",
            "originalName": "【日终（T）】收盘集合竞价打压股价-北交所",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "1&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "收盘集合竞价期间价格跌幅",
                    "weight": 0.5,
                    "threshold": "0.05"
                },
                {
                    "id": 2,
                    "name": "收盘集合竞价期间市场成交量比例",
                    "weight": 0.5,
                    "threshold": "0.5"
                }
            ]
        },
        {
            "name": "收盘集合竞价拉抬股价",
            "originalName": "【日终（T）】收盘集合竞价拉抬股价",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "(1||4)&&3&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计成交金额",
                    "weight": 0.2,
                    "threshold": "3000000"
                },
                {
                    "id": 2,
                    "name": "收盘集合竞价期间市场成交量比例",
                    "weight": 0.4,
                    "threshold": "0.3"
                },
                {
                    "id": 3,
                    "name": "收盘集合竞价期间价格涨幅",
                    "weight": 0.4,
                    "threshold": "0.03"
                },
                {
                    "id": 4,
                    "name": "累计成交数量",
                    "weight": 0.2,
                    "threshold": "300000"
                }
            ]
        },
        {
            "name": "收盘集合竞价拉抬股价-北交所",
            "originalName": "【日终（T）】收盘集合竞价拉抬股价-北交所",
            "priority": "high",
            "priorityText": "重点结果类",
            "expression": "1&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "收盘集合竞价期间价格涨幅",
                    "weight": 0.5,
                    "threshold": "0.05"
                },
                {
                    "id": 2,
                    "name": "收盘集合竞价期间市场成交量比例",
                    "weight": 0.5,
                    "threshold": "0.5"
                }
            ]
        }
    ],
    "涨跌幅限制申报": [
        {
            "name": "涨停价大额申报",
            "originalName": "【实时】涨停价大额申报",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1||2",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计委托金额",
                    "weight": 1.0,
                    "threshold": "2000000"
                },
                {
                    "id": 2,
                    "name": "累计委托数量",
                    "weight": 1.0,
                    "threshold": "200000"
                }
            ]
        },
        {
            "name": "盘中维持涨(跌)幅限制价格",
            "originalName": "【实时】盘中维持涨(跌)幅限制价格",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "(1||3)&&2&&4",
            "triggers": [
                {
                    "id": 1,
                    "name": "该价格剩余有效申报金额",
                    "weight": 0.2,
                    "threshold": "10000000"
                },
                {
                    "id": 2,
                    "name": "占当时该价格市场总申报数量比例",
                    "weight": 0.4,
                    "threshold": "0.3"
                },
                {
                    "id": 3,
                    "name": "该价格剩余有效申报数量",
                    "weight": 0.2,
                    "threshold": "1000000"
                },
                {
                    "id": 4,
                    "name": "成交占剩余申报数量比例（注册制新规生效后创业板默认触发）",
                    "weight": 0.4,
                    "threshold": "0.7"
                }
            ]
        },
        {
            "name": "盘中维持涨(跌)幅限制价格-北交所",
            "originalName": "【实时】盘中维持涨(跌)幅限制价格-北交所",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "(1||3)&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "该价格剩余有效申报金额",
                    "weight": 0.5,
                    "threshold": "1000000"
                },
                {
                    "id": 2,
                    "name": "占当时该价格市场总申报数量比例",
                    "weight": 0.5,
                    "threshold": "0.5"
                },
                {
                    "id": 3,
                    "name": "该价格剩余有效申报数量",
                    "weight": 0.5,
                    "threshold": "100000"
                }
            ]
        },
        {
            "name": "跌停价大额申报",
            "originalName": "【实时】跌停价大额申报",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1||2",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计委托金额",
                    "weight": 1.0,
                    "threshold": "2000000"
                },
                {
                    "id": 2,
                    "name": "累计委托数量",
                    "weight": 1.0,
                    "threshold": "200000"
                }
            ]
        },
        {
            "name": "收盘维持涨幅限制价格",
            "originalName": "【日终（T）】收盘维持涨幅限制价格",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "(3||4)&&5&&(6||7)",
            "triggers": [
                {
                    "id": 3,
                    "name": "新增申报未成交数量",
                    "weight": 0.3,
                    "threshold": "300000"
                },
                {
                    "id": 4,
                    "name": "新增申报未成交金额",
                    "weight": 0.3,
                    "threshold": "3000000"
                },
                {
                    "id": 5,
                    "name": "收盘时剩余有效申报数量占比",
                    "weight": 0.4,
                    "threshold": "0.3"
                },
                {
                    "id": 6,
                    "name": "市场剩余有效申报数量",
                    "weight": 0.3,
                    "threshold": "1000000"
                },
                {
                    "id": 7,
                    "name": "市场剩余有效申报金额",
                    "weight": 0.3,
                    "threshold": "10000000"
                }
            ]
        },
        {
            "name": "收盘维持涨幅限制价格-北交所",
            "originalName": "【日终（T）】收盘维持涨幅限制价格-北交所",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "(1||2)&&3",
            "triggers": [
                {
                    "id": 1,
                    "name": "新增申报未成交数量",
                    "weight": 0.5,
                    "threshold": "100000"
                },
                {
                    "id": 2,
                    "name": "新增申报未成交金额",
                    "weight": 0.5,
                    "threshold": "1000000"
                },
                {
                    "id": 3,
                    "name": "收盘时剩余有效申报数量占比",
                    "weight": 0.5,
                    "threshold": "0.5"
                }
            ]
        },
        {
            "name": "收盘维持跌幅限制价格",
            "originalName": "【日终（T）】收盘维持跌幅限制价格",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "(3||4)&&5&&(6||7)",
            "triggers": [
                {
                    "id": 3,
                    "name": "新增申报未成交数量",
                    "weight": 0.3,
                    "threshold": "300000"
                },
                {
                    "id": 4,
                    "name": "新增申报未成交金额",
                    "weight": 0.3,
                    "threshold": "3000000"
                },
                {
                    "id": 5,
                    "name": "收盘时剩余有效申报数量占比",
                    "weight": 0.4,
                    "threshold": "0.3"
                },
                {
                    "id": 6,
                    "name": "市场剩余有效申报数量",
                    "weight": 0.3,
                    "threshold": "1000000"
                },
                {
                    "id": 7,
                    "name": "市场剩余有效申报金额",
                    "weight": 0.3,
                    "threshold": "10000000"
                }
            ]
        },
        {
            "name": "收盘维持跌幅限制价格-北交所",
            "originalName": "【日终（T）】收盘维持跌幅限制价格-北交所",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "(1||2)&&3",
            "triggers": [
                {
                    "id": 1,
                    "name": "新增申报未成交数量",
                    "weight": 0.5,
                    "threshold": "100000"
                },
                {
                    "id": 2,
                    "name": "新增申报未成交金额",
                    "weight": 0.5,
                    "threshold": "1000000"
                },
                {
                    "id": 3,
                    "name": "收盘时剩余有效申报数量占比",
                    "weight": 0.5,
                    "threshold": "0.5"
                }
            ]
        }
    ],
    "特殊时期监控": [
        {
            "name": "维稳期间大额委托",
            "originalName": "【实时】维稳期间大额委托",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计委托金额",
                    "weight": 1.0,
                    "threshold": "5000000"
                }
            ]
        },
        {
            "name": "维稳期间大额成交",
            "originalName": "【实时】维稳期间大额成交",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计成交金额",
                    "weight": 1.0,
                    "threshold": "5000000"
                }
            ]
        }
    ],
    "程序化交易": [
        {
            "name": "瞬时申报速率异常",
            "originalName": "【实时】瞬时申报速率异常",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "申报笔数",
                    "weight": 1.0,
                    "threshold": "600"
                }
            ]
        },
        {
            "name": "短时间内大额成交",
            "originalName": "【实时】短时间内大额成交",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1&&2&&3",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计成交金额",
                    "weight": 0.3,
                    "threshold": "30000000"
                },
                {
                    "id": 2,
                    "name": "指数涨（跌）幅",
                    "weight": 0.4,
                    "threshold": "0.002"
                },
                {
                    "id": 3,
                    "name": "累计成交金额市场占比",
                    "weight": 0.3,
                    "threshold": "0.03"
                }
            ]
        },
        {
            "name": "频繁报撤单",
            "originalName": "【实时】频繁报撤单",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "委托撤单笔数比",
                    "weight": 1.0,
                    "threshold": "0.8"
                }
            ]
        },
        {
            "name": "频繁拉抬打压",
            "originalName": "【实时】频繁拉抬打压",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "发生次数",
                    "weight": 1.0,
                    "threshold": "15"
                }
            ]
        },
        {
            "name": "频繁瞬时撤单",
            "originalName": "【实时】频繁瞬时撤单",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "瞬时撤单次数",
                    "weight": 0.5,
                    "threshold": "500"
                },
                {
                    "id": 2,
                    "name": "全日撤单比例",
                    "weight": 0.5,
                    "threshold": "0.5"
                }
            ]
        }
    ],
    "自买自卖": [
        {
            "name": "自买自卖",
            "originalName": "【日终（T）】自买自卖",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1||5",
            "triggers": [
                {
                    "id": 1,
                    "name": "成交数量占全天市场总成交量比",
                    "weight": 1.0,
                    "threshold": "0.1"
                },
                {
                    "id": 5,
                    "name": "收盘集合竞价成交数量占期间市场成交量比",
                    "weight": 1.0,
                    "threshold": "0.3"
                }
            ]
        },
        {
            "name": "自买自卖-北交所",
            "originalName": "【日终（T）】自买自卖-北交所",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1||2",
            "triggers": [
                {
                    "id": 1,
                    "name": "成交数量占全天市场总成交量比",
                    "weight": 1.0,
                    "threshold": "0.1"
                },
                {
                    "id": 2,
                    "name": "收盘集合竞价成交数量占期间市场成交量比",
                    "weight": 1.0,
                    "threshold": "0.3"
                }
            ]
        }
    ],
    "虚假申报": [
        {
            "name": "盘中最优五档单向虚假申报",
            "originalName": "【实时】盘中最优五档单向虚假申报",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1&&2&&3&&(5||6)&&7",
            "triggers": [
                {
                    "id": 1,
                    "name": "发生次数",
                    "weight": 0.2,
                    "threshold": "3"
                },
                {
                    "id": 2,
                    "name": "撤单率",
                    "weight": 0.2,
                    "threshold": "0.5"
                },
                {
                    "id": 3,
                    "name": "撤单次数（注册制新规后非创业板默认触发）",
                    "weight": 0.1,
                    "threshold": "3"
                },
                {
                    "id": 5,
                    "name": "剩余有效申报数量",
                    "weight": 0.2,
                    "threshold": "1000000"
                },
                {
                    "id": 6,
                    "name": "剩余有效申报金额",
                    "weight": 0.2,
                    "threshold": "10000000"
                },
                {
                    "id": 7,
                    "name": "占同向有效申报占比",
                    "weight": 0.3,
                    "threshold": "0.3"
                }
            ]
        },
        {
            "name": "盘中最优五档单向虚假申报-北交所",
            "originalName": "【实时】盘中最优五档单向虚假申报-北交所",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1&&2",
            "triggers": [
                {
                    "id": 1,
                    "name": "撤单率",
                    "weight": 0.5,
                    "threshold": "0.5"
                },
                {
                    "id": 2,
                    "name": "撤单次数",
                    "weight": 0.5,
                    "threshold": "2"
                }
            ]
        },
        {
            "name": "盘中最优价位频繁撤销申报",
            "originalName": "【实时】盘中最优价位频繁撤销申报",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "累计撤单次数",
                    "weight": 1.0,
                    "threshold": "12"
                }
            ]
        },
        {
            "name": "盘中涨停板反复虚假申报",
            "originalName": "【实时】盘中涨停板反复虚假申报",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1&&4&&(5||6)&&7",
            "triggers": [
                {
                    "id": 1,
                    "name": "发生次数",
                    "weight": 0.2,
                    "threshold": "2"
                },
                {
                    "id": 4,
                    "name": "有效申报数量占比",
                    "weight": 0.3,
                    "threshold": "0.3"
                },
                {
                    "id": 5,
                    "name": "剩余有效申报数量",
                    "weight": 0.3,
                    "threshold": "1000000"
                },
                {
                    "id": 6,
                    "name": "剩余有效申报金额",
                    "weight": 0.3,
                    "threshold": "10000000"
                },
                {
                    "id": 7,
                    "name": "撤单率",
                    "weight": 0.2,
                    "threshold": "0.5"
                }
            ]
        },
        {
            "name": "盘中涨停板反复虚假申报-北交所",
            "originalName": "【实时】盘中涨停板反复虚假申报-北交所",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "发生次数",
                    "weight": 1.0,
                    "threshold": "2"
                }
            ]
        },
        {
            "name": "盘中跌停板反复虚假申报",
            "originalName": "【实时】盘中跌停板反复虚假申报",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1&&4&&(5||6)&&7",
            "triggers": [
                {
                    "id": 1,
                    "name": "发生次数",
                    "weight": 0.2,
                    "threshold": "2"
                },
                {
                    "id": 4,
                    "name": "有效申报数量占比",
                    "weight": 0.3,
                    "threshold": "0.3"
                },
                {
                    "id": 5,
                    "name": "剩余有效申报数量",
                    "weight": 0.3,
                    "threshold": "1000000"
                },
                {
                    "id": 6,
                    "name": "剩余有效申报金额",
                    "weight": 0.3,
                    "threshold": "10000000"
                },
                {
                    "id": 7,
                    "name": "撤单率",
                    "weight": 0.2,
                    "threshold": "0.5"
                }
            ]
        },
        {
            "name": "盘中跌停板反复虚假申报-北交所",
            "originalName": "【实时】盘中跌停板反复虚假申报-北交所",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "发生次数",
                    "weight": 1.0,
                    "threshold": "2"
                }
            ]
        },
        {
            "name": "开盘集合竞价虚假申报",
            "originalName": "【日终（T）】开盘集合竞价虚假申报",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1&&2&&(3||4)",
            "triggers": [
                {
                    "id": 1,
                    "name": "占期间市场申报总量比",
                    "weight": 0.3,
                    "threshold": "0.3"
                },
                {
                    "id": 2,
                    "name": "撤单率",
                    "weight": 0.4,
                    "threshold": "0.5"
                },
                {
                    "id": 3,
                    "name": "累计申报金额",
                    "weight": 0.3,
                    "threshold": "3000000"
                },
                {
                    "id": 4,
                    "name": "累计申报数量",
                    "weight": 0.3,
                    "threshold": "300000"
                }
            ]
        },
        {
            "name": "开盘集合竞价虚假申报-北交所",
            "originalName": "【日终（T）】开盘集合竞价虚假申报-北交所",
            "priority": "medium",
            "priorityText": "重点行为类",
            "expression": "1",
            "triggers": [
                {
                    "id": 1,
                    "name": "撤单率",
                    "weight": 1.0,
                    "threshold": "0.5"
                }
            ]
        }
    ],
    "高买低卖": [
        {
            "name": "日内高买低卖",
            "originalName": "【日终（T）】日内高买低卖",
            "priority": "low",
            "priorityText": "一般行为类",
            "expression": "1&&(2||3)",
            "triggers": [
                {
                    "id": 1,
                    "name": "买入、卖出均价差额占卖出均价比例",
                    "weight": 0.5,
                    "threshold": "0.1"
                },
                {
                    "id": 2,
                    "name": "亏损金额",
                    "weight": 0.5,
                    "threshold": "1000000"
                },
                {
                    "id": 3,
                    "name": "反向交易金额",
                    "weight": 0.5,
                    "threshold": "1000000"
                }
            ]
        }
    ]
}
        
        // 更新统计数据
        document.getElementById('total-categories').textContent = 13;
        document.getElementById('total-items').textContent = 64;
        document.getElementById('total-triggers').textContent = 185;
        document.getElementById('high-priority').textContent = 18;
        
        // 触发器颜色映射 - 柔和颜色方案
        const triggerColorMap = {
            '#FF0000': 0, '#0000FF': 1, '#00FF00': 2, '#FFA500': 3, '#800080': 4,
            '#00FFFF': 5, '#FF00FF': 6, '#808080': 7, '#A52A2A': 8, '#4682B4': 9, '#FFC0CB': 10
        };
        
        // 全局展开状态
        let allExpanded = false;
        
        // 解析表达式并分配颜色 - 与Python函数一致
        function parseExpressionAndAssignColors(expression) {
            const triggerColors = {};
            let colorIndex = 0;
            const colors = ['#FF0000', '#0000FF', '#00FF00', '#FFA500', '#800080', 
                          '#00FFFF', '#FF00FF', '#808080', '#A52A2A', '#4682B4', '#FFC0CB'];
            
            // 简化的表达式解析 - 按&&分割组件
            const andComponents = expression.split('&&').map(comp => comp.trim());
            
            andComponents.forEach(component => {
                const currentColor = colors[colorIndex % colors.length];
                colorIndex++;
                
                // 移除外层括号
                let cleaned = component.replace(/^\(|\)$/g, '');
                
                // 处理OR组件
                if (cleaned.includes('||')) {
                    const orParts = cleaned.split('||');
                    orParts.forEach(part => {
                        const triggerNum = parseInt(part.trim());
                        if (!isNaN(triggerNum)) {
                            triggerColors[triggerNum] = currentColor;
                        }
                    });
                } else {
                    const triggerNum = parseInt(cleaned);
                    if (!isNaN(triggerNum)) {
                        triggerColors[triggerNum] = currentColor;
                    }
                }
            });
            
            return triggerColors;
        }
        
        // 按重点指标数量排序分类
        function sortCategoriesByPriority(data) {
            const categoryPriorities = {};
            
            // 计算每个分类的重点指标数量
            Object.keys(data).forEach(categoryName => {
                const items = data[categoryName];
                const highPriorityCount = items.filter(item => item.priority === 'high').length;
                const mediumPriorityCount = items.filter(item => item.priority === 'medium').length;
                categoryPriorities[categoryName] = highPriorityCount * 2 + mediumPriorityCount; // 高优先级权重更高
            });
            
            // 按优先级分数排序
            return Object.keys(data).sort((a, b) => categoryPriorities[b] - categoryPriorities[a]);
        }
        
        // 渲染思维导图
        function renderMindmap(filteredData = data) {
            const container = document.getElementById('mindmap-branches');
            container.innerHTML = '';
            
            // 按重点指标数量排序
            const sortedCategories = sortCategoriesByPriority(filteredData);
            
            sortedCategories.forEach(categoryName => {
                const categoryItems = filteredData[categoryName];
                if (!categoryItems || categoryItems.length === 0) return;
                
                const categoryBranch = document.createElement('div');
                categoryBranch.className = 'category-branch';
                
                // 计算分类的重点指标统计
                const highCount = categoryItems.filter(item => item.priority === 'high').length;
                const mediumCount = categoryItems.filter(item => item.priority === 'medium').length;
                const lowCount = categoryItems.filter(item => item.priority === 'low').length;
                
                let priorityInfo = '';
                if (highCount > 0) priorityInfo += `🔴结果类${highCount} `;
                if (mediumCount > 0) priorityInfo += `🟡行为类${mediumCount} `;
                if (lowCount > 0) priorityInfo += `🟢一般类${lowCount}`;
                
                categoryBranch.innerHTML = `
                    <div class="category-node" onclick="toggleCategory(this)">
                        ${categoryName} <br>
                        <small style="font-size: 0.8em; opacity: 0.9;">${priorityInfo}</small>
                    </div>
                    <div class="risk-items-container">
                        ${categoryItems.map(item => {
                            const triggerColors = parseExpressionAndAssignColors(item.expression);
                            return `
                                <div class="risk-item" data-priority="${item.priority}">
                                    <div class="risk-node priority-${item.priority}" onclick="toggleRiskItem(this)">
                                        <span class="priority-indicator ${item.priority}"></span>
                                        ${item.name}
                                        <span class="expand-indicator">▼</span>
                                    </div>
                                    <div class="triggers-container">
                                        ${item.triggers.map(trigger => {
                                            const triggerColor = triggerColors[trigger.id] || '#808080';
                                            const colorClass = triggerColorMap[triggerColor] !== undefined ? 
                                                `trigger-color-${triggerColorMap[triggerColor]}` : 'trigger-color-7';
                                            return `
                                                <div class="trigger-node ${colorClass}" title="触发器ID: ${trigger.id}">
                                                    <strong>[${trigger.id}]</strong> ${trigger.name}<br>
                                                    <small>权重: ${trigger.weight} | 阈值: ${trigger.threshold}</small>
                                                </div>
                                            `;
                                        }).join('')}
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                `;
                
                container.appendChild(categoryBranch);
            });
        }
        
        // 切换分类展开/收起
        function toggleCategory(categoryNode) {
            const container = categoryNode.nextElementSibling;
            const isVisible = container.style.display !== 'none';
            container.style.display = isVisible ? 'none' : 'block';
        }
        
        // 切换风险项展开/收起
        function toggleRiskItem(riskNode) {
            const riskItem = riskNode.parentElement;
            riskItem.classList.toggle('expanded');
        }
        
        // 展开/收起所有节点
        function toggleAllNodes() {
            const allRiskItems = document.querySelectorAll('.risk-item');
            const allCategoryContainers = document.querySelectorAll('.risk-items-container');
            const btn = document.getElementById('expandAllBtn');
            
            if (allExpanded) {
                // 收起所有监控项目的触发条件
                allRiskItems.forEach(item => {
                    item.classList.remove('expanded');
                });
                // 收起所有风险类别
                allCategoryContainers.forEach(container => {
                    container.style.display = 'none';
                });
                btn.textContent = '展开所有节点';
                btn.classList.remove('expanded');
                allExpanded = false;
            } else {
                // 展开所有监控项目的触发条件
                allRiskItems.forEach(item => {
                    item.classList.add('expanded');
                });
                // 展开所有风险类别
                allCategoryContainers.forEach(container => {
                    container.style.display = 'block';
                });
                btn.textContent = '收起所有节点';
                btn.classList.add('expanded');
                allExpanded = true;
            }
        }
        
        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterData(searchTerm, getCurrentFilter());
        });
        
        // 优先级过滤
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.dataset.filter;
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                filterData(searchTerm, filter);
            });
        });
        
        function getCurrentFilter() {
            return document.querySelector('.filter-btn.active').dataset.filter;
        }
        
        function filterData(searchTerm, priorityFilter) {
            const filteredData = {};
            
            Object.keys(data).forEach(categoryName => {
                const filteredItems = data[categoryName].filter(item => {
                    const matchesSearch = !searchTerm || 
                        item.name.toLowerCase().includes(searchTerm) ||
                        item.originalName.toLowerCase().includes(searchTerm) ||
                        item.triggers.some(trigger => trigger.name.toLowerCase().includes(searchTerm));
                    
                    const matchesPriority = priorityFilter === 'all' || item.priority === priorityFilter;
                    
                    return matchesSearch && matchesPriority;
                });
                
                if (filteredItems.length > 0) {
                    filteredData[categoryName] = filteredItems;
                }
            });
            
            renderMindmap(filteredData);
        }
        
        // 初始化页面
        renderMindmap();
        
        // 添加统计数字动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = parseInt(stat.textContent);
                let currentValue = 0;
                const increment = Math.ceil(finalValue / 30);
                
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    stat.textContent = currentValue;
                }, 50);
            });
        });
    </script>
</body>
</html>