import pandas as pd
from io import StringIO
import xmind
from xmind.core.topic import TopicElement
from xmind.core.workbook import WorkbookElement
# from xmind.utils import XMindMarkDownParser # 此脚本中不直接用于markdown解析

# 提供的CSV内容
csv_content = """风险项编号,风险项名称,风险类别,表达式,触发原因编号,触发原因,权重,阈值
101101012504,【实时】维稳期间大额成交,特殊时期监控,1,1,累计成交金额,1.0,"5,000,000.00"
101101012704,【实时】维稳期间大额委托,特殊时期监控,1,1,累计委托金额,1.0,"5,000,000.00"
101101013904,【实时】严重异常波动股票申报速率超限,严重异常波动证券,1,1,累计委托金额,1.0,"10,000,000.00"
101101014204,【查询】【日终（T-1）】证券停牌前大量成交,基础交易监控,1,1,单方向累计数量占流盘比例,1.0,0.0015
101101014604,【实时】大额频繁申报-北交所,基础交易监控,1&&2,1,累计委托金额,0.5,"5,000,000.00"
101101014604,【实时】大额频繁申报-北交所,基础交易监控,1&&2,2,累计委托数量占比,0.5,0.006
101101014704,【实时】大额买入重点公募REITs基金监控,基础交易监控,1||2,1,累计成交金额,1.0,"2,100,000.00"
101101014704,【实时】大额买入重点公募REITs基金监控,基础交易监控,1||2,2,累计成交数量,1.0,"1,050,000.00"
101101015004,【实时】严重异常波动股票申报速率超限-北交所,严重异常波动证券,1,1,累计委托金额,1.0,"8,000,000.00"
101101020104,【日终（T）】开盘集合竞价拉抬股价,拉抬打压,(2||4)&&3&&1&&6,1,开盘价相比昨日收盘价涨幅,0.3,0.05
101101020104,【日终（T）】开盘集合竞价拉抬股价,拉抬打压,(2||4)&&3&&1&&6,2,累计成交金额,0.3,"3,000,000.00"
101101020104,【日终（T）】开盘集合竞价拉抬股价,拉抬打压,(2||4)&&3&&1&&6,3,占期间市场成交总量比,0.3,0.3
101101020104,【日终（T）】开盘集合竞价拉抬股价,拉抬打压,(2||4)&&3&&1&&6,4,累计成交数量,0.3,"300,000.00"
101101020104,【日终（T）】开盘集合竞价拉抬股价,拉抬打压,(2||4)&&3&&1&&6,6,股票涨停时有效申报量占比(非涨停默认取1级阈值),0.1,0.1
101101020204,【日终（T）】开盘集合竞价打压股价,拉抬打压,(2||4)&&3&&1&&5,1,开盘价相比昨日收盘价跌幅,0.3,0.05
101101020204,【日终（T）】开盘集合竞价打压股价,拉抬打压,(2||4)&&3&&1&&5,2,累计成交金额,0.3,"3,000,000.00"
101101020204,【日终（T）】开盘集合竞价打压股价,拉抬打压,(2||4)&&3&&1&&5,3,占期间市场成交总量比,0.3,0.3
101101020204,【日终（T）】开盘集合竞价打压股价,拉抬打压,(2||4)&&3&&1&&5,4,累计成交数量,0.3,"300,000.00"
101101020204,【日终（T）】开盘集合竞价打压股价,拉抬打压,(2||4)&&3&&1&&5,5,股票跌停时有效申报量占比(非跌停默认取1级阈值),0.1,0.1
101101020304,【实时】盘中拉抬股价,拉抬打压,1&&(2||3)&&6,1,成交量占期间市场成交量比例,0.4,0.3
101101020304,【实时】盘中拉抬股价,拉抬打压,1&&(2||3)&&6,2,累计成交金额,0.2,"3,000,000.00"
101101020304,【实时】盘中拉抬股价,拉抬打压,1&&(2||3)&&6,3,累计成交数量,0.2,"300,000.00"
101101020304,【实时】盘中拉抬股价,拉抬打压,1&&(2||3)&&6,6,拉抬幅度,0.4,0.04
101101020404,【实时】盘中打压股价,拉抬打压,1&&(2||3)&&5,1,成交量占期间市场成交量比例,0.4,0.3
101101020404,【实时】盘中打压股价,拉抬打压,1&&(2||3)&&5,2,累计成交金额,0.2,"3,000,000.00"
101101020404,【实时】盘中打压股价,拉抬打压,1&&(2||3)&&5,3,累计成交数量,0.2,"300,000.00"
101101020404,【实时】盘中打压股价,拉抬打压,1&&(2||3)&&5,5,打压幅度,0.4,0.04
101101020704,【日终（T）】收盘集合竞价拉抬股价,拉抬打压,(1||4)&&3&&2,1,累计成交金额,0.2,"3,000,000.00"
101101020704,【日终（T）】收盘集合竞价拉抬股价,拉抬打压,(1||4)&&3&&2,2,收盘集合竞价期间市场成交量比例,0.4,0.3
101101020704,【日终（T）】收盘集合竞价拉抬股价,拉抬打压,(1||4)&&3&&2,3,收盘集合竞价期间价格涨幅,0.4,0.03
101101020704,【日终（T）】收盘集合竞价拉抬股价,拉抬打压,(1||4)&&3&&2,4,累计成交数量,0.2,"300,000.00"
101101020804,【日终（T）】收盘集合竞价打压股价,拉抬打压,(1||4)&&3&&2,1,累计成交金额,0.2,"3,000,000.00"
101101020804,【日终（T）】收盘集合竞价打压股价,拉抬打压,(1||4)&&3&&2,2,收盘集合竞价期间市场成交量比例,0.4,0.3
101101020804,【日终（T）】收盘集合竞价打压股价,拉抬打压,(1||4)&&3&&2,3,收盘集合竞价期间价格跌幅,0.4,0.03
101101020804,【日终（T）】收盘集合竞价打压股价,拉抬打压,(1||4)&&3&&2,4,累计成交数量,0.2,"300,000.00"
101101021004,【日终（T）】开盘集合竞价拉抬股价-北交所,拉抬打压,1&&2,1,开盘价相比昨日收盘价涨幅,0.5,0.05
101101021004,【日终（T）】开盘集合竞价拉抬股价-北交所,拉抬打压,1&&2,2,占期间市场成交总量比,0.5,0.5
101101021104,【日终（T）】开盘集合竞价打压股价-北交所,拉抬打压,1&&2,1,开盘价相比昨日收盘价跌幅,0.5,0.05
101101021104,【日终（T）】开盘集合竞价打压股价-北交所,拉抬打压,1&&2,2,占期间市场成交总量比,0.5,0.5
101101021204,【实时】盘中拉抬股价-北交所,拉抬打压,1&&2,1,拉抬幅度,0.5,0.05
101101021204,【实时】盘中拉抬股价-北交所,拉抬打压,1&&2,2,成交量占期间市场成交量比例,0.5,0.5
101101021304,【实时】盘中打压股价-北交所,拉抬打压,1&&2,1,打压幅度,0.5,0.05
101101021304,【实时】盘中打压股价-北交所,拉抬打压,1&&2,2,成交量占期间市场成交量比例,0.5,0.5
101101021404,【日终（T）】收盘集合竞价拉抬股价-北交所,拉抬打压,1&&2,1,收盘集合竞价期间价格涨幅,0.5,0.05
101101021404,【日终（T）】收盘集合竞价拉抬股价-北交所,拉抬打压,1&&2,2,收盘集合竞价期间市场成交量比例,0.5,0.5
101101021504,【日终（T）】收盘集合竞价打压股价-北交所,拉抬打压,1&&2,1,收盘集合竞价期间价格跌幅,0.5,0.05
101101021504,【日终（T）】收盘集合竞价打压股价-北交所,拉抬打压,1&&2,2,收盘集合竞价期间市场成交量比例,0.5,0.5
101101021604,【实时】拉抬开盘价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),1,累计成交数量,0.1,"300,000.00"
101101021604,【实时】拉抬开盘价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),2,累计成交金额,0.1,"3,000,000.00"
101101021604,【实时】拉抬开盘价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),3,占期间市场成交总量比,0.3,0.3
101101021604,【实时】拉抬开盘价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),4,开盘价涨幅,0.3,0.02
101101021604,【实时】拉抬开盘价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),5,反向交易成交数量,0.3,"100,000.00"
101101021604,【实时】拉抬开盘价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),6,反向交易成交金额,0.3,"1,000,000.00"
101101021704,【实时】打压开盘价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),1,累计成交数量,0.1,"300,000.00"
101101021704,【实时】打压开盘价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),2,累计成交金额,0.1,"3,000,000.00"
101101021704,【实时】打压开盘价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),3,占期间市场成交总量比,0.3,0.3
101101021704,【实时】打压开盘价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),4,开盘价跌幅,0.3,0.02
101101021704,【实时】打压开盘价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),5,反向交易成交数量,0.3,"100,000.00"
101101021704,【实时】打压开盘价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),6,反向交易成交金额,0.3,"1,000,000.00"
101101021804,【实时】盘中拉抬股价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),1,累计成交数量,0.1,"300,000.00"
101101021804,【实时】盘中拉抬股价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),2,累计成交金额,0.1,"3,000,000.00"
101101021804,【实时】盘中拉抬股价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),3,占期间市场成交总量比,0.3,0.3
101101021804,【实时】盘中拉抬股价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),4,拉抬幅度,0.3,0.02
101101021804,【实时】盘中拉抬股价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),5,反向交易成交数量,0.3,"100,000.00"
101101021804,【实时】盘中拉抬股价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),6,反向交易成交金额,0.3,"1,000,000.00"
101101021904,【实时】盘中打压股价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),1,累计成交数量,0.1,"300,000.00"
101101021904,【实时】盘中打压股价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),2,累计成交金额,0.1,"3,000,000.00"
101101021904,【实时】盘中打压股价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),3,占期间市场成交总量比,0.3,0.3
101101021904,【实时】盘中打压股价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),4,打压幅度,0.3,0.02
101101021904,【实时】盘中打压股价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),5,反向交易成交数量,0.3,"100,000.00"
101101021904,【实时】盘中打压股价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),6,反向交易成交金额,0.3,"1,000,000.00"
101101022004,【实时】拉抬收盘价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),1,累计成交数量,0.1,"300,000.00"
101101022004,【实时】拉抬收盘价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),2,累计成交金额,0.1,"3,000,000.00"
101101022004,【实时】拉抬收盘价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),3,占期间市场成交总量比,0.3,0.3
101101022004,【实时】拉抬收盘价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),4,收盘集合竞价阶段股票涨幅,0.3,0.02
101101022004,【实时】拉抬收盘价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),5,反向交易成交数量,0.3,"100,000.00"
101101022004,【实时】拉抬收盘价并反向卖出,拉抬打压,(1||2)&&3&&4&&(5||6),6,反向交易成交金额,0.3,"1,000,000.00"
101101022104,【实时】打压收盘价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),1,累计成交数量,0.1,"300,000.00"
101101022104,【实时】打压收盘价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),2,累计成交金额,0.1,"3,000,000.00"
101101022104,【实时】打压收盘价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),3,占期间市场成交总量比,0.3,0.3
101101022104,【实时】打压收盘价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),4,收盘集合竞价阶段股票跌幅,0.3,0.02
101101022104,【实时】打压收盘价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),5,反向交易成交数量,0.3,"100,000.00"
101101022104,【实时】打压收盘价并反向买入,拉抬打压,(1||2)&&3&&4&&(5||6),6,反向交易成交金额,0.3,"1,000,000.00"
101101030104,【日终（T）】开盘集合竞价虚假申报,虚假申报,1&&2&&(3||4),1,占期间市场申报总量比,0.3,0.3
101101030104,【日终（T）】开盘集合竞价虚假申报,虚假申报,1&&2&&(3||4),2,撤单率,0.4,0.5
101101030104,【日终（T）】开盘集合竞价虚假申报,虚假申报,1&&2&&(3||4),3,累计申报金额,0.3,"3,000,000.00"
101101030104,【日终（T）】开盘集合竞价虚假申报,虚假申报,1&&2&&(3||4),4,累计申报数量,0.3,"300,000.00"
101101030204,【实时】盘中最优五档单向虚假申报,虚假申报,1&&2&&3&&(5||6)&&7,1,发生次数,0.2,3.00
101101030204,【实时】盘中最优五档单向虚假申报,虚假申报,1&&2&&3&&(5||6)&&7,2,撤单率,0.2,0.5
101101030204,【实时】盘中最优五档单向虚假申报,虚假申报,1&&2&&3&&(5||6)&&7,3,撤单次数（注册制新规后非创业板默认触发）,0.1,3.00
101101030204,【实时】盘中最优五档单向虚假申报,虚假申报,1&&2&&3&&(5||6)&&7,5,剩余有效申报数量,0.2,"1,000,000.00"
101101030204,【实时】盘中最优五档单向虚假申报,虚假申报,1&&2&&3&&(5||6)&&7,6,剩余有效申报金额,0.2,"10,000,000.00"
101101030204,【实时】盘中最优五档单向虚假申报,虚假申报,1&&2&&3&&(5||6)&&7,7,占同向有效申报占比,0.3,0.3
101101030704,【实时】盘中涨停板反复虚假申报,虚假申报,1&&4&&(5||6)&&7,1,发生次数,0.2,2.00
101101030704,【实时】盘中涨停板反复虚假申报,虚假申报,1&&4&&(5||6)&&7,4,有效申报数量占比,0.3,0.3
101101030704,【实时】盘中涨停板反复虚假申报,虚假申报,1&&4&&(5||6)&&7,5,剩余有效申报数量,0.3,"1,000,000.00"
101101030704,【实时】盘中涨停板反复虚假申报,虚假申报,1&&4&&(5||6)&&7,6,剩余有效申报金额,0.3,"10,000,000.00"
101101030704,【实时】盘中涨停板反复虚假申报,虚假申报,1&&4&&(5||6)&&7,7,撤单率,0.2,0.5
101101030804,【实时】盘中跌停板反复虚假申报,虚假申报,1&&4&&(5||6)&&7,1,发生次数,0.2,2.00
101101030804,【实时】盘中跌停板反复虚假申报,虚假申报,1&&4&&(5||6)&&7,4,有效申报数量占比,0.3,0.3
101101030804,【实时】盘中跌停板反复虚假申报,虚假申报,1&&4&&(5||6)&&7,5,剩余有效申报数量,0.3,"1,000,000.00"
101101030804,【实时】盘中跌停板反复虚假申报,虚假申报,1&&4&&(5||6)&&7,6,剩余有效申报金额,0.3,"10,000,000.00"
101101030804,【实时】盘中跌停板反复虚假申报,虚假申报,1&&4&&(5||6)&&7,7,撤单率,0.2,0.5
101101031104,【日终（T）】收盘维持涨幅限制价格,涨跌幅限制申报,(3||4)&&5&&(6||7),3,新增申报未成交数量,0.3,"300,000.00"
101101031104,【日终（T）】收盘维持涨幅限制价格,涨跌幅限制申报,(3||4)&&5&&(6||7),4,新增申报未成交金额,0.3,"3,000,000.00"
101101031104,【日终（T）】收盘维持涨幅限制价格,涨跌幅限制申报,(3||4)&&5&&(6||7),5,收盘时剩余有效申报数量占比,0.4,0.3
101101031104,【日终（T）】收盘维持涨幅限制价格,涨跌幅限制申报,(3||4)&&5&&(6||7),6,市场剩余有效申报数量,0.3,"1,000,000.00"
101101031104,【日终（T）】收盘维持涨幅限制价格,涨跌幅限制申报,(3||4)&&5&&(6||7),7,市场剩余有效申报金额,0.3,"10,000,000.00"
101101031204,【日终（T）】收盘维持跌幅限制价格,涨跌幅限制申报,(3||4)&&5&&(6||7),3,新增申报未成交数量,0.3,"300,000.00"
101101031204,【日终（T）】收盘维持跌幅限制价格,涨跌幅限制申报,(3||4)&&5&&(6||7),4,新增申报未成交金额,0.3,"3,000,000.00"
101101031204,【日终（T）】收盘维持跌幅限制价格,涨跌幅限制申报,(3||4)&&5&&(6||7),5,收盘时剩余有效申报数量占比,0.4,0.3
101101031204,【日终（T）】收盘维持跌幅限制价格,涨跌幅限制申报,(3||4)&&5&&(6||7),6,市场剩余有效申报数量,0.3,"1,000,000.00"
101101031204,【日终（T）】收盘维持跌幅限制价格,涨跌幅限制申报,(3||4)&&5&&(6||7),7,市场剩余有效申报金额,0.3,"10,000,000.00"
101101031904,【实时】盘中最优价位频繁撤销申报,虚假申报,1,1,累计撤单次数,1.0,12.00
101101032104,【日终（T）】开盘集合竞价虚假申报-北交所,虚假申报,1,1,撤单率,1.0,0.5
101101032204,【实时】盘中最优五档单向虚假申报-北交所,虚假申报,1&&2,1,撤单率,0.5,0.5
101101032204,【实时】盘中最优五档单向虚假申报-北交所,虚假申报,1&&2,2,撤单次数,0.5,2.00
101101032304,【实时】盘中涨停板反复虚假申报-北交所,虚假申报,1,1,发生次数,1.0,2.00
101101032404,【实时】盘中跌停板反复虚假申报-北交所,虚假申报,1,1,发生次数,1.0,2.00
101101040104,【实时】涨停价大额申报,涨跌幅限制申报,1||2,1,累计委托金额,1.0,"2,000,000.00"
101101040104,【实时】涨停价大额申报,涨跌幅限制申报,1||2,2,累计委托数量,1.0,"200,000.00"
101101040204,【实时】跌停价大额申报,涨跌幅限制申报,1||2,1,累计委托金额,1.0,"2,000,000.00"
101101040204,【实时】跌停价大额申报,涨跌幅限制申报,1||2,2,累计委托数量,1.0,"200,000.00"
101101041001,【实时】盘中维持涨(跌)幅限制价格,涨跌幅限制申报,(1||3)&&2&&4,1,该价格剩余有效申报金额,0.2,"10,000,000.00"
101101041001,【实时】盘中维持涨(跌)幅限制价格,涨跌幅限制申报,(1||3)&&2&&4,2,占当时该价格市场总申报数量比例,0.4,0.3
101101041001,【实时】盘中维持涨(跌)幅限制价格,涨跌幅限制申报,(1||3)&&2&&4,3,该价格剩余有效申报数量,0.2,"1,000,000.00"
101101041001,【实时】盘中维持涨(跌)幅限制价格,涨跌幅限制申报,(1||3)&&2&&4,4,成交占剩余申报数量比例（注册制新规生效后创业板默认触发）,0.4,0.7
101101041204,【实时】盘中维持涨(跌)幅限制价格-北交所,涨跌幅限制申报,(1||3)&&2,1,该价格剩余有效申报金额,0.5,"1,000,000.00"
101101041204,【实时】盘中维持涨(跌)幅限制价格-北交所,涨跌幅限制申报,(1||3)&&2,2,占当时该价格市场总申报数量比例,0.5,0.5
101101041204,【实时】盘中维持涨(跌)幅限制价格-北交所,涨跌幅限制申报,(1||3)&&2,3,该价格剩余有效申报数量,0.5,"100,000.00"
101101041304,【日终（T）】收盘维持涨幅限制价格-北交所,涨跌幅限制申报,(1||2)&&3,1,新增申报未成交数量,0.5,"100,000.00"
101101041304,【日终（T）】收盘维持涨幅限制价格-北交所,涨跌幅限制申报,(1||2)&&3,2,新增申报未成交金额,0.5,"1,000,000.00"
101101041304,【日终（T）】收盘维持涨幅限制价格-北交所,涨跌幅限制申报,(1||2)&&3,3,收盘时剩余有效申报数量占比,0.5,0.5
101101041404,【日终（T）】收盘维持跌幅限制价格-北交所,涨跌幅限制申报,(1||2)&&3,1,新增申报未成交数量,0.5,"100,000.00"
101101041404,【日终（T）】收盘维持跌幅限制价格-北交所,涨跌幅限制申报,(1||2)&&3,2,新增申报未成交金额,0.5,"1,000,000.00"
101101041404,【日终（T）】收盘维持跌幅限制价格-北交所,涨跌幅限制申报,(1||2)&&3,3,收盘时剩余有效申报数量占比,0.5,0.5
101101050104,【日终（T）】自买自卖,自买自卖,1||5,1,成交数量占全天市场总成交量比,1.0,0.1
101101050104,【日终（T）】自买自卖,自买自卖,1||5,5,收盘集合竞价成交数量占期间市场成交量比,1.0,0.3
101101050404,【日终（T）】自买自卖-北交所,自买自卖,1||2,1,成交数量占全天市场总成交量比,1.0,0.1
101101050404,【日终（T）】自买自卖-北交所,自买自卖,1||2,2,收盘集合竞价成交数量占期间市场成交量比,1.0,0.3
101101070104,【日终（T）】日内高买低卖,高买低卖,1&&(2||3),1,买入、卖出均价差额占卖出均价比例,0.5,0.1
101101070104,【日终（T）】日内高买低卖,高买低卖,1&&(2||3),2,亏损金额,0.5,"1,000,000.00"
101101070104,【日终（T）】日内高买低卖,高买低卖,1&&(2||3),3,反向交易金额,0.5,"1,000,000.00"
101101080104,【日终（T-1）】大宗交易与竞价交易日内反向交易,大宗交易,3||4,3,反向交易金额,1.0,"12,000,000.00"
101101080104,【日终（T-1）】大宗交易与竞价交易日内反向交易,大宗交易,3||4,4,反向交易数量,1.0,"1,200,000.00"
101101080204,【日终（T）】大宗交易与竞价交易隔日反向交易,大宗交易,3||4,3,反向交易金额,1.0,"12,000,000.00"
101101080204,【日终（T）】大宗交易与竞价交易隔日反向交易,大宗交易,3||4,4,反向交易数量,1.0,"1,200,000.00"
101101080304,【日终（T-1）】大宗交易与竞价交易日内高买低卖,大宗交易,1&&(4||6),1,买入、卖出均价差额占卖出均价比例,0.5,0.1
101101080304,【日终（T-1）】大宗交易与竞价交易日内高买低卖,大宗交易,1&&(4||6),4,反向交易金额,0.5,"2,000,000.00"
101101080304,【日终（T-1）】大宗交易与竞价交易日内高买低卖,大宗交易,1&&(4||6),6,亏损金额,0.5,"1,000,000.00"
101101080404,【日终（T）】大宗交易与竞价交易隔日高买低卖,大宗交易,1&&(4||5),1,买卖均价差占卖出均价比例,0.5,0.1
101101080404,【日终（T）】大宗交易与竞价交易隔日高买低卖,大宗交易,1&&(4||5),4,反向交易金额,0.5,"2,000,000.00"
101101080404,【日终（T）】大宗交易与竞价交易隔日高买低卖,大宗交易,1&&(4||5),5,亏损金额,0.5,"1,000,000.00"
101101080504,【日终（T-1）】大宗交易高买低卖,大宗交易,1&&(4||5),1,买入、卖出均价差额占卖出均价比例,0.5,0.1
101101080504,【日终（T-1）】大宗交易高买低卖,大宗交易,1&&(4||5),4,反向交易金额,0.5,"2,000,000.00"
101101080504,【日终（T-1）】大宗交易高买低卖,大宗交易,1&&(4||5),5,亏损金额,0.5,"1,000,000.00"
101101080704,【日终（T）】大宗交易隔日高买低卖,大宗交易,1&&(4||6),1,买卖均价差占卖出均价比例,0.5,0.1
101101080704,【日终（T）】大宗交易隔日高买低卖,大宗交易,1&&(4||6),4,反向交易金额,0.5,"2,000,000.00"
101101080704,【日终（T）】大宗交易隔日高买低卖,大宗交易,1&&(4||6),6,亏损金额,0.5,"1,000,000.00"
101101090104,【实时】超限买入风险警示股,买入风险警示股,1,1,累计买入数量,1.0,"500,000.00"
101101100104,【实时】频繁报撤单,程序化交易,1,1,委托撤单笔数比,1.0,0.8
101101100804,【实时】瞬时申报速率异常,程序化交易,1,1,申报笔数,1.0,600.00
101101100904,【实时】频繁瞬时撤单,程序化交易,1&&2,1,瞬时撤单次数,0.5,500.00
101101100904,【实时】频繁瞬时撤单,程序化交易,1&&2,2,全日撤单比例,0.5,0.5
101101101004,【实时】短时间内大额成交,程序化交易,1&&2&&3,1,累计成交金额,0.3,"30,000,000.00"
101101101004,【实时】短时间内大额成交,程序化交易,1&&2&&3,2,指数涨（跌）幅,0.4,0.002
101101101004,【实时】短时间内大额成交,程序化交易,1&&2&&3,3,累计成交金额市场占比,0.3,0.03
101101101104,【实时】频繁拉抬打压,程序化交易,1,1,发生次数,1.0,15.00
101101110104,【实时】拉抬打压ETF基金价格偏离IOPV值,偏离度,1&&2&&3,1,自营账户交易ETF时的偏离度,0.3,0.05
101101110104,【实时】拉抬打压ETF基金价格偏离IOPV值,偏离度,1&&2&&3,2,累计成交数量占比,0.4,0.4
101101110104,【实时】拉抬打压ETF基金价格偏离IOPV值,偏离度,1&&2&&3,3,拉抬打压幅度,0.3,0.04
101101110204,【实时】拉抬打压债券价格偏离中证估值,偏离度,1&&2&&3,1,委托价偏离度,0.3,0.06
101101110204,【实时】拉抬打压债券价格偏离中证估值,偏离度,1&&2&&3,2,期间成交数量占比,0.4,0.1
101101110204,【实时】拉抬打压债券价格偏离中证估值,偏离度,1&&2&&3,3,成交拉抬打压幅度,0.3,0.04
101101110304,【实时】股票报价偏离最新价,偏离度,1&&2,1,最小股票报价偏离度,0.5,0.06
101101110304,【实时】股票报价偏离最新价,偏离度,1&&2,2,累计成交数量,0.5,"2,000,000.00"
101101110404,【实时】拉抬打压基金价格偏离上日净值,偏离度,1&&3&&4,1,偏离度,0.3,0.06
101101110404,【实时】拉抬打压基金价格偏离上日净值,偏离度,1&&3&&4,3,累计成交数量占比,0.4,0.1
101101110404,【实时】拉抬打压基金价格偏离上日净值,偏离度,1&&3&&4,4,拉抬打压幅度,0.3,0.04
101101150104,【实时】主板严重异常波动可转债申报速率超限,严重异常波动证券,1,1,累计委托金额,1.0,"5,000,000.00"
101101160104,【实时】大额买入重点监控可转债,可转债监控,1||2,1,累计成交金额,1.0,"500,000.00"
101101160104,【实时】大额买入重点监控可转债,可转债监控,1||2,2,累计成交数量,1.0,"5,000.00"
101101160204,【实时】拉抬重点监控可转债,可转债监控,2&&(1||3),1,3分钟累计成交金额,0.5,"600,000.00"
101101160204,【实时】拉抬重点监控可转债,可转债监控,2&&(1||3),2,拉抬幅度,0.5,0.01
101101160204,【实时】拉抬重点监控可转债,可转债监控,2&&(1||3),3,10分钟累计成交金额,0.5,"1,000,000.00"
"""

# 将CSV内容读取到pandas DataFrame中
df = pd.read_csv(StringIO(csv_content), sep=',', skipinitialspace=True, encoding='utf-8')

# 清理'阈值'列：移除逗号并转换为数值，然后格式化回字符串
df['阈值'] = df['阈值'].astype(str).str.replace(',', '', regex=False)
df['阈值'] = pd.to_numeric(df['阈值'], errors='coerce')
# 对于数值，如果是整数则格式化为移除尾随的.0，否则保留小数
df['阈值'] = df['阈值'].apply(lambda x: f"{int(x)}" if pd.notna(x) and x == int(x) else f"{x:g}" if pd.notna(x) else '')

# 定义次重点指标
secondary_important_indicators = [
    "【实时】主板严重异常波动可转债申报速率超限",
    "【实时】涨停价大额申报",
    "【实时】严重异常波动股票申报速率超限-北交所",
    "【实时】严重异常波动股票申报速率超限",
    "【实时】维稳期间大额委托",
    "【实时】维稳期间大额成交",
    "【实时】盘中最优五档单向虚假申报-北交所",
    "【实时】盘中最优五档单向虚假申报",
    "【实时】盘中最优价位频繁撤销申报",
    "【实时】盘中涨停板反复虚假申报-北交所",
    "【实时】盘中涨停板反复虚假申报",
    "【实时】盘中维持涨(跌)幅限制价格-北交所",
    "【实时】盘中维持涨(跌)幅限制价格",
    "【实时】盘中跌停板反复虚假申报-北交所",
    "【实时】盘中跌停板反复虚假申报",
    "【实时】拉抬打压债券价格偏离中证估值",
    "【实时】跌停价大额申报",
    "【实时】大额买入重点监控可转债",
    "【实时】大额买入重点公募REITs基金监控",
    "【日终（T）】自买自卖-北交所",
    "【日终（T）】自买自卖",
    "【日终（T）】收盘维持涨幅限制价格-北交所",
    "【日终（T）】收盘维持涨幅限制价格",
    "【日终（T）】收盘维持跌幅限制价格-北交所",
    "【日终（T）】收盘维持跌幅限制价格",
    "【日终（T）】开盘集合竞价虚假申报-北交所",
    "【日终（T）】开盘集合竞价虚假申报"
]

# 定义颜色常量（xmind-sdk的十六进制字符串）
COLOR_RED = "#FF0000"
COLOR_YELLOW = "#FFFF00"
COLOR_GREEN = "#008000" # 更深的绿色以获得更好的可见性

# 为触发原因定义不同颜色的调色板（十六进制字符串）
TRIGGER_COLORS = [
    "#FF0000", # 红色
    "#0000FF", # 蓝色
    "#00FF00", # 绿色
    "#FFA500", # 橙色
    "#800080", # 紫色
    "#00FFFF", # 青色
    "#FF00FF", # 洋红色
    "#808080", # 灰色
    "#A52A2A", # 棕色
    "#4682B4", # 钢蓝色
    "#FFC0CB"  # 粉色
]

def get_next_trigger_color_index(current_index):
    """循环使用TRIGGER_COLORS调色板"""
    return (current_index + 1) % len(TRIGGER_COLORS)

def parse_expression_and_assign_colors(expression_str, current_trigger_color_index):
    """
    解析布尔表达式以为触发编号分配颜色
    返回一个将trigger_number映射到color_hex_string的字典，以及更新的颜色索引
    """
    trigger_colors_map = {}
    current_color_idx = current_trigger_color_index

    # 辅助函数：按顶级&&分割，考虑括号
    def split_by_and(s):
        parts = []
        balance = 0
        current_part = []
        for char in s:
            if char == '&' and balance == 0:
                parts.append("".join(current_part).strip())
                current_part = []
            elif char == '(':
                balance += 1
                current_part.append(char)
            elif char == ')':
                balance -= 1
                current_part.append(char)
            else:
                current_part.append(char)
        parts.append("".join(current_part).strip())
        return [p for p in parts if p] # 过滤空字符串

    and_components = split_by_and(expression_str)

    for component in and_components:
        current_component_color_hex = TRIGGER_COLORS[current_color_idx]
        current_color_idx = get_next_trigger_color_index(current_color_idx)

        # 如果存在外层括号，则移除以进行进一步处理
        component_cleaned = component.strip()
        if component_cleaned.startswith('(') and component_cleaned.endswith(')'):
            temp_comp = component_cleaned[1:-1].strip()
            if '&&' not in temp_comp and '||' in temp_comp: # 确保不是嵌套的AND或已处理过的
                 component_cleaned = temp_comp


        # 检查组件内的OR
        if '||' in component_cleaned:
            or_parts = component_cleaned.split('||')
            for part in or_parts:
                part = part.strip()
                try:
                    trigger_num = int(part)
                    trigger_colors_map[trigger_num] = current_component_color_hex
                except ValueError:
                    pass
        else:
            # 单个触发编号（无OR）
            try:
                trigger_num = int(component_cleaned)
                trigger_colors_map[trigger_num] = current_component_color_hex
            except ValueError:
                pass

    return trigger_colors_map, current_color_idx

# 创建新的XMind工作簿
import os
import tempfile
import zipfile

# 创建一个标准的XMind模板文件
def create_xmind_template():
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.xmind', delete=False)
    temp_file.close()
    
    # 创建标准的XMind文件结构
    content_xml = '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xmap-content xmlns="urn:xmind:xmap:xmlns:content:2.0" xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xhtml="http://www.w3.org/1999/xhtml" xmlns:xlink="http://www.w3.org/1999/xlink" version="2.0">
  <sheet id="sheet1" theme="theme1">
    <topic id="root" structure-class="org.xmind.ui.logic.right">
      <title>Central Topic</title>
    </topic>
  </sheet>
</xmap-content>'''

    styles_xml = '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xmap-styles xmlns="urn:xmind:xmap:xmlns:style:2.0" xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:svg="http://www.w3.org/2000/svg" version="2.0">
  <styles>
    <style id="theme1" type="theme">
      <topic-properties>
        <topic-property id="central-topic">
          <border-line-color>#000000</border-line-color>
          <border-line-width>2pt</border-line-width>
          <line-color>#000000</line-color>
          <line-width>2pt</line-width>
          <shape-class>org.xmind.topicShape.roundedRect</shape-class>
          <text-color>#000000</text-color>
        </topic-property>
        <topic-property id="main-topic">
          <border-line-color>#000000</border-line-color>
          <border-line-width>1pt</border-line-width>
          <line-color>#000000</line-color>
          <line-width>2pt</line-width>
          <shape-class>org.xmind.topicShape.roundedRect</shape-class>
          <text-color>#000000</text-color>
        </topic-property>
        <topic-property id="sub-topic">
          <border-line-color>#000000</border-line-color>
          <border-line-width>1pt</border-line-width>
          <line-color>#000000</line-color>
          <line-width>1pt</line-width>
          <shape-class>org.xmind.topicShape.roundedRect</shape-class>
          <text-color>#000000</text-color>
        </topic-property>
      </topic-properties>
    </style>
  </styles>
</xmap-styles>'''

    # 添加meta.xml文件
    meta_xml = '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<meta xmlns="urn:xmind:xmap:xmlns:meta:2.0" version="2.0">
  <Author>
    <Name>Kiro AI Assistant</Name>
  </Author>
  <Create>
    <Time>2025-07-28T12:28:00.000Z</Time>
  </Create>
  <Creator>
    <Name>XMind</Name>
    <Version>2023</Version>
  </Creator>
</meta>'''

    manifest_xml = '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<manifest xmlns="urn:xmind:xmap:xmlns:manifest:1.0">
  <file-entry full-path="content.xml" media-type="text/xml"/>
  <file-entry full-path="styles.xml" media-type="text/xml"/>
  <file-entry full-path="meta.xml" media-type="text/xml"/>
  <file-entry full-path="META-INF/" media-type=""/>
  <file-entry full-path="META-INF/manifest.xml" media-type="text/xml"/>
</manifest>'''

    # 创建ZIP文件
    with zipfile.ZipFile(temp_file.name, 'w', zipfile.ZIP_DEFLATED) as zf:
        zf.writestr('content.xml', content_xml)
        zf.writestr('styles.xml', styles_xml)
        zf.writestr('meta.xml', meta_xml)
        zf.writestr('META-INF/manifest.xml', manifest_xml)
    
    return temp_file.name

# 创建模板并加载
template_path = create_xmind_template()
workbook = xmind.load(template_path)

# 清理临时文件
try:
    os.unlink(template_path)
except:
    pass

# 获取根主题
primary_sheet = workbook.getPrimarySheet()
root_topic = primary_sheet.getRootTopic()
root_topic.setTitle("异常交易行为")

# 按风险类别分组
for risk_category, category_group in df.groupby('风险类别'):
    category_topic = root_topic.addSubTopic()
    category_topic.setTitle(risk_category)

    # 在每个风险类别内按风险项名称分组
    for risk_item_name_full, item_group in category_group.groupby('风险项名称'):
        # 清理风险项名称：移除所有时间标注和前缀
        cleaned_risk_item_name = risk_item_name_full
        # 移除时间标注
        cleaned_risk_item_name = cleaned_risk_item_name.replace("【实时】", "")
        cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T）】", "")
        cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T-1）】", "")
        cleaned_risk_item_name = cleaned_risk_item_name.replace("【查询】", "")
        # 移除其他标注
        cleaned_risk_item_name = cleaned_risk_item_name.replace("(自营账户)", "")
        cleaned_risk_item_name = cleaned_risk_item_name.replace("（重点指标）", "")
        cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点指标）", "")
        cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点）", "")
        cleaned_risk_item_name = cleaned_risk_item_name.strip()


        item_topic = category_topic.addSubTopic()
        item_topic.setTitle(cleaned_risk_item_name)

        # 确定风险项名称的颜色
        is_secondary_important = risk_item_name_full in secondary_important_indicators
        is_primary_important = (risk_category == "拉抬打压" and not is_secondary_important)

        # 设置风险项颜色
        if is_primary_important:
            item_topic.setAttribute("fill", COLOR_RED)
        elif is_secondary_important:
            item_topic.setAttribute("fill", COLOR_YELLOW)
        else:
            item_topic.setAttribute("fill", COLOR_GREEN)

        # 获取此风险项名称的唯一表达式（不显示在思维导图中，仅用于颜色判断）
        expression = item_group['表达式'].iloc[0] if not item_group['表达式'].empty else "无表达式"
        
        # 解析表达式并为触发器分配颜色
        current_trigger_color_index = 0 # 为每个风险项重置颜色索引
        trigger_colors, _ = parse_expression_and_assign_colors(expression, current_trigger_color_index)

        # 在风险项下添加所有触发器
        for _, row in item_group.iterrows():
            trigger_id = row['触发原因编号']
            trigger = row['触发原因']
            weight = row['权重']
            threshold = row['阈值']

            trigger_node_text = f"{trigger} | 权重: {weight}, 阈值: {threshold}"

            trigger_topic = item_topic.addSubTopic()
            trigger_topic.setTitle(trigger_node_text)

            # 根据解析的表达式应用颜色
            if trigger_id in trigger_colors:
                trigger_topic.setAttribute("fill", trigger_colors[trigger_id])

# 生成OPML格式文件（支持颜色）
def generate_opml():
    """生成OPML格式的思维导图文件，支持颜色显示"""
    import xml.etree.ElementTree as ET
    from datetime import datetime
    
    # 创建OPML根元素
    opml = ET.Element("opml", version="2.0")
    
    # 添加头部信息
    head = ET.SubElement(opml, "head")
    ET.SubElement(head, "title").text = "异常交易行为监控规则"
    ET.SubElement(head, "dateCreated").text = datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")
    ET.SubElement(head, "dateModified").text = datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")
    ET.SubElement(head, "ownerName").text = "Kiro AI Assistant"
    ET.SubElement(head, "expansionState").text = "1,2,3,4,5,6,7,8,9,10,11,12,13"
    
    # 创建主体
    body = ET.SubElement(opml, "body")
    
    # 根节点
    root_outline = ET.SubElement(body, "outline", text="异常交易行为", type="text")
    
    # 按风险类别分组
    for risk_category, category_group in df.groupby('风险类别'):
        category_outline = ET.SubElement(root_outline, "outline", text=risk_category, type="text")
        
        # 在每个风险类别内按风险项名称分组
        for risk_item_name_full, item_group in category_group.groupby('风险项名称'):
            # 清理风险项名称
            cleaned_risk_item_name = risk_item_name_full
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【实时】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T）】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T-1）】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【查询】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("(自营账户)", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（重点指标）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点指标）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.strip()
            
            # 确定颜色 - 修复优先级判断逻辑
            is_secondary_important = risk_item_name_full in secondary_important_indicators
            is_primary_important = (risk_category == "拉抬打压" and not is_secondary_important)
            
            if is_primary_important:
                color = COLOR_RED
                priority = "高"
            elif is_secondary_important:
                color = COLOR_YELLOW
                priority = "中"
            else:
                color = COLOR_GREEN
                priority = "低"
            
            # 创建风险项节点，添加颜色和优先级信息
            item_text = f"{cleaned_risk_item_name} [优先级: {priority}]"
            item_outline = ET.SubElement(category_outline, "outline", 
                                       text=item_text, 
                                       type="text",
                                       color=color,
                                       priority=priority)
            
            # 获取表达式并解析颜色
            expression = item_group['表达式'].iloc[0] if not item_group['表达式'].empty else "无表达式"
            current_trigger_color_index = 0
            trigger_colors, _ = parse_expression_and_assign_colors(expression, current_trigger_color_index)
            
            # 添加触发原因
            for _, row in item_group.iterrows():
                trigger_id = row['触发原因编号']
                trigger = row['触发原因']
                weight = row['权重']
                threshold = row['阈值']
                
                trigger_text = f"{trigger} | 权重: {weight}, 阈值: {threshold}"
                trigger_color = trigger_colors.get(trigger_id, "#000000")
                
                ET.SubElement(item_outline, "outline", 
                            text=trigger_text, 
                            type="text",
                            color=trigger_color,
                            triggerId=str(trigger_id))
    
    # 格式化XML
    def indent(elem, level=0):
        i = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = i + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
            for elem in elem:
                indent(elem, level + 1)
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = i
    
    indent(opml)
    
    # 保存OPML文件
    tree = ET.ElementTree(opml)
    opml_file = os.path.join(script_dir, "异常交易行为.opml")
    tree.write(opml_file, encoding='utf-8', xml_declaration=True)
    return opml_file

# 修复XMind文件生成问题
def create_simple_xmind():
    """创建一个简单但有效的XMind文件"""
    try:
        # 直接使用xmind库创建新工作簿
        from xmind.core.workbook import WorkbookDocument
        
        # 创建新的工作簿
        workbook = WorkbookDocument()
        
        # 获取默认工作表
        sheet = workbook.getPrimarySheet()
        if sheet is None:
            sheet = workbook.createSheet()
            workbook.addSheet(sheet)
            workbook.setPrimarySheet(sheet)
        
        # 获取或创建根主题
        root_topic = sheet.getRootTopic()
        if root_topic is None:
            from xmind.core.topic import TopicElement
            root_topic = TopicElement()
            sheet.setRootTopic(root_topic)
        
        root_topic.setTitle("异常交易行为")
        
        # 按风险类别分组
        for risk_category, category_group in df.groupby('风险类别'):
            category_topic = root_topic.addSubTopic()
            category_topic.setTitle(risk_category)

            # 在每个风险类别内按风险项名称分组
            for risk_item_name_full, item_group in category_group.groupby('风险项名称'):
                # 清理风险项名称
                cleaned_risk_item_name = risk_item_name_full
                cleaned_risk_item_name = cleaned_risk_item_name.replace("【实时】", "")
                cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T）】", "")
                cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T-1）】", "")
                cleaned_risk_item_name = cleaned_risk_item_name.replace("【查询】", "")
                cleaned_risk_item_name = cleaned_risk_item_name.replace("(自营账户)", "")
                cleaned_risk_item_name = cleaned_risk_item_name.replace("（重点指标）", "")
                cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点指标）", "")
                cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点）", "")
                cleaned_risk_item_name = cleaned_risk_item_name.strip()

                item_topic = category_topic.addSubTopic()
                
                # 确定优先级并添加到标题中
                is_secondary_important = risk_item_name_full in secondary_important_indicators
                is_primary_important = (risk_category == "拉抬打压" and not is_secondary_important)
                
                if is_primary_important:
                    priority = "【高优先级】"
                elif is_secondary_important:
                    priority = "【中优先级】"
                else:
                    priority = "【低优先级】"
                
                item_topic.setTitle(f"{priority} {cleaned_risk_item_name}")

                # 获取表达式并解析颜色
                expression = item_group['表达式'].iloc[0] if not item_group['表达式'].empty else "无表达式"
                current_trigger_color_index = 0
                trigger_colors, _ = parse_expression_and_assign_colors(expression, current_trigger_color_index)

                # 在风险项下添加所有触发器
                for _, row in item_group.iterrows():
                    trigger_id = row['触发原因编号']
                    trigger = row['触发原因']
                    weight = row['权重']
                    threshold = row['阈值']

                    trigger_node_text = f"触发器{trigger_id}: {trigger} | 权重: {weight}, 阈值: {threshold}"

                    trigger_topic = item_topic.addSubTopic()
                    trigger_topic.setTitle(trigger_node_text)
        
        return workbook
        
    except Exception as e:
        print(f"创建XMind工作簿时出错: {e}")
        return None

# 保存文件到脚本同一目录下
script_dir = os.path.dirname(os.path.abspath(__file__))

# 1. 生成OPML文件（支持颜色）
try:
    opml_file = generate_opml()
    print(f"✓ OPML文件已成功生成: {opml_file}")
except Exception as e:
    print(f"✗ OPML文件生成失败: {e}")

# 2. 生成修复后的XMind文件 - 使用最简单的方法
def create_basic_xmind():
    """创建基本的XMind文件，不使用复杂的模板"""
    try:
        # 创建基本的XML内容
        content_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<xmap-content xmlns="urn:xmind:xmap:xmlns:content:2.0" version="2.0">
  <sheet id="sheet1">
    <topic id="root">
      <title>异常交易行为</title>
    </topic>
  </sheet>
</xmap-content>'''
        
        # 创建基本的ZIP文件
        import zipfile
        xmind_file = os.path.join(script_dir, "异常交易行为_基础版.xmind")
        
        with zipfile.ZipFile(xmind_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            zf.writestr('content.xml', content_xml)
            
            # 添加基本的manifest
            manifest = '''<?xml version="1.0" encoding="UTF-8"?>
<manifest xmlns="urn:xmind:xmap:xmlns:manifest:1.0">
  <file-entry full-path="content.xml" media-type="text/xml"/>
</manifest>'''
            zf.writestr('META-INF/manifest.xml', manifest)
        
        return xmind_file
    except Exception as e:
        print(f"创建基础XMind文件失败: {e}")
        return None

try:
    basic_xmind = create_basic_xmind()
    if basic_xmind:
        print(f"✓ 基础XMind文件已成功生成: {basic_xmind}")
    else:
        print("✗ XMind文件生成失败")
except Exception as e:
    print(f"✗ XMind文件生成失败: {e}")

# 3. 生成FreeMind格式文件（.mm格式，也支持颜色）
def generate_freemind():
    """生成FreeMind格式的思维导图文件"""
    import xml.etree.ElementTree as ET
    
    # 创建根元素
    map_elem = ET.Element("map", version="1.0.1")
    
    # 根节点
    root_node = ET.SubElement(map_elem, "node", 
                             COLOR="#000000", 
                             CREATED="1753676912996",
                             ID="root",
                             MODIFIED="1753676912996",
                             TEXT="异常交易行为")
    
    # 按风险类别分组
    for risk_category, category_group in df.groupby('风险类别'):
        category_node = ET.SubElement(root_node, "node",
                                    COLOR="#000000",
                                    CREATED="1753676912996",
                                    MODIFIED="1753676912996",
                                    TEXT=risk_category)
        
        # 在每个风险类别内按风险项名称分组
        for risk_item_name_full, item_group in category_group.groupby('风险项名称'):
            # 清理风险项名称
            cleaned_risk_item_name = risk_item_name_full
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【实时】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T）】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T-1）】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【查询】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("(自营账户)", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（重点指标）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点指标）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.strip()
            
            # 确定颜色
            is_secondary_important = risk_item_name_full in secondary_important_indicators
            is_primary_important = (risk_category == "拉抬打压" and not is_secondary_important)
            
            if is_primary_important:
                color = "#FF0000"  # 红色
            elif is_secondary_important:
                color = "#FFAA00"  # 橙色（FreeMind中黄色不够明显）
            else:
                color = "#008000"  # 绿色
            
            item_node = ET.SubElement(category_node, "node",
                                    COLOR=color,
                                    CREATED="1753676912996",
                                    MODIFIED="1753676912996",
                                    TEXT=cleaned_risk_item_name)
            
            # 获取表达式并解析颜色
            expression = item_group['表达式'].iloc[0] if not item_group['表达式'].empty else "无表达式"
            current_trigger_color_index = 0
            trigger_colors, _ = parse_expression_and_assign_colors(expression, current_trigger_color_index)
            
            # 添加触发原因
            for _, row in item_group.iterrows():
                trigger_id = row['触发原因编号']
                trigger = row['触发原因']
                weight = row['权重']
                threshold = row['阈值']
                
                trigger_text = f"{trigger} | 权重: {weight}, 阈值: {threshold}"
                trigger_color = trigger_colors.get(trigger_id, "#000000")
                
                ET.SubElement(item_node, "node",
                            COLOR=trigger_color,
                            CREATED="1753676912996",
                            MODIFIED="1753676912996",
                            TEXT=trigger_text)
    
    # 格式化XML
    def indent(elem, level=0):
        i = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = i + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
            for elem in elem:
                indent(elem, level + 1)
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = i
    
    indent(map_elem)
    
    # 保存FreeMind文件
    tree = ET.ElementTree(map_elem)
    mm_file = os.path.join(script_dir, "异常交易行为.mm")
    tree.write(mm_file, encoding='utf-8', xml_declaration=True)
    return mm_file

# 4. 生成FreeMind文件
try:
    mm_file = generate_freemind()
    print(f"✓ FreeMind文件已成功生成: {mm_file}")
except Exception as e:
    print(f"✗ FreeMind文件生成失败: {e}")

# 5. 生成XMind优化的OPML文件
def generate_xmind_optimized_opml():
    """生成针对XMind优化的OPML文件，使用XMind支持的颜色格式"""
    import xml.etree.ElementTree as ET
    from datetime import datetime
    
    # 创建OPML根元素
    opml = ET.Element("opml", version="2.0")
    
    # 添加头部信息
    head = ET.SubElement(opml, "head")
    ET.SubElement(head, "title").text = "异常交易行为监控规则 (XMind优化版)"
    ET.SubElement(head, "dateCreated").text = datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")
    ET.SubElement(head, "dateModified").text = datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")
    ET.SubElement(head, "ownerName").text = "Kiro AI Assistant"
    ET.SubElement(head, "expansionState").text = "1,2,3,4,5,6,7,8,9,10,11,12,13"
    
    # XMind支持的颜色映射
    xmind_colors = {
        COLOR_RED: "red",
        COLOR_YELLOW: "yellow", 
        COLOR_GREEN: "green"
    }
    
    # 创建主体
    body = ET.SubElement(opml, "body")
    
    # 根节点
    root_outline = ET.SubElement(body, "outline", text="异常交易行为", type="text")
    
    # 按风险类别分组
    for risk_category, category_group in df.groupby('风险类别'):
        category_outline = ET.SubElement(root_outline, "outline", text=risk_category, type="text")
        
        # 在每个风险类别内按风险项名称分组
        for risk_item_name_full, item_group in category_group.groupby('风险项名称'):
            # 清理风险项名称
            cleaned_risk_item_name = risk_item_name_full
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【实时】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T）】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T-1）】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【查询】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("(自营账户)", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（重点指标）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点指标）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.strip()
            
            # 确定颜色和优先级
            is_secondary_important = risk_item_name_full in secondary_important_indicators
            is_primary_important = (risk_category == "拉抬打压" and not is_secondary_important)
            
            if is_primary_important:
                color_hex = COLOR_RED
                color_name = "red"
                priority = "高"
                priority_symbol = "🔴"
            elif is_secondary_important:
                color_hex = COLOR_YELLOW
                color_name = "yellow"
                priority = "中"
                priority_symbol = "🟡"
            else:
                color_hex = COLOR_GREEN
                color_name = "green"
                priority = "低"
                priority_symbol = "🟢"
            
            # 创建风险项节点，使用多种颜色格式以提高兼容性
            item_text = f"{priority_symbol} {cleaned_risk_item_name} [优先级: {priority}]"
            item_attrs = {
                "text": item_text,
                "type": "text",
                "color": color_hex,  # 十六进制颜色
                "_note": f"优先级: {priority}",  # XMind笔记
                "priority": priority
            }
            
            # 添加XMind特定的属性
            if color_name:
                item_attrs["_color"] = color_name  # XMind颜色名称
                item_attrs["fillColor"] = color_hex  # 填充颜色
                item_attrs["fontColor"] = "#000000"  # 字体颜色
            
            item_outline = ET.SubElement(category_outline, "outline", **item_attrs)
            
            # 获取表达式并解析颜色
            expression = item_group['表达式'].iloc[0] if not item_group['表达式'].empty else "无表达式"
            current_trigger_color_index = 0
            trigger_colors, _ = parse_expression_and_assign_colors(expression, current_trigger_color_index)
            
            # 添加触发原因
            for _, row in item_group.iterrows():
                trigger_id = row['触发原因编号']
                trigger = row['触发原因']
                weight = row['权重']
                threshold = row['阈值']
                
                trigger_text = f"触发器{trigger_id}: {trigger} | 权重: {weight}, 阈值: {threshold}"
                trigger_color = trigger_colors.get(trigger_id, "#000000")
                
                trigger_attrs = {
                    "text": trigger_text,
                    "type": "text",
                    "color": trigger_color,
                    "fillColor": trigger_color,
                    "fontColor": "#FFFFFF" if trigger_color in ["#000000", "#800080", "#A52A2A"] else "#000000",
                    "triggerId": str(trigger_id),
                    "_note": f"表达式: {expression}"
                }
                
                ET.SubElement(item_outline, "outline", **trigger_attrs)
    
    # 格式化XML
    def indent(elem, level=0):
        i = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = i + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
            for elem in elem:
                indent(elem, level + 1)
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = i
    
    indent(opml)
    
    # 保存XMind优化的OPML文件
    tree = ET.ElementTree(opml)
    opml_file = os.path.join(script_dir, "异常交易行为_XMind优化版.opml")
    tree.write(opml_file, encoding='utf-8', xml_declaration=True)
    return opml_file

# 6. 生成MindManager格式的OPML文件
def generate_mindmanager_opml():
    """生成针对MindManager优化的OPML文件"""
    import xml.etree.ElementTree as ET
    from datetime import datetime
    
    # 创建OPML根元素
    opml = ET.Element("opml", version="2.0")
    
    # 添加头部信息
    head = ET.SubElement(opml, "head")
    ET.SubElement(head, "title").text = "异常交易行为监控规则 (MindManager优化版)"
    ET.SubElement(head, "dateCreated").text = datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")
    ET.SubElement(head, "dateModified").text = datetime.now().strftime("%a, %d %b %Y %H:%M:%S GMT")
    ET.SubElement(head, "ownerName").text = "Kiro AI Assistant"
    
    # 创建主体
    body = ET.SubElement(opml, "body")
    
    # 根节点
    root_outline = ET.SubElement(body, "outline", text="异常交易行为", type="text")
    
    # 按风险类别分组
    for risk_category, category_group in df.groupby('风险类别'):
        category_outline = ET.SubElement(root_outline, "outline", text=risk_category, type="text")
        
        # 在每个风险类别内按风险项名称分组
        for risk_item_name_full, item_group in category_group.groupby('风险项名称'):
            # 清理风险项名称
            cleaned_risk_item_name = risk_item_name_full
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【实时】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T）】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T-1）】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【查询】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("(自营账户)", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（重点指标）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点指标）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.strip()
            
            # 确定颜色和优先级
            is_secondary_important = risk_item_name_full in secondary_important_indicators
            is_primary_important = (risk_category == "拉抬打压" and not is_secondary_important)
            
            if is_primary_important:
                priority = "1"  # MindManager优先级格式
                priority_text = "高"
                color_rgb = "255,0,0"  # RGB格式
            elif is_secondary_important:
                priority = "2"
                priority_text = "中"
                color_rgb = "255,255,0"
            else:
                priority = "3"
                priority_text = "低"
                color_rgb = "0,128,0"
            
            # 创建风险项节点，使用MindManager格式
            item_text = f"{cleaned_risk_item_name} [优先级: {priority_text}]"
            item_attrs = {
                "text": item_text,
                "type": "text",
                "priority": priority,
                "fillColor": color_rgb,
                "_note": f"优先级: {priority_text}, 类别: {risk_category}"
            }
            
            item_outline = ET.SubElement(category_outline, "outline", **item_attrs)
            
            # 获取表达式并解析颜色
            expression = item_group['表达式'].iloc[0] if not item_group['表达式'].empty else "无表达式"
            current_trigger_color_index = 0
            trigger_colors, _ = parse_expression_and_assign_colors(expression, current_trigger_color_index)
            
            # 添加触发原因
            for _, row in item_group.iterrows():
                trigger_id = row['触发原因编号']
                trigger = row['触发原因']
                weight = row['权重']
                threshold = row['阈值']
                
                trigger_text = f"触发器{trigger_id}: {trigger} | 权重: {weight}, 阈值: {threshold}"
                
                # 将十六进制颜色转换为RGB
                hex_color = trigger_colors.get(trigger_id, "#000000")
                rgb_color = f"{int(hex_color[1:3], 16)},{int(hex_color[3:5], 16)},{int(hex_color[5:7], 16)}"
                
                trigger_attrs = {
                    "text": trigger_text,
                    "type": "text",
                    "fillColor": rgb_color,
                    "triggerId": str(trigger_id),
                    "_note": f"表达式: {expression}, 触发器ID: {trigger_id}"
                }
                
                ET.SubElement(item_outline, "outline", **trigger_attrs)
    
    # 格式化XML
    def indent(elem, level=0):
        i = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = i + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
            for elem in elem:
                indent(elem, level + 1)
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = i
    
    indent(opml)
    
    # 保存MindManager优化的OPML文件
    tree = ET.ElementTree(opml)
    opml_file = os.path.join(script_dir, "异常交易行为_MindManager优化版.opml")
    tree.write(opml_file, encoding='utf-8', xml_declaration=True)
    return opml_file

try:
    xmind_opml = generate_xmind_optimized_opml()
    print(f"✓ XMind优化OPML文件已成功生成: {xmind_opml}")
except Exception as e:
    print(f"✗ XMind优化OPML文件生成失败: {e}")

try:
    mm_opml = generate_mindmanager_opml()
    print(f"✓ MindManager优化OPML文件已成功生成: {mm_opml}")
except Exception as e:
    print(f"✗ MindManager优化OPML文件生成失败: {e}")

# 7. 生成HTML思维导图可视化文件
def generate_html_visualization():
    """生成思维导图风格的HTML可视化文件，与思维导图颜色方案一致"""
    
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>异常交易行为监控规则 - 思维导图可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            padding: 20px;
        }
        
        .mindmap-container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .mindmap-content {
            padding: 40px;
            background: white;
        }
        
        .usage-guide {
            background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
            border: 2px solid #3498db;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.1);
        }
        
        .usage-guide h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
        }
        
        .usage-guide h3::before {
            content: "📖";
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .usage-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        
        .usage-section {
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .usage-section h4 {
            color: #34495e;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .color-legend {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 8px;
        }
        
        .color-item {
            display: flex;
            align-items: center;
            font-size: 0.9em;
        }
        
        .color-dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 6px;
            border: 1px solid #ddd;
        }
        
        .usage-list {
            list-style: none;
            padding: 0;
        }
        
        .usage-list li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
            font-size: 0.95em;
            line-height: 1.4;
        }
        
        .usage-list li::before {
            content: "•";
            color: #3498db;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .branches-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 50px;
            margin-top: 30px;
            justify-items: center;
        }
        
        .category-branch {
            width: 100%;
            max-width: 500px;
            position: relative;
        }
        
        .category-node {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 18px 30px;
            border-radius: 30px;
            font-size: 1.4em;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
            margin-bottom: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .category-node:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(231, 76, 60, 0.4);
        }
        
        .risk-items-container {
            margin-left: 25px;
            border-left: 4px solid #e74c3c;
            padding-left: 25px;
        }
        
        .risk-item {
            margin-bottom: 18px;
            position: relative;
        }
        
        .risk-node {
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            position: relative;
            font-size: 1.05em;
        }
        
        .risk-node:hover {
            transform: translateX(8px);
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
        }
        
        /* 监控项目颜色方案 - 调整优先级描述 */
        .risk-node.priority-high {
            background: #FF0000;
            color: white;
        }
        
        .risk-node.priority-medium {
            background: #FFFF00;
            color: #333;
        }
        
        .risk-node.priority-low {
            background: #008000;
            color: white;
        }
        
        .triggers-container {
            display: none;
            margin-top: 15px;
            margin-left: 25px;
            border-left: 3px solid #ddd;
            padding-left: 20px;
        }
        
        .risk-item.expanded .triggers-container {
            display: block;
        }
        
        .trigger-node {
            margin: 12px 0;
            padding: 12px 18px;
            border-radius: 18px;
            font-size: 0.95em;
            color: #333;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            cursor: pointer;
            line-height: 1.4;
            border: 1px solid #ddd;
        }
        
        .trigger-node:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
        }
        
        /* 触发器颜色 - 柔和的颜色方案，避免与监控项目颜色冲突 */
        .trigger-color-0 { background: #e8f4f8; border-color: #3498db; }
        .trigger-color-1 { background: #f0f3ff; border-color: #6c5ce7; }
        .trigger-color-2 { background: #e8f5e8; border-color: #27ae60; }
        .trigger-color-3 { background: #fff3e0; border-color: #f39c12; }
        .trigger-color-4 { background: #f8e8ff; border-color: #9b59b6; }
        .trigger-color-5 { background: #e0f7fa; border-color: #1abc9c; }
        .trigger-color-6 { background: #fce4ec; border-color: #e91e63; }
        .trigger-color-7 { background: #f5f5f5; border-color: #95a5a6; }
        .trigger-color-8 { background: #fff8e1; border-color: #f1c40f; }
        .trigger-color-9 { background: #e3f2fd; border-color: #2196f3; }
        .trigger-color-10 { background: #f3e5f5; border-color: #8e24aa; }
        
        .expand-indicator {
            float: right;
            font-size: 1em;
            transition: transform 0.3s ease;
            margin-left: 10px;
        }
        
        .risk-item.expanded .expand-indicator {
            transform: rotate(180deg);
        }
        
        .search-controls {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 35px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        
        .search-box {
            width: 100%;
            max-width: 400px;
            padding: 12px 20px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1em;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .search-box:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .filter-buttons {
            margin-top: 15px;
        }
        
        .filter-btn {
            background: #e9ecef;
            border: none;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .filter-btn.active {
            background: #3498db;
            color: white;
        }
        
        .control-buttons {
            margin: 20px 0;
            text-align: left;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
        }
        
        .control-btn::before {
            content: "📂";
            font-size: 1.1em;
        }
        
        .control-btn.expanded::before {
            content: "📁";
        }
        
        .stats-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        
        .stat-item {
            flex: 1;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .priority-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .priority-indicator.high { background: #FF0000; }
        .priority-indicator.medium { background: #FFFF00; }
        .priority-indicator.low { background: #008000; }
        
        @media (max-width: 768px) {
            .usage-content {
                grid-template-columns: 1fr;
            }
            
            .branches-container {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .mindmap-content {
                padding: 20px;
            }
            
            .category-branch {
                max-width: 100%;
            }
            
            .stats-info {
                flex-direction: column;
                gap: 15px;
            }
            
            .risk-items-container {
                margin-left: 15px;
                padding-left: 15px;
            }
            
            .triggers-container {
                margin-left: 15px;
                padding-left: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="mindmap-container">
        <div class="header">
            <h1>🧠 异常交易行为监控规则 - 思维导图</h1>
            <p>智能化风险监控规则可视化展示</p>
        </div>
        
        <div class="mindmap-content">
            <div class="usage-guide">
                <h3>使用方法说明</h3>
                <div class="usage-content">
                    <div class="usage-section">
                        <h4>🎨 颜色标注说明</h4>
                        <div class="color-legend">
                            <div class="color-item">
                                <div class="color-dot" style="background: #FF0000;"></div>
                                <span>重点结果类</span>
                            </div>
                            <div class="color-item">
                                <div class="color-dot" style="background: #FFFF00;"></div>
                                <span>重点行为类</span>
                            </div>
                            <div class="color-item">
                                <div class="color-dot" style="background: #008000;"></div>
                                <span>一般行为类</span>
                            </div>
                        </div>
                        <p style="margin-top: 10px; font-size: 0.9em; color: #666;">
                            触发条件采用柔和色彩，根据逻辑表达式分组显示
                        </p>
                    </div>
                    <div class="usage-section">
                        <h4>🖱️ 操作指南</h4>
                        <ul class="usage-list">
                            <li>点击风险类别可展开/收起该类别下的所有项目</li>
                            <li>点击监控项目可查看详细的触发条件</li>
                            <li>使用搜索框快速定位特定风险项目</li>
                            <li>使用过滤按钮按优先级筛选显示内容</li>
                            <li>使用"展开/收起所有"按钮批量操作</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="search-controls">
                <input type="text" class="search-box" placeholder="🔍 搜索风险项目或触发条件..." id="searchInput">
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">全部</button>
                    <button class="filter-btn" data-filter="high">重点结果类</button>
                    <button class="filter-btn" data-filter="medium">重点行为类</button>
                    <button class="filter-btn" data-filter="low">一般行为类</button>
                </div>
            </div>
            
            <div class="control-buttons">
                <button class="control-btn" id="expandAllBtn" onclick="toggleAllNodes()">展开所有节点</button>
            </div>
            
            <div class="stats-info">
                <div class="stat-item">
                    <div class="stat-number" id="total-categories">0</div>
                    <div class="stat-label">风险类别</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="total-items">0</div>
                    <div class="stat-label">监控项目</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="total-triggers">0</div>
                    <div class="stat-label">触发条件</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="high-priority">0</div>
                    <div class="stat-label">重点结果类</div>
                </div>
            </div>
            
            <div class="branches-container" id="mindmap-branches">
                <!-- 动态生成思维导图分支 -->
            </div>
        </div>
    </div>
    
    <script>
        // 数据
        const data = '''
    
    # 将数据转换为JavaScript格式
    js_data = "{\n"
    
    category_count = 0
    total_items = 0
    total_triggers = 0
    high_priority_count = 0
    
    for risk_category, category_group in df.groupby('风险类别'):
        category_count += 1
        js_data += f'    "{risk_category}": [\n'
        
        category_items = []
        for risk_item_name_full, item_group in category_group.groupby('风险项名称'):
            total_items += 1
            
            # 清理风险项名称
            cleaned_risk_item_name = risk_item_name_full
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【实时】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T）】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【日终（T-1）】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("【查询】", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("(自营账户)", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（重点指标）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点指标）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.replace("（次重点）", "")
            cleaned_risk_item_name = cleaned_risk_item_name.strip()
            
            # 确定优先级
            is_secondary_important = risk_item_name_full in secondary_important_indicators
            is_primary_important = (risk_category == "拉抬打压" and not is_secondary_important)
            
            if is_primary_important:
                priority = "high"
                priority_text = "重点结果类"
                high_priority_count += 1
            elif is_secondary_important:
                priority = "medium"
                priority_text = "重点行为类"
            else:
                priority = "low"
                priority_text = "一般行为类"
            
            # 获取表达式
            expression = item_group['表达式'].iloc[0] if not item_group['表达式'].empty else "无表达式"
            
            # 构建触发器数据
            triggers = []
            for _, row in item_group.iterrows():
                total_triggers += 1
                trigger_data = {
                    'id': int(row['触发原因编号']),
                    'name': row['触发原因'].replace('"', '\\"'),
                    'weight': float(row['权重']),
                    'threshold': str(row['阈值'])
                }
                triggers.append(trigger_data)
            
            item_data = {
                'name': cleaned_risk_item_name.replace('"', '\\"'),
                'original_name': risk_item_name_full.replace('"', '\\"'),
                'priority': priority,
                'priority_text': priority_text,
                'expression': expression.replace('"', '\\"'),
                'triggers': triggers
            }
            category_items.append(item_data)
        
        # 转换为JavaScript对象格式
        for i, item in enumerate(category_items):
            js_data += f'        {{\n'
            js_data += f'            "name": "{item["name"]}",\n'
            js_data += f'            "originalName": "{item["original_name"]}",\n'
            js_data += f'            "priority": "{item["priority"]}",\n'
            js_data += f'            "priorityText": "{item["priority_text"]}",\n'
            js_data += f'            "expression": "{item["expression"]}",\n'
            js_data += f'            "triggers": [\n'
            
            for j, trigger in enumerate(item["triggers"]):
                js_data += f'                {{\n'
                js_data += f'                    "id": {trigger["id"]},\n'
                js_data += f'                    "name": "{trigger["name"]}",\n'
                js_data += f'                    "weight": {trigger["weight"]},\n'
                js_data += f'                    "threshold": "{trigger["threshold"]}"\n'
                js_data += f'                }}'
                if j < len(item["triggers"]) - 1:
                    js_data += ','
                js_data += '\n'
            
            js_data += f'            ]\n'
            js_data += f'        }}'
            if i < len(category_items) - 1:
                js_data += ','
            js_data += '\n'
        
        js_data += f'    ]'
        if risk_category != list(df.groupby('风险类别'))[-1][0]:
            js_data += ','
        js_data += '\n'
    
    js_data += "}"
    
    html_content += js_data
    html_content += f'''
        
        // 更新统计数据
        document.getElementById('total-categories').textContent = {category_count};
        document.getElementById('total-items').textContent = {total_items};
        document.getElementById('total-triggers').textContent = {total_triggers};
        document.getElementById('high-priority').textContent = {high_priority_count};
        
        // 触发器颜色映射 - 柔和颜色方案
        const triggerColorMap = {{
            '#FF0000': 0, '#0000FF': 1, '#00FF00': 2, '#FFA500': 3, '#800080': 4,
            '#00FFFF': 5, '#FF00FF': 6, '#808080': 7, '#A52A2A': 8, '#4682B4': 9, '#FFC0CB': 10
        }};
        
        // 全局展开状态
        let allExpanded = false;
        
        // 解析表达式并分配颜色 - 与Python函数一致
        function parseExpressionAndAssignColors(expression) {{
            const triggerColors = {{}};
            let colorIndex = 0;
            const colors = ['#FF0000', '#0000FF', '#00FF00', '#FFA500', '#800080', 
                          '#00FFFF', '#FF00FF', '#808080', '#A52A2A', '#4682B4', '#FFC0CB'];
            
            // 简化的表达式解析 - 按&&分割组件
            const andComponents = expression.split('&&').map(comp => comp.trim());
            
            andComponents.forEach(component => {{
                const currentColor = colors[colorIndex % colors.length];
                colorIndex++;
                
                // 移除外层括号
                let cleaned = component.replace(/^\\(|\\)$/g, '');
                
                // 处理OR组件
                if (cleaned.includes('||')) {{
                    const orParts = cleaned.split('||');
                    orParts.forEach(part => {{
                        const triggerNum = parseInt(part.trim());
                        if (!isNaN(triggerNum)) {{
                            triggerColors[triggerNum] = currentColor;
                        }}
                    }});
                }} else {{
                    const triggerNum = parseInt(cleaned);
                    if (!isNaN(triggerNum)) {{
                        triggerColors[triggerNum] = currentColor;
                    }}
                }}
            }});
            
            return triggerColors;
        }}
        
        // 按重点指标数量排序分类
        function sortCategoriesByPriority(data) {{
            const categoryPriorities = {{}};
            
            // 计算每个分类的重点指标数量
            Object.keys(data).forEach(categoryName => {{
                const items = data[categoryName];
                const highPriorityCount = items.filter(item => item.priority === 'high').length;
                const mediumPriorityCount = items.filter(item => item.priority === 'medium').length;
                categoryPriorities[categoryName] = highPriorityCount * 2 + mediumPriorityCount; // 高优先级权重更高
            }});
            
            // 按优先级分数排序
            return Object.keys(data).sort((a, b) => categoryPriorities[b] - categoryPriorities[a]);
        }}
        
        // 渲染思维导图
        function renderMindmap(filteredData = data) {{
            const container = document.getElementById('mindmap-branches');
            container.innerHTML = '';
            
            // 按重点指标数量排序
            const sortedCategories = sortCategoriesByPriority(filteredData);
            
            sortedCategories.forEach(categoryName => {{
                const categoryItems = filteredData[categoryName];
                if (!categoryItems || categoryItems.length === 0) return;
                
                const categoryBranch = document.createElement('div');
                categoryBranch.className = 'category-branch';
                
                // 计算分类的重点指标统计
                const highCount = categoryItems.filter(item => item.priority === 'high').length;
                const mediumCount = categoryItems.filter(item => item.priority === 'medium').length;
                const lowCount = categoryItems.filter(item => item.priority === 'low').length;
                
                let priorityInfo = '';
                if (highCount > 0) priorityInfo += `🔴结果类${{highCount}} `;
                if (mediumCount > 0) priorityInfo += `🟡行为类${{mediumCount}} `;
                if (lowCount > 0) priorityInfo += `🟢一般类${{lowCount}}`;
                
                categoryBranch.innerHTML = `
                    <div class="category-node" onclick="toggleCategory(this)">
                        ${{categoryName}} <br>
                        <small style="font-size: 0.8em; opacity: 0.9;">${{priorityInfo}}</small>
                    </div>
                    <div class="risk-items-container">
                        ${{categoryItems.map(item => {{
                            const triggerColors = parseExpressionAndAssignColors(item.expression);
                            return `
                                <div class="risk-item" data-priority="${{item.priority}}">
                                    <div class="risk-node priority-${{item.priority}}" onclick="toggleRiskItem(this)">
                                        <span class="priority-indicator ${{item.priority}}"></span>
                                        ${{item.name}}
                                        <span class="expand-indicator">▼</span>
                                    </div>
                                    <div class="triggers-container">
                                        ${{item.triggers.map(trigger => {{
                                            const triggerColor = triggerColors[trigger.id] || '#808080';
                                            const colorClass = triggerColorMap[triggerColor] !== undefined ? 
                                                `trigger-color-${{triggerColorMap[triggerColor]}}` : 'trigger-color-7';
                                            return `
                                                <div class="trigger-node ${{colorClass}}" title="触发器ID: ${{trigger.id}}">
                                                    <strong>[${{trigger.id}}]</strong> ${{trigger.name}}<br>
                                                    <small>权重: ${{trigger.weight}} | 阈值: ${{trigger.threshold}}</small>
                                                </div>
                                            `;
                                        }}).join('')}}
                                    </div>
                                </div>
                            `;
                        }}).join('')}}
                    </div>
                `;
                
                container.appendChild(categoryBranch);
            }});
        }}
        
        // 切换分类展开/收起
        function toggleCategory(categoryNode) {{
            const container = categoryNode.nextElementSibling;
            const isVisible = container.style.display !== 'none';
            container.style.display = isVisible ? 'none' : 'block';
        }}
        
        // 切换风险项展开/收起
        function toggleRiskItem(riskNode) {{
            const riskItem = riskNode.parentElement;
            riskItem.classList.toggle('expanded');
        }}
        
        // 展开/收起所有节点
        function toggleAllNodes() {{
            const allRiskItems = document.querySelectorAll('.risk-item');
            const allCategoryContainers = document.querySelectorAll('.risk-items-container');
            const btn = document.getElementById('expandAllBtn');
            
            if (allExpanded) {{
                // 收起所有监控项目的触发条件
                allRiskItems.forEach(item => {{
                    item.classList.remove('expanded');
                }});
                // 收起所有风险类别
                allCategoryContainers.forEach(container => {{
                    container.style.display = 'none';
                }});
                btn.textContent = '展开所有节点';
                btn.classList.remove('expanded');
                allExpanded = false;
            }} else {{
                // 展开所有监控项目的触发条件
                allRiskItems.forEach(item => {{
                    item.classList.add('expanded');
                }});
                // 展开所有风险类别
                allCategoryContainers.forEach(container => {{
                    container.style.display = 'block';
                }});
                btn.textContent = '收起所有节点';
                btn.classList.add('expanded');
                allExpanded = true;
            }}
        }}
        
        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function(e) {{
            const searchTerm = e.target.value.toLowerCase();
            filterData(searchTerm, getCurrentFilter());
        }});
        
        // 优先级过滤
        document.querySelectorAll('.filter-btn').forEach(btn => {{
            btn.addEventListener('click', function() {{
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.dataset.filter;
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                filterData(searchTerm, filter);
            }});
        }});
        
        function getCurrentFilter() {{
            return document.querySelector('.filter-btn.active').dataset.filter;
        }}
        
        function filterData(searchTerm, priorityFilter) {{
            const filteredData = {{}};
            
            Object.keys(data).forEach(categoryName => {{
                const filteredItems = data[categoryName].filter(item => {{
                    const matchesSearch = !searchTerm || 
                        item.name.toLowerCase().includes(searchTerm) ||
                        item.originalName.toLowerCase().includes(searchTerm) ||
                        item.triggers.some(trigger => trigger.name.toLowerCase().includes(searchTerm));
                    
                    const matchesPriority = priorityFilter === 'all' || item.priority === priorityFilter;
                    
                    return matchesSearch && matchesPriority;
                }});
                
                if (filteredItems.length > 0) {{
                    filteredData[categoryName] = filteredItems;
                }}
            }});
            
            renderMindmap(filteredData);
        }}
        
        // 初始化页面
        renderMindmap();
        
        // 添加统计数字动画效果
        document.addEventListener('DOMContentLoaded', function() {{
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {{
                const finalValue = parseInt(stat.textContent);
                let currentValue = 0;
                const increment = Math.ceil(finalValue / 30);
                
                const timer = setInterval(() => {{
                    currentValue += increment;
                    if (currentValue >= finalValue) {{
                        currentValue = finalValue;
                        clearInterval(timer);
                    }}
                    stat.textContent = currentValue;
                }}, 50);
            }});
        }});
    </script>
</body>
</html>'''
    
    # 保存HTML文件
    html_file = os.path.join(script_dir, "异常交易行为可视化.html")
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return html_file

try:
    html_file = generate_html_visualization()
    print(f"✓ HTML可视化文件已成功生成: {html_file}")
except Exception as e:
    print(f"✗ HTML可视化文件生成失败: {e}")

print("\n文件生成总结:")
print("=" * 60)
print("已生成以下格式的思维导图文件:")
print("1. OPML格式 (.opml) - 通用版本，支持颜色")
print("2. XMind优化OPML (.opml) - 专门针对XMind软件优化")
print("3. MindManager优化OPML (.opml) - 专门针对MindManager优化")
print("4. FreeMind格式 (.mm) - 开源思维导图软件，完整颜色支持")
print("5. XMind格式 (.xmind) - 基础版本")
print("6. HTML思维导图 (.html) - 思维导图风格的网页展示")
print("\n🧠 HTML思维导图可视化特性:")
print("=" * 60)
print("🎨 思维导图布局 - 中心主题向外辐射的经典思维导图结构")
print("🌈 一致的颜色方案 - 与思维导图软件完全一致的颜色编码:")
print("   • 高优先级: 红色 (#FF0000)")
print("   • 中优先级: 黄色 (#FFFF00)")
print("   • 低优先级: 绿色 (#008000)")
print("   • 触发器: 11种不同颜色，与表达式逻辑对应")
print("� 智细能搜索 - 实时搜索风险项目和触发条件")
print("🏷️ 优先级过滤 - 按高/中/低优先级筛选显示")
print("📊 统计面板 - 动态显示各类数据统计")
print("⚡ 交互式展开 - 点击节点展开/收起子项")
print("📱 响应式设计 - 完美适配各种设备")
print("📋 优化排版 - 更清晰的层级结构和视觉效果")
print("🔗 智能排序 - 重点指标较多的风险类别优先显示")
print("\n颜色编码说明:")
print("=" * 60)
print("🔴 红色节点 - 拉抬打压类别中的高优先级风险项")
print("🟡 黄色节点 - 次重点指标（中优先级）")
print("🟢 绿色节点 - 一般优先级风险项")
print("🌈 触发器颜色 - 根据布尔表达式中的AND/OR逻辑关系分组着色")
print("📊 分类统计 - 每个风险类别显示高/中/低优先级数量")
print("\n针对XMind颜色问题的解决方案:")
print("=" * 60)
print("🎯 XMind导入OPML颜色问题的解决方法:")
print("1. 使用 '异常交易行为_XMind优化版.opml' - 包含XMind特定的颜色属性")
print("2. 导入后手动设置主题样式")
print("3. 使用FreeMind格式 (.mm) - 颜色支持最完整")
print("4. 使用HTML思维导图 (.html) - 完美还原思维导图效果")
print("\n推荐使用顺序:")
print("1. HTML思维导图 (.html) - 完美的思维导图体验，颜色完全一致")
print("2. FreeMind格式 (.mm) - 传统思维导图软件，颜色显示准确")
print("3. XMind优化OPML - 针对XMind特别优化")
print("4. MindManager优化OPML - 如果使用MindManager")
print("5. 通用OPML - 其他思维导图软件")