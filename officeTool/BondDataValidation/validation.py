import pandas as pd
import os
import sys
import numpy as np
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config import BASE_DIRECTORY, SOURCE_FILES, FINAL_FILE, SHEET_MAPPINGS, DEFAULT_MAPPING


def read_and_extract_data(file_path, sheet_name, row_range, col_spec, column_names=None, numeric_columns=None):
    """
    从Excel文件中读取指定工作表的特定区域数据
    
    Parameters:
    file_path (str): 文件路径
    sheet_name (str): 工作表名称
    row_range (tuple or int): 行范围 (start, end) 或 (start,) 表示从start到末尾，或整数表示特定行
    col_spec (tuple or list): 列范围 (start, end) 或列索引列表
    column_names (list): 列名列表
    numeric_columns (list): 数值列名列表，如果为空或None，则所有列都转换为数值
    
    Returns:
    pd.DataFrame: 提取的数据
    """
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        
        # 处理行范围
        if isinstance(row_range, tuple):
            if len(row_range) == 1:
                # 从特定行到末尾
                row_start = row_range[0]
                row_end = None
            else:
                # 行范围
                row_start, row_end = row_range
        elif isinstance(row_range, int):
            # 特定行
            row_start = row_range
            row_end = row_range + 1
        else:
            # 默认处理
            row_start, row_end = 0, 10
            
        # 处理列
        if isinstance(col_spec, tuple) and len(col_spec) == 2:
            # 列范围
            col_start, col_end = col_spec
            if row_end is None:
                extracted_data = df.iloc[row_start:, col_start:col_end]
            else:
                extracted_data = df.iloc[row_start:row_end, col_start:col_end]
        elif isinstance(col_spec, list):
            # 列索引列表
            if row_end is None:
                extracted_data = df.iloc[row_start:, col_spec]
            else:
                extracted_data = df.iloc[row_start:row_end, col_spec]
        else:
            # 默认处理
            if row_end is None:
                extracted_data = df.iloc[row_start:, 0:10]
            else:
                extracted_data = df.iloc[row_start:row_end, 0:10]
        
        # 根据num_col配置转换数值列
        result_df = convert_numeric_columns(extracted_data, column_names, numeric_columns)
        
        # 设置列名
        if column_names:
            if len(column_names) == len(result_df.columns):
                result_df.columns = column_names
            else:
                print(f"警告: 配置的列名数量({len(column_names)})与实际提取的列数({len(result_df.columns)})不匹配")
                print(f"配置列名: {column_names}")
                print(f"实际列数: {result_df.columns.tolist() if hasattr(result_df, 'columns') else '未知'}")
                # 尝试截断或扩展列名以匹配实际列数
                if len(column_names) > len(result_df.columns):
                    # 列名多于实际列数，截断列名
                    result_df.columns = column_names[:len(result_df.columns)]
                else:
                    # 列名少于实际列数，使用原始列名并添加缺失的列名
                    truncated_columns = column_names + [f"未命名列_{i}" for i in range(len(column_names), len(result_df.columns))]
                    result_df.columns = truncated_columns
        
        return result_df
    except Exception as e:
        print(f"读取文件 {file_path} 的工作表 {sheet_name} 时出错: {e}")
        import traceback
        traceback.print_exc()
        result_df = pd.DataFrame()
        if column_names:
            # 确保DataFrame有正确的列数后再设置列名
            if len(result_df.columns) == len(column_names):
                result_df.columns = column_names
            else:
                # 如果列数不匹配，则先添加适当的列
                result_df = pd.DataFrame(columns=column_names)
        return result_df


def read_specific_rows_cols_data(file_path, sheet_name, row_index, col_indices, column_names=None, numeric_columns=None):
    """
    从Excel文件中读取指定工作表的特定行列数据
    
    Parameters:
    file_path (str): 文件路径
    sheet_name (str): 工作表名称
    row_index (int or tuple): 行索引，整数表示特定行，元组(2,)表示从第三行到末尾
    col_indices (list): 列索引列表
    column_names (list): 列名列表
    numeric_columns (list): 数值列名列表，如果为空或None，则所有列都转换为数值
    
    Returns:
    pd.DataFrame: 提取的数据
    """
    # 为了保持向后兼容，这个函数仍然存在，但内部调用统一的read_and_extract_data函数
    return read_and_extract_data(file_path, sheet_name, row_index, col_indices, column_names, numeric_columns)


def convert_numeric_columns(df, column_names=None, numeric_columns=None):
    """
    根据配置将指定列转换为数值型
    
    Parameters:
    df (pd.DataFrame): 原始数据框
    column_names (list): 列名列表
    numeric_columns (list): 数值列名列表，如果为空或None，则所有列都转换为数值
    
    Returns:
    pd.DataFrame: 转换后的数据框
    """
    result_df = df.copy()
    
    # 如果没有指定数值列或者数值列为空，则所有列都转换为数值
    if not numeric_columns:
        result_df = result_df.apply(pd.to_numeric, errors='coerce').fillna(0.0)
    else:
        # 只转换指定的数值列
        for i, col in enumerate(column_names or result_df.columns):
            # 检查索引是否在范围内
            if i < len(result_df.columns):
                if col in numeric_columns:
                    result_df.iloc[:, i] = pd.to_numeric(result_df.iloc[:, i], errors='coerce').fillna(0.0)
    
    return result_df


def is_single_row_config(config):
    """
    判断配置是否为单行提取配置
    
    Parameters:
    config (dict): 配置字典
    
    Returns:
    bool: 如果是单行提取配置返回True，否则返回False
    """
    # 检查是否为单行提取（整数）或特定行到末尾的提取（元组，如(2,)）
    return ('row_range' in config and isinstance(config['row_range'], int)) or \
           ('row_range' in config and isinstance(config['row_range'], tuple) and len(config['row_range']) == 1)


def is_range_to_end(config):
    """
    判断配置是否为从特定行到数据末尾的提取
    
    Parameters:
    config (dict): 配置字典
    
    Returns:
    bool: 如果是从特定行到末尾的提取返回True，否则返回False
    """
    return 'row_range' in config and isinstance(config['row_range'], tuple) and len(config['row_range']) == 1


def compare_dataframes(df1, df2, tolerance=0.01):
    """
    比较两个DataFrame是否在指定容差范围内一致
    
    Parameters:
    df1 (pd.DataFrame): 第一个数据框
    df2 (pd.DataFrame): 第二个数据框
    tolerance (float): 容差值
    
    Returns:
    bool: 如果数据一致返回True，否则返回False
    dict: 详细比较结果
    """
    try:
        # 检查形状是否一致
        if df1.shape != df2.shape:
            return False, {
                'error': f'DataFrame形状不匹配: {df1.shape} vs {df2.shape}'
            }
        
        diff = np.abs(df1.values - df2.values)
        is_consistent = np.all(diff < tolerance)
        return is_consistent, {
            'difference': diff,
            'max_difference': np.max(diff),
            'mean_difference': np.mean(diff)
        }
    except Exception as e:
        log_global_result(f"比较数据时出错: {e}")
        return False, {
            'error': str(e)
        }


def compare_text_dataframes(source_df, final_df, bond_code_col, tolerance=0.01):
    """
    比较文本数据，根据债券代码逐条核对
    
    Parameters:
    source_df (pd.DataFrame): 源数据框（已按债券代码汇总）
    final_df (pd.DataFrame): 最终数据框
    bond_code_col (str or int): 债券代码列名或索引
    tolerance (float): 容差值
    """
    log_global_result("\n开始逐条核对数据...")
    
    try:
        # 确保两个DataFrame都按照债券代码排序
        source_sorted = source_df.sort_values(bond_code_col).reset_index(drop=True)
        final_sorted = final_df.sort_values(bond_code_col).reset_index(drop=True)
        
        # 检查债券代码是否匹配
        source_bond_codes = set(source_sorted[bond_code_col])
        final_bond_codes = set(final_sorted[bond_code_col])
        
        # 查找只在source中存在的债券代码
        only_in_source = source_bond_codes - final_bond_codes
        # 查找只在final中存在的债券代码
        only_in_final = final_bond_codes - source_bond_codes
        
        if only_in_source:
            log_global_result(f"以下债券代码只在源数据中存在: {only_in_source}")
        
        if only_in_final:
            log_global_result(f"以下债券代码只在最终数据中存在: {only_in_final}")
        
        # 找到共同的债券代码
        common_bond_codes = source_bond_codes & final_bond_codes
        log_global_result(f"共同的债券代码数量: {len(common_bond_codes)}")
        
        inconsistent_count = 0
        consistent_count = 0
        
        # 获取数值列
        source_numeric_cols = source_sorted.select_dtypes(include=[np.number]).columns.tolist()
        final_numeric_cols = final_sorted.select_dtypes(include=[np.number]).columns.tolist()
        common_numeric_cols = list(set(source_numeric_cols) & set(final_numeric_cols))
        
        # 逐条比较共同债券代码的数据
        for bond_code in common_bond_codes:
            source_row = source_sorted[source_sorted[bond_code_col] == bond_code].iloc[0]
            final_row = final_sorted[final_sorted[bond_code_col] == bond_code].iloc[0]
            
            try:
                # 只比较数值列
                if common_numeric_cols:
                    source_numeric_values = source_row[common_numeric_cols]
                    final_numeric_values = final_row[common_numeric_cols]
                    
                    diff = np.abs(source_numeric_values.values - final_numeric_values.values)
                    
                    if np.all(diff < tolerance):
                        consistent_count += 1
                    else:
                        inconsistent_count += 1
                        log_global_result(f"\n债券代码 {bond_code} 数据不一致:")
                        
                        # 将行数据转换为DataFrame以便美观显示
                        comparison_df = pd.DataFrame({
                            '源数据': source_row,
                            '最终数据': final_row,
                            '差异': np.concatenate([diff, [''] * (len(source_row) - len(diff))])
                        }, index=source_row.index)
                        
                        # 使用pandas的DataFrame字符串表示方法
                        log_global_result("  数据对比:")
                        log_global_result(f"\n{comparison_df.to_string(index=True)}")
                else:
                    # 如果没有数值列，认为数据一致
                    consistent_count += 1
            except Exception as e:
                inconsistent_count += 1
                log_global_result(f"比较债券代码 {bond_code} 时出错: {e}")
        
        log_global_result(f"\n核对结果:")
        log_global_result(f"  一致记录数: {consistent_count}")
        log_global_result(f"  不一致记录数: {inconsistent_count}")
        log_global_result(f"  仅在源数据中存在: {len(only_in_source)}")
        log_global_result(f"  仅在最终数据中存在: {len(only_in_final)}")
        
        if inconsistent_count == 0 and len(only_in_source) == 0 and len(only_in_final) == 0:
            log_global_result("\n所有数据核对一致")
        else:
            log_global_result("\n数据存在不一致")
    except Exception as e:
        log_global_result(f"核对数据时发生错误: {e}")
        import traceback
        traceback.print_exc()


def validate_bond_data(directory, files, sheet_mapping, final_file, mapping_name=None):
    """
    验证债券数据
    
    Parameters:
    directory (str): 文件目录
    files (list): 要汇总的文件列表
    sheet_mapping (dict): 工作表映射配置
    final_file (str): 汇总后的文件
    mapping_name (str): 映射名称，用于特殊处理
    """
    # 获取源文件和最终文件的配置
    source_config = sheet_mapping['source']
    final_config = sheet_mapping['final']
    column_names = sheet_mapping.get('col', None)
    numeric_columns = sheet_mapping.get('num_col', None)  # 获取数值列配置
    
    # 检查文件是否存在并读取数据
    dataframes = []
    for file in files:
        file_path = os.path.join(directory, file)
        if os.path.exists(file_path):
            log_global_result(f"正在读取文件: {file}")
            
            # 使用统一的函数处理数据读取
            df = read_and_extract_data(
                file_path, 
                source_config['sheet_name'], 
                source_config['row_range'], 
                source_config.get('col_range', (0, 10)),
                column_names,
                numeric_columns  # 传递数值列配置
            )
            
            # 只有当DataFrame不为空时才添加
            if not df.empty:
                dataframes.append(df)
            else:
                log_global_result(f"警告: 从文件 {file} 读取的数据为空")
        else:
            log_global_result(f"文件不存在: {file_path}")

    # 读取最终文件数据
    final_file_path = os.path.join(directory, final_file)
    if os.path.exists(final_file_path):
        # 使用统一的函数处理数据读取
        final_df = read_and_extract_data(
            final_file_path,
            final_config['sheet_name'],
            final_config['row_range'],
            final_config.get('col_range', (0, 10)),
            column_names,
            numeric_columns  # 传递数值列配置
        )
    else:
        log_global_result(f"最终文件不存在: {final_file_path}")
        return

    # 合并数据
    if dataframes:
        # 需要根据特定键进行文本核对的配置
        text_verification_configs = {
            '现券持仓': '债券代码',
            '回购存续明细': '交易流水号',
            '回购存续交易多只保证券': '交易流水号'
        }
        
        if mapping_name in text_verification_configs:
            # 对于这些配置，使用pandas.concat进行数据合并
            try:
                # 使用pandas.concat合并所有数据
                combined_df = pd.concat(dataframes, ignore_index=True)
                
                # 确保有列名
                if column_names and len(column_names) == len(combined_df.columns):
                    combined_df.columns = column_names
                elif column_names:
                    # 如果列数不匹配，尝试修复
                    log_global_result(f"警告: 合并后数据列数({len(combined_df.columns)})与配置列数({len(column_names)})不匹配")
                    if len(column_names) > len(combined_df.columns):
                        combined_df.columns = column_names[:len(combined_df.columns)]
                    else:
                        combined_df.columns = column_names + [f"未命名列_{i}" for i in range(len(column_names), len(combined_df.columns))]
                
                # 确定分组键的名称
                group_key_col = text_verification_configs[mapping_name]
                
                # 检查分组键列是否存在
                if group_key_col in combined_df.columns:
                    # 根据分组键进行分组求和
                    # 只对数值列进行求和，保留分组键列
                    numeric_cols = combined_df.select_dtypes(include=[np.number]).columns.tolist()
                    agg_dict = {col: 'sum' for col in numeric_cols}
                    # 对于非数值列（如分组键），使用first方法保留第一个值
                    non_numeric_cols = combined_df.select_dtypes(exclude=[np.number]).columns.tolist()
                    for col in non_numeric_cols:
                        if col != group_key_col:  # 不对分组列应用first
                            agg_dict[col] = 'first'
                    
                    result_df = combined_df.groupby(group_key_col).agg(agg_dict).reset_index()
                    result_df = result_df.round(2)
                    
                    log_global_result(f"\n按{group_key_col}分组汇总后数据 ({mapping_name}):")
                    log_global_result(result_df.to_string())
                    
                    log_global_result(f"\n最终文件数据 ({mapping_name}):")
                    log_global_result(final_df.to_string())
                    
                    # 逐条核对数据
                    compare_text_dataframes(result_df, final_df, group_key_col)
                else:
                    log_global_result(f"警告: 分组键列 '{group_key_col}' 不存在于数据中")
                    log_global_result(f"可用列名: {list(combined_df.columns)}")
                    # 如果分组键列不存在，则使用原始的累加方式
                    result_df = safe_accumulate_dataframes(dataframes)
                    # 设置列名
                    if column_names and len(column_names) == len(result_df.columns):
                        result_df.columns = column_names
                    elif column_names:
                        # 如果列数不匹配，尝试修复
                        if len(column_names) > len(result_df.columns):
                            result_df.columns = column_names[:len(result_df.columns)]
                        else:
                            result_df.columns = column_names + [f"未命名列_{i}" for i in range(len(column_names), len(result_df.columns))]
                    
                    log_global_result("\n合并后数据:")
                    log_global_result(result_df.to_string())
                    
                    log_global_result("\n最终文件数据:")
                    log_global_result(final_df.to_string())
                    
                    # 比较数据
                    is_consistent, comparison_details = compare_dataframes(result_df, final_df)
                    if is_consistent:
                        log_global_result("\n数据一致")
                    else:
                        log_global_result("\n数据不一致")
                        if 'max_difference' in comparison_details:
                            log_global_result(f"最大差异: {comparison_details['max_difference']:.4f}")
                        if 'mean_difference' in comparison_details:
                            log_global_result(f"平均差异: {comparison_details['mean_difference']:.4f}")
                        if 'difference' in comparison_details:
                            log_global_result("差异矩阵:")
                            log_global_result(str(comparison_details['difference']))
                        if 'error' in comparison_details:
                            log_global_result(f"错误信息: {comparison_details['error']}")
            except Exception as e:
                log_global_result(f"数据合并时出错: {e}")
                import traceback
                traceback.print_exc()
                # 如果合并出错，则使用原始的累加方式
                result_df = safe_accumulate_dataframes(dataframes)
                # 设置列名
                if column_names and len(column_names) == len(result_df.columns):
                    result_df.columns = column_names
                elif column_names:
                    # 如果列数不匹配，尝试修复
                    if len(column_names) > len(result_df.columns):
                        result_df.columns = column_names[:len(result_df.columns)]
                    else:
                        result_df.columns = column_names + [f"未命名列_{i}" for i in range(len(column_names), len(result_df.columns))]
                
                log_global_result("\n合并后数据:")
                log_global_result(result_df.to_string())
                
                log_global_result("\n最终文件数据:")
                log_global_result(final_df.to_string())
                
                # 比较数据
                is_consistent, comparison_details = compare_dataframes(result_df, final_df)
                if is_consistent:
                    log_global_result("\n数据一致")
                else:
                    log_global_result("\n数据不一致")
                    if 'max_difference' in comparison_details:
                        log_global_result(f"最大差异: {comparison_details['max_difference']:.4f}")
                    if 'mean_difference' in comparison_details:
                        log_global_result(f"平均差异: {comparison_details['mean_difference']:.4f}")
                    if 'difference' in comparison_details:
                        log_global_result("差异矩阵:")
                        log_global_result(str(comparison_details['difference']))
                    if 'error' in comparison_details:
                        log_global_result(f"错误信息: {comparison_details['error']}")
        else:
            # 对于其他配置，我们需要累加所有数据框
            result_df = safe_accumulate_dataframes(dataframes)
            # 设置列名
            if column_names and len(column_names) == len(result_df.columns):
                result_df.columns = column_names
            elif column_names:
                # 如果列数不匹配，尝试修复
                if len(column_names) > len(result_df.columns):
                    result_df.columns = column_names[:len(result_df.columns)]
                else:
                    result_df.columns = column_names + [f"未命名列_{i}" for i in range(len(column_names), len(result_df.columns))]
            
            log_global_result("\n合并后数据:")
            log_global_result(result_df.to_string())
            
            log_global_result("\n最终文件数据:")
            log_global_result(final_df.to_string())
            
            # 比较数据
            is_consistent, comparison_details = compare_dataframes(result_df, final_df)
            if is_consistent:
                log_global_result("\n数据一致")
            else:
                log_global_result("\n数据不一致")
                if 'max_difference' in comparison_details:
                    log_global_result(f"最大差异: {comparison_details['max_difference']:.4f}")
                if 'mean_difference' in comparison_details:
                    log_global_result(f"平均差异: {comparison_details['mean_difference']:.4f}")
                if 'difference' in comparison_details:
                    log_global_result("差异矩阵:")
                    log_global_result(str(comparison_details['difference']))
                if 'error' in comparison_details:
                    log_global_result(f"错误信息: {comparison_details['error']}")
    else:
        log_global_result("没有可处理的数据")


def safe_accumulate_dataframes(dataframes):
    """
    安全地累加多个DataFrame
    
    Parameters:
    dataframes (list): DataFrame列表
    
    Returns:
    pd.DataFrame: 累加后的DataFrame
    """
    if not dataframes:
        return pd.DataFrame()
    
    # 过滤掉空的DataFrame
    non_empty_dataframes = [df for df in dataframes if not df.empty]
    
    if not non_empty_dataframes:
        return pd.DataFrame()
    
    # 确保所有DataFrame具有相同的列数
    target_columns = non_empty_dataframes[0].shape[1]
    valid_dataframes = [df for df in non_empty_dataframes if df.shape[1] == target_columns]
    
    if not valid_dataframes:
        print("警告: 没有列数一致的DataFrame")
        return pd.DataFrame()
    
    # 从第一个有效的DataFrame开始
    try:
        result = valid_dataframes[0].values.copy()
    except Exception as e:
        print(f"警告: 无法复制第一个DataFrame: {e}")
        return pd.DataFrame()
    
    # 累加其他DataFrame
    for i, df in enumerate(valid_dataframes[1:], 1):
        try:
            # 检查形状是否兼容
            if result.shape[1] == df.values.shape[1]:  # 列数相同
                # 确保行数也兼容，如果不兼容则只处理能兼容的部分
                min_rows = min(result.shape[0], df.values.shape[0])
                if min_rows > 0:
                    result[:min_rows] += df.values[:min_rows]
                # 如果当前DataFrame有更多的行，则添加这些行
                if df.values.shape[0] > result.shape[0]:
                    result = np.vstack([result, df.values[result.shape[0]:]])
            else:
                print(f"警告: 数据形状不匹配，跳过一个DataFrame (形状: {result.shape} vs {df.values.shape})")
        except Exception as e:
            print(f"警告: 累加第{i}个DataFrame时出错: {e}")
    
    # 创建结果DataFrame并保留列名（如果原始DataFrame有列名）
    result_df = pd.DataFrame(result.round(2))
    
    # 如果第一个有效的DataFrame有列名，则设置列名
    if len(valid_dataframes[0].columns) == result_df.shape[1]:
        result_df.columns = valid_dataframes[0].columns
    
    return result_df


def validate_all_sheets():
    """
    验证所有配置的工作表
    """
    log_global_result("开始验证所有工作表...")
    for mapping_name in SHEET_MAPPINGS:
        log_global_result(f"\n{'='*50}")
        log_global_result(f"验证 {mapping_name}")
        log_global_result(f"{'='*50}")
        validate_bond_data(
            BASE_DIRECTORY, 
            SOURCE_FILES, 
            SHEET_MAPPINGS[mapping_name], 
            FINAL_FILE,
            mapping_name
        )


import pandas as pd
import os
import shutil
from config import BASE_DIRECTORY, SHEET_MAPPINGS
from validation import read_and_extract_data

# 全局变量用于收集所有校验结果
validation_results_global = []

def log_global_result(message):
    """记录校验结果到全局列表并打印到控制台"""
    print(message)
    validation_results_global.append(message)

def clear_global_results():
    """清空全局校验结果列表"""
    global validation_results_global
    validation_results_global.clear()


def cross_table_validation():
    """
    跨表校验函数，使用final表数据进行校验，并将结果保存到txt文件中
    """
    print("\n开始跨表校验...")
    
    # 创建一个列表来存储所有输出信息
    validation_results = []
    
    def log_result(message):
        """记录校验结果并打印到控制台"""
        print(message)
        validation_results.append(message)
    
    # 读取各表的final数据
    # 现券持仓表
    position_mapping = SHEET_MAPPINGS['现券持仓']
    position_final_config = position_mapping['final']
    position_column_names = position_mapping.get('col', None)
    position_numeric_columns = position_mapping.get('num_col', None)
    
    # 现券交易表
    trade_mapping = SHEET_MAPPINGS['现券交易']
    trade_final_config = trade_mapping['final']
    trade_column_names = trade_mapping.get('col', None)
    trade_numeric_columns = trade_mapping.get('num_col', None)
    
    # 债券交易杠杆表
    leverage_mapping = SHEET_MAPPINGS['债券交易杠杆']
    leverage_final_config = leverage_mapping['final']
    leverage_column_names = leverage_mapping.get('col', None)
    leverage_numeric_columns = leverage_mapping.get('num_col', None)
    
    # 回购交易表
    repo_mapping = SHEET_MAPPINGS['回购交易']
    repo_final_config = repo_mapping['final']
    repo_column_names = repo_mapping.get('col', None)
    repo_numeric_columns = repo_mapping.get('num_col', None)
    
    # 读取现券持仓final数据
    position_df = read_and_extract_data(
        os.path.join(BASE_DIRECTORY, FINAL_FILE),
        position_final_config['sheet_name'],
        position_final_config['row_range'],
        position_final_config.get('col_range', (0, 10)),
        position_column_names,
        position_numeric_columns
    )
    
    # 读取现券交易final数据
    trade_df = read_and_extract_data(
        os.path.join(BASE_DIRECTORY, FINAL_FILE),
        trade_final_config['sheet_name'],
        trade_final_config['row_range'],
        trade_final_config.get('col_range', (0, 10)),
        trade_column_names,
        trade_numeric_columns
    )
    
    # 读取债券交易杠杆final数据
    leverage_df = read_and_extract_data(
        os.path.join(BASE_DIRECTORY, FINAL_FILE),
        leverage_final_config['sheet_name'],
        leverage_final_config['row_range'],
        leverage_final_config.get('col_range', (0, 10)),
        leverage_column_names,
        leverage_numeric_columns
    )
    
    # 读取回购交易final数据
    repo_df = read_and_extract_data(
        os.path.join(BASE_DIRECTORY, FINAL_FILE),
        repo_final_config['sheet_name'],
        repo_final_config['row_range'],
        repo_final_config.get('col_range', (0, 10)),
        repo_column_names,
        repo_numeric_columns
    )
    
    print("\n校验结果:")
    
    # 1. 校验券面金额和公允价值
    log_result("\n1. 校验券面金额和公允价值:")
    
    # 确保列名正确设置
    if position_column_names and len(position_column_names) == len(position_df.columns):
        position_df.columns = position_column_names
    if trade_column_names and len(trade_column_names) == len(trade_df.columns):
        trade_df.columns = trade_column_names
    if leverage_column_names and len(leverage_column_names) == len(leverage_df.columns):
        leverage_df.columns = leverage_column_names
    if repo_column_names and len(repo_column_names) == len(repo_df.columns):
        repo_df.columns = repo_column_names
    
    # 计算现券持仓表中的期末持有券面金额和公允价值总和
    position_face_value_sum = 0
    position_fair_value_sum = 0
    if '期末持有券面金额（万元）' in position_df.columns:
        position_face_value_sum = position_df['期末持有券面金额（万元）'].sum()
    if '期末持有公允价值（万元）' in position_df.columns:
        position_fair_value_sum = position_df['期末持有公允价值（万元）'].sum()
    
    log_result(f"   现券持仓表 - 期末持有券面金额总和: {position_face_value_sum:.2f}")
    log_result(f"   现券持仓表 - 期末持有公允价值总和: {position_fair_value_sum:.2f}")
    
    # 计算现券交易表中的期末持仓量券面金额和公允价值总和
    trade_face_value_sum = 0
    trade_fair_value_sum = 0
    if '期末持仓量券面金额（万元）' in trade_df.columns:
        trade_face_value_sum = trade_df['期末持仓量券面金额（万元）'].sum()
    if '期末持仓量公允价值（万元）' in trade_df.columns:
        trade_fair_value_sum = trade_df['期末持仓量公允价值（万元）'].sum()
    
    log_result(f"   现券交易表 - 期末持仓量券面金额总和: {trade_face_value_sum:.2f}")
    log_result(f"   现券交易表 - 期末持仓量公允价值总和: {trade_fair_value_sum:.2f}")
    
    # 获取债券交易杠杆表中的券面金额和公允价值
    leverage_face_value = 0
    leverage_fair_value = 0
    if '券面金额（万元）' in leverage_df.columns:
        leverage_face_value = leverage_df['券面金额（万元）'].sum()
    if '公允价值（万元）' in leverage_df.columns:
        leverage_fair_value = leverage_df['公允价值（万元）'].sum()
    
    print(f"   债券交易杠杆表 - 券面金额: {leverage_face_value:.2f}")
    print(f"   债券交易杠杆表 - 公允价值: {leverage_fair_value:.2f}")
    
    # 校验券面金额是否相等
    face_value_equal = abs(position_face_value_sum - trade_face_value_sum) < 0.01 and \
                      abs(position_face_value_sum - leverage_face_value) < 0.01
    log_result(f"   券面金额校验结果: {'通过' if face_value_equal else '不通过'}")
    if not face_value_equal:
        log_result(f"     差异: 现券持仓({position_face_value_sum:.2f}) vs 现券交易({trade_face_value_sum:.2f}) vs 杠杆({leverage_face_value:.2f})")
    
    # 校验公允价值是否相等
    fair_value_equal = abs(position_fair_value_sum - trade_fair_value_sum) < 0.01 and \
                       abs(position_fair_value_sum - leverage_fair_value) < 0.01
    log_result(f"   公允价值校验结果: {'通过' if fair_value_equal else '不通过'}")
    if not fair_value_equal:
        log_result(f"     差异: 现券持仓({position_fair_value_sum:.2f}) vs 现券交易({trade_fair_value_sum:.2f}) vs 杠杆({leverage_fair_value:.2f})")
    
    # 2. 校验回购交易与债券交易杠杆
    log_result("\n2. 校验回购交易与债券交易杠杆:")
    
    # 获取回购交易表第2行和第4行的表内规模
    positive_repo_amount = 0
    negative_repo_amount = 0
    if '表内规模（万元）' in repo_df.columns:
        # 打印调试信息
        print(f"   回购交易表数据形状: {repo_df.shape}")
        print(f"   回购交易表列名: {repo_df.columns.tolist()}")
        print(f"   表内规模列数据:\n{repo_df['表内规模（万元）']}")
        
        # 获取表内规模列的数据
        repo_values = repo_df['表内规模（万元）']
        if len(repo_values) >= 4:
            # 根据回购交易表的配置，第2行和第4行对应索引1和3
            positive_repo_amount = repo_values.iloc[1]  # 第2行（索引1）
            negative_repo_amount = repo_values.iloc[3]  # 第4行（索引3）
        elif len(repo_values) >= 2:
            # 如果只有少量数据，至少获取第一行数据用于调试
            positive_repo_amount = repo_values.iloc[0] if len(repo_values) > 0 else 0
            negative_repo_amount = repo_values.iloc[1] if len(repo_values) > 1 else 0
    
    log_result(f"   回购交易表 - 正回购表内规模（第2行）: {positive_repo_amount:.2f}")
    log_result(f"   回购交易表 - 逆回购表内规模（第4行）: {negative_repo_amount:.2f}")
    
    # 获取债券交易杠杆表中的正回购和逆回购资金余额
    leverage_positive_repo = 0
    leverage_negative_repo = 0
    if '债券正回购资金余额（万元）' in leverage_df.columns:
        leverage_positive_repo = leverage_df['债券正回购资金余额（万元）'].sum()
    if '债券逆回购资金余额（万元）' in leverage_df.columns:
        leverage_negative_repo = leverage_df['债券逆回购资金余额（万元）'].sum()
    
    print(f"   债券交易杠杆表 - 债券正回购资金余额: {leverage_positive_repo:.2f}")
    print(f"   债券交易杠杆表 - 债券逆回购资金余额: {leverage_negative_repo:.2f}")
    
    # 校验正回购资金是否相等
    positive_repo_equal = abs(positive_repo_amount - leverage_positive_repo) < 0.01
    log_result(f"   正回购资金校验结果: {'通过' if positive_repo_equal else '不通过'}")
    if not positive_repo_equal:
        log_result(f"     差异: 回购交易({positive_repo_amount:.2f}) vs 杠杆({leverage_positive_repo:.2f})")
    
    # 校验逆回购资金是否相等
    negative_repo_equal = abs(negative_repo_amount - leverage_negative_repo) < 0.01
    log_result(f"   逆回购资金校验结果: {'通过' if negative_repo_equal else '不通过'}")
    if not negative_repo_equal:
        log_result(f"     差异: 回购交易({negative_repo_amount:.2f}) vs 杠杆({leverage_negative_repo:.2f})")
    
    # 总体校验结果
    overall_result = face_value_equal and fair_value_equal and positive_repo_equal and negative_repo_equal
    log_result(f"\n总体校验结果: {'全部通过' if overall_result else '存在不通过项'}")
    
    # 将校验结果写入txt文件
    try:
        result_path = os.path.join(BASE_DIRECTORY, '校验结果.txt')
        with open(result_path, 'w', encoding='utf-8') as f:
            for line in validation_results:
                f.write(line + '\n')
        log_result(f"\n校验结果已保存到: {result_path}")
    except Exception as e:
        log_result(f"\n保存校验结果到文件时出错: {e}")
    
    return overall_result


def main():
    # 清空全局结果列表
    global validation_results_global
    validation_results_global = []
    
    # 验证所有配置的工作表
    validate_all_sheets()
    
    # 执行跨表校验
    cross_table_validation()


if __name__ == "__main__":
    main()