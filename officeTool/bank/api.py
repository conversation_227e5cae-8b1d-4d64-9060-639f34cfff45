# -- coding: utf-8 --**
import requests
import json
import pandas as pd
from WindPy import *

w.start()
# 取消panda科学计数法,保留4位有效小数位.
pd.set_option('float_format', lambda x: '%.2f' % x)
# 设置中文对齐,数值等宽对齐.
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.max_columns', 20)
pd.set_option('display.width', 500)


class wind_api:
    def __init__(self):
        pass

    def cffex_settlement(self, contract):  # 中金所结算基差
        date = datetime.today().strftime('%Y%m%d')
        settle_data = w.wss(contract, "anal_basis_stkidx,  settle,  pre_settle", "tradeDate=" + date + ";cycle=D;")
        if settle_data.ErrorCode == 0:
            return settle_data.Data[0]

    def code_convert(self, code):  # 转换为wind代码
        wind_code = w.htocode(code, "all")
        if wind_code.ErrorCode == 0:
            return wind_code.Data[0]
        else:
            return None

    def rt_data(self, code):  # 实时行情数据
        w.wsq(code, "rt_latest", func=DemoCallback)

    def warning_stock_pool(self: str = None):  # 预警股票池
        date = (datetime.today() - timedelta(days=1)).strftime('%Y%m%d')
        t_weeks_ago = (datetime.today() - timedelta(days=14)).strftime('%Y%m%d')
        code = w.wset("sectorconstituent", "date=" + date + ";sectorId=a001010100000000;field=date,wind_code")
        code = ','.join(code.Data[1])
        data = w.wss(code, "mkt,  bps_new,  avgclose_per,  vol_nd,  avg_MV_per",
                     "ndays=-18;tradeDate=" + date + ";priceAdj=U;unit=1;days=-118;startDate=" + t_weeks_ago + ";endDate=" + date + ";currencyType=;")
        result = pd.DataFrame(data.Data, index=data.Fields, columns=data.Codes)
        result = result.T
        #print(result)
        return result


class ths_api:
    def __init__(self):
        # Token accessToken 及权限校验机制
        self.getAccessTokenUrl = 'https://quantapi.51ifind.com/api/v1/get_access_token'
        # 获取refresh_token需下载Windows版本接口包解压，打开超级命令-工具-refresh_token查询
        self.refreshtoken = 'eyJzaWduX3RpbWUiOiIyMDI1LTA3LTEwIDE4OjM5OjA1In0=.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.C461009DFCDA55717A10051B3D819E1AAEB46EBEE846694E1D41E56247A33BDF'
        self.getAccessTokenHeader = {"Content-Type": "application/json", "refresh_token": self.refreshtoken}
        self.getAccessTokenResponse = requests.post(url=self.getAccessTokenUrl, headers=self.getAccessTokenHeader)
        self.accessToken = json.loads(self.getAccessTokenResponse.content)['data']['access_token']
        self.thsHeaders = {
            "Content-Type": "application/json",
            "access_token": self.accessToken}

    def error_message(self, error_id):
        thsUrl = 'https://quantapi.51ifind.com/api/v1/get_error_message'
        thsPara = {
            "errorcode": error_id
        }
        thsResponse = requests.post(url=thsUrl, json=thsPara, headers=self.thsHeaders)
        thsData = json.loads(thsResponse.content.decode('utf-8'))['data']
        print(thsData)
        return thsData

    def resolving_data(self, data, data_name):
        code_list = list()
        data_list = list()
        for i in data:
            code = i['thscode']
            demand_data = i['table'][data_name][0]
            code_list.append(code)
            data_list.append(demand_data)
        df = pd.DataFrame({'code': code_list, data_name: data_list})
        return df

    def strategy_query_overview(self, id):  # 策略查询概览
        thsUrl = 'https://quantapi.51ifind.com/api/v1/portfolio_manage'
        thsPara = {
            "func": "query_overview",
            "name": id[0],
            "portfid": id[1],
            "indicators": "category,thscode,stockName,newPrice,increase,increaseRate,number,weight,todayProfit,floatProfit,floatProfitRate,totalProfit,totalProfitRate,interestIncome,realizedProfit,positionPrice,positionCost,breakevenPrice,serviceCharge,moneyType,currentPrices,updateTime,marketValue"
        }
        thsResponse = requests.post(url=thsUrl, json=thsPara, headers=self.thsHeaders)
        thsData = json.loads(thsResponse.content.decode('utf-8'))
        if thsData["errorcode"] == 0:
            table_data = thsData['data']['tableData']['合计']
            stock_data = thsData['data']['tableData']['股票']
            market_values = stock_data['marketValue'] / 10000
            todayProfit = table_data['todayProfit'] / 10000
            initial_values = market_values - todayProfit
            return [initial_values, todayProfit, market_values]
        else:
            print("策略查询概览失败")
            self.error_message(thsData["errorcode"])
            return 0

    def sw_stock(self, code):  # 申万行业分类
        date = datetime.today().strftime('%Y%m%d')
        thsUrl = 'https://quantapi.51ifind.com/api/v1/basic_data_service'
        thsPara = {
            "codes": code,
            "indipara": [{"indicator": "ths_the_sw_industry_stock", "indiparams": ["100", date]}]}
        thsResponse = requests.post(url=thsUrl, json=thsPara, headers=self.thsHeaders)
        thsData = json.loads(thsResponse.content.decode('utf-8'))
        if thsData["errorcode"] == 0:
            return self.resolving_data(thsData['tables'], 'ths_the_sw_industry_stock')
        else:
            self.error_message(thsData["errorcode"])
            return 0

    def sw_bond(self, code):  # 申万债券分类
        date = datetime.today().strftime('%Y%m%d')
        thsUrl = 'https://quantapi.51ifind.com/api/v1/basic_data_service'
        thsPara = {
            "codes": code,
            "indipara": [{"indicator": "ths_object_the_sw_bond", "indiparams": ["100", date]}]}
        thsResponse = requests.post(url=thsUrl, json=thsPara, headers=self.thsHeaders)
        thsData = json.loads(thsResponse.content.decode('utf-8'))
        if thsData['errorcode'] == 0:
            return self.resolving_data(thsData['tables'], 'ths_object_the_sw_bond')
        else:
            self.error_message(thsData["errorcode"])
            return 0

    def bond_issuer(self, code):  # 债券发行人
        thsUrl = 'https://quantapi.51ifind.com/api/v1/basic_data_service'
        thsPara = {
            "codes": code,
            "indipara": [{"indicator": "ths_actual_issuer_bond"}]}
        thsResponse = requests.post(url=thsUrl, json=thsPara, headers=self.thsHeaders)
        thsData = json.loads(thsResponse.content.decode('utf-8'))
        if thsData["errorcode"] == 0:
            return self.resolving_data(thsData['tables'], 'ths_actual_issuer_bond')
        else:
            self.error_message(thsData["errorcode"])
            return 0

    def bond_ths_type(self, code):  # 同花顺债券分类
        thsUrl = 'https://quantapi.51ifind.com/api/v1/basic_data_service'
        thsPara = {
            "codes": code,
            "indipara": [{"indicator": "ths_ths_bond_first_type_bond"}]}
        thsResponse = requests.post(url=thsUrl, json=thsPara, headers=self.thsHeaders)
        thsData = json.loads(thsResponse.content.decode('utf-8'))
        if thsData["errorcode"] == 0:
            return self.resolving_data(thsData['tables'], 'ths_ths_bond_first_type_bond')
        else:
            self.error_message(thsData["errorcode"])
            return 0

    def remain_duration_y_bond(self, code):  # 债券剩余期限（年）
        last_day_of_prev_month = (datetime.now().replace(day=1) - timedelta(days=1)).strftime('%Y%m%d')
        thsUrl = 'https://quantapi.51ifind.com/api/v1/basic_data_service'
        thsPara = {
            "codes": code,
            "indipara": [{"indicator": "ths_remain_duration_y_bond", "indiparams": [last_day_of_prev_month]}]}
        thsResponse = requests.post(url=thsUrl, json=thsPara, headers=self.thsHeaders)
        thsData = json.loads(thsResponse.content.decode('utf-8'))
        if thsData["errorcode"] == 0:
            return self.resolving_data(thsData['tables'], 'ths_remain_duration_y_bond')
        else:
            self.error_message(thsData["errorcode"])
            return 0

    def ths_fund_type_fund(self, code):  # 同花顺基金分类
        thsUrl = 'https://quantapi.51ifind.com/api/v1/basic_data_service'
        thsPara = {
            "codes": code,
            "indipara": [{"indicator": "ths_fund_type_fund"}]}
        thsResponse = requests.post(url=thsUrl, json=thsPara, headers=self.thsHeaders)
        thsData = json.loads(thsResponse.content.decode('utf-8'))
        if thsData["errorcode"] == 0:
            return self.resolving_data(thsData['tables'], 'ths_fund_type_fund')
        else:
            self.error_message(thsData["errorcode"])
            return 0

    def Convertible_Bond_pool(self):  # 可转债代码池
        date = datetime.today().strftime('%Y%m%d')
        thsUrl = 'https://quantapi.51ifind.com/api/v1/data_pool'
        thsPara = {
            "reportname": "p00570",
            "functionpara": {"jyzt": "未到期", "sfdb": "全部", "jysc": "全部", "edate": date},
            "outputpara": "jydm,jydm_mc"}
        thsResponse = requests.post(url=thsUrl, json=thsPara, headers=self.thsHeaders)
        thsData = json.loads(thsResponse.content.decode('utf-8'))
        if thsData["errorcode"] == 0:
            convertible_bond_code = thsData['tables'][0]['table']['jydm']
            convertible_bond_name = thsData['tables'][0]['table']['jydm_mc']
            df = pd.DataFrame({'code': convertible_bond_code, 'name': convertible_bond_name})
            return df
        else:
            self.error_message(thsData["errorcode"])
            return 0


def main():
    strategy = ths_api()
    wind = wind_api()
    # wind.warning_stock_pool()

    # 分策略数据
    strategy_id = [["500无对冲0.01", 164688], ["500untiny", 286909], ["1000untiny", 286910], ["1000价值无对冲", 312736],["空气指增0.005",348151,]]
    for i in strategy_id:
        data = strategy.strategy_query_overview(i)
        print(data)

    '''    # 债券发行人
    sw_bond = strategy.bond_issuer(
        '012482662.IB,032280455.IB,032381012.IB,032381105.IB,032381235.IB,032381247.IB,032400598.IB,032480998.IB,092280015.IB,102000778.IB,102000884.IB,102100548.IB,102101145.IB,102102193.IB,102383057.IB,102383214.IB')
    print(sw_bond)

    # wind股指期货数据
    a = wind.cffex_settlement("IC2412.CFE")
    # wind 代码转换
    b = wind.code_convert("159949")
    print(a, b)
    # 债券-申万行业
    sw = strategy.sw_bond('128081.SZ')
    print(sw)

       # 债券-债券类型
    bond_type = strategy.bond_ths_type('128081.SZ')
    print(bond_type)
    # 债券剩余期限
    bond_duraiton = strategy.remain_duration_y_bond('128081.SZ')
    print(bond_duraiton)
    # 基金-基金类型
    fund_type = strategy.ths_fund_type_fund('000001.OF')
    print(fund_type)
    # 可转债池
    c_bond = strategy.Convertible_Bond_pool()
    print(c_bond)



    # 申万行业数据
    sw = strategy.sw_stock('601360.SH')
    print(sw)'''


if __name__ == '__main__':
    main()
