# -*- coding: utf-8 -*-
"""
@Time    : 2024/11/24 10:43
<AUTHOR> Lyn
@File    : bankdatav2.py
"""

from api import ths_api
import pandas as pd
import warnings
from typing import Dict, List, Union, Optional, Tuple, Any

warnings.filterwarnings("ignore")


class BankDataAnalyzer:
    """银行同业投资数据分析类"""

    def __init__(self, file_path: str):
        """初始化数据分析器

        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        try:
            self.excel_data = pd.read_excel(file_path, sheet_name=None, dtype={'证券品种编码': str})
        except Exception as e:
            raise ValueError(f"无法读取Excel文件: {e}")
        self.ths_api = ths_api()

    def calculate_asset_value(self, asset_data: pd.DataFrame) -> float:
        """计算资产价值（单位：元）

        Args:
            asset_data: 包含期末余额的DataFrame

        Returns:
            资产价值（万元）
        """
        # 删除首列不含数据的行
        clean_data = asset_data.dropna(axis=0, how='any')
        asset_value_in_wan = clean_data['期末余额'].sum()
        return asset_value_in_wan

    def get_ths_data(self, security_code: str) -> pd.DataFrame:
        """获取同花顺数据

        Args:
            security_code: 证券代码，逗号分隔

        Returns:
            合并后的同花顺数据
        """
        if not security_code:
            return pd.DataFrame()

        try:
            bond_issuer_data = self.ths_api.bond_issuer(security_code)
            bond_sw_data = self.ths_api.sw_bond(security_code)
            bond_duration_data = self.ths_api.remain_duration_y_bond(security_code)
            bond_type_data = self.ths_api.bond_ths_type(security_code)
            fund_type_data = self.ths_api.ths_fund_type_fund(security_code)

            merged_data = pd.merge(bond_issuer_data, bond_duration_data, on='code', how='outer')
            merged_data = pd.merge(merged_data, bond_type_data, on='code', how='outer')
            merged_data = pd.merge(merged_data, fund_type_data, on='code', how='outer')
            merged_data = pd.merge(merged_data, bond_sw_data, on='code', how='outer')
            return merged_data
        except Exception as e:
            print(f"获取同花顺数据失败: {e}")
            return pd.DataFrame()

    def process_holding_bond(self, bond_data: pd.DataFrame) -> pd.DataFrame:
        """处理持有债券数据，添加交易所信息和同花顺数据

        Args:
            bond_data: 债券数据

        Returns:
            处理后的债券数据
        """
        if bond_data.empty:
            return pd.DataFrame()

        # 去掉证券品种编码小数点右侧的字符串
        processed_data = bond_data.copy()

        # 确保'证券品种编码'列存在
        if '证券品种编码' not in processed_data.columns:
            print("警告: 数据中缺少'证券品种编码'列")
            return processed_data

        # processed_data['证券品种编码'] = processed_data['证券品种编码'].astype(str).str.split('.').str[0]
        # 初始化code列
        processed_data['code'] = ''

        # 根据市场名称添加交易所信息
        exchange_suffix_map = {
            '银行间': '.IB',
            '上海交易所': '.SH',
            '深圳交易所': '.SZ'
        }

        # 确保'市场名称'列存在
        if '市场名称' not in processed_data.columns:
            print("警告: 数据中缺少'市场名称'列")
            processed_data['code'] = processed_data['证券品种编码']
        else:
            # for market_name, exchange_suffix in exchange_suffix_map.items():
            #    market_filter = processed_data['市场名称'].str.contains(market_name, na=False)
            processed_data.loc[:, 'code'] = processed_data.loc[:, '证券品种编码']

            # 获取并合并同花顺数据
        if not processed_data['code'].empty:
            unique_security_codes = ','.join(set(filter(None, processed_data['code'].tolist())))
            if unique_security_codes:
                ths_market_data = self.get_ths_data(unique_security_codes)
                enriched_data = pd.merge(processed_data, ths_market_data, on='code', how='left')
                return enriched_data

        return processed_data

    def analyze_holding_duration(self, bond_data: pd.DataFrame) -> List[float]:
        """按剩余期限划分债券

        Args:
            bond_data: 债券数据

        Returns:
            各期限区间的债券余额列表 [0-1年, 1-5年, 5年以上]
        """
        if bond_data.empty:
            return [0.0, 0.0, 0.0]

        duration_analysis_data = bond_data.copy()

        # 检查必要的列是否存在
        if 'ths_remain_duration_y_bond' not in duration_analysis_data.columns:
            print("警告: 数据中缺少'ths_remain_duration_y_bond'列")
            return [0.0, 0.0, 0.0]

        if '期末余额' not in duration_analysis_data.columns:
            print("警告: 数据中缺少'期末余额'列")
            return [0.0, 0.0, 0.0]

        # 安全地转换为浮点数，处理可能的NaN值
        duration_analysis_data['ths_remain_duration_y_bond'] = pd.to_numeric(
            duration_analysis_data['ths_remain_duration_y_bond'],
            errors='coerce'
        ).fillna(0)

        duration_analysis_data['duration_category'] = pd.cut(
            duration_analysis_data['ths_remain_duration_y_bond'],
            bins=[0, 1, 5, 100],
            labels=['0-1', '1-5', '5+'],
            include_lowest=True
        )

        duration_summary = duration_analysis_data.groupby('duration_category')['期末余额'].sum()

        # 确保所有类别都有值，即使是0
        for category in ['0-1', '1-5', '5+']:
            if category not in duration_summary.index:
                duration_summary[category] = 0.0

        return duration_summary.sort_index().tolist()

    def split_holdings_by_type(self, bond_data: pd.DataFrame) -> List[List[float]]:
        """拆分金融债、同业存单、ABS和其他债券

        Args:
            bond_data: 债券数据

        Returns:
            按类型拆分的债券数据列表
        """
        bond_type_results = []
        # 筛选金融机构发行的债券
        financial_institution_bonds = bond_data[
            bond_data['ths_object_the_sw_bond'].str.contains('银行|非银金融', na=False)]

        # 按债券类型拆分
        bond_type_categories = {
            '金融债': financial_institution_bonds[
                financial_institution_bonds['ths_ths_bond_first_type_bond'].str.contains('金融债|可转债', na=False)],
            '同业存单': financial_institution_bonds[
                financial_institution_bonds['ths_ths_bond_first_type_bond'].str.contains('同业存单', na=False)],
            'ABS': financial_institution_bonds[
                financial_institution_bonds['ths_ths_bond_first_type_bond'].str.contains('资产支持证券', na=False)],
            '其他': financial_institution_bonds[
                ~financial_institution_bonds['ths_ths_bond_first_type_bond'].str.contains(
                    '金融债|可转债|同业存单|资产支持证券', na=False)]
        }

        # 计算每种类型的总额和期限分布
        for type_specific_bonds in bond_type_categories.values():
            type_summary = [type_specific_bonds['期末余额'].sum()] + self.analyze_holding_duration(type_specific_bonds)
            bond_type_results.append(type_summary)

        return bond_type_results

    def analyze_holdings_by_issuer(self, bond_data: pd.DataFrame) -> pd.Series:
        """按发行人划分债券持仓

        Args:
            bond_data: 债券数据

        Returns:
            按发行人分组的债券余额
        """
        financial_institution_bonds = bond_data[
            bond_data['ths_object_the_sw_bond'].str.contains('银行|非银金融', na=False)]
        issuer_balance_summary = financial_institution_bonds.groupby('ths_actual_issuer_bond')[
            '期末余额'].sum().sort_values(ascending=False)
        return issuer_balance_summary

    def split_income_by_type(self, income_data: pd.DataFrame, income_column: str) -> List[float]:
        """按债券类型拆分收益数据

        Args:
            income_data: 债券数据
            income_column: 收益列名

        Returns:
            按类型拆分的收益数据列表
        """
        financial_institution_income = income_data[
            income_data['ths_object_the_sw_bond'].str.contains('银行|非银金融', na=False)]

        # 按债券类型拆分收益
        financial_bond_income = financial_institution_income.loc[
            financial_institution_income['ths_ths_bond_first_type_bond'].str.contains('金融债|可转债', na=False)][
            income_column].sum()
        negotiable_cd_income = financial_institution_income.loc[
            financial_institution_income['ths_ths_bond_first_type_bond'].str.contains('同业存单', na=False)][
            income_column].sum()
        abs_income = financial_institution_income.loc[
            financial_institution_income['ths_ths_bond_first_type_bond'].str.contains('资产支持证券', na=False)][
            income_column].sum()
        other_bond_income = financial_institution_income.loc[
            ~financial_institution_income['ths_ths_bond_first_type_bond'].str.contains(
                '金融债|可转债|同业存单|资产支持证券', na=False)][income_column].sum()

        return [financial_bond_income or 0, negotiable_cd_income or 0, abs_income or 0, other_bond_income or 0]

    def analyze_fund_data(self) -> Tuple[float, float, float, float]:
        """分析基金和资管产品数据

        Returns:
            (买入返售金额, 公募基金金额, 私募基金金额, 资管产品金额)
        """
        # 买入返售
        repo_amount = self.calculate_asset_value(self.excel_data['买入返售-逆回购'])

        # 交易性基金（公募）
        public_fund_amount = self.calculate_asset_value(self.excel_data['交易性基金（公募）'])

        # 私募/资管合并校验
        asset_management_data = self.excel_data['交易性-其他（资管产品）']
        private_fund_amount = self.calculate_asset_value(
            asset_management_data[asset_management_data['证券品种名称'].str.contains('私募')])
        asset_management_amount = self.calculate_asset_value(
            asset_management_data[asset_management_data['证券品种名称'].str.contains('资管|单一|资产管理')])
        total_fund_amount = self.calculate_asset_value(asset_management_data)

        # 校验资管产品项
        if private_fund_amount + asset_management_amount != total_fund_amount:
            print('请校验资管产品项')
            asset_management_amount = self.calculate_asset_value(
                asset_management_data[~asset_management_data['证券品种名称'].str.contains('私募')])

        return repo_amount, public_fund_amount, private_fund_amount, asset_management_amount

    def analyze_bond_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series]:
        """分析债券数据

        Returns:
            (债券持仓数据, 债券类型拆分数据, 发行人分析数据)
        """
        # 合并交易性债券和其他债权投资
        combined_bond_assets = pd.concat([
            self.excel_data['交易性债券'].dropna(axis=0, how='any'),
            self.excel_data['其他债权投资-债券'].dropna(axis=0, how='any')
        ])

        # 处理债券持仓数据
        bond_holdings = self.process_holding_bond(combined_bond_assets)

        # 按债券类型拆分
        bond_holdings_by_type = self.split_holdings_by_type(bond_holdings)
        bond_holdings_by_type_df = pd.DataFrame(
            bond_holdings_by_type,
            columns=['期末余额', '0-1', '1-5', '5+'],
            index=['金融债', '同业存单', 'ABS', '其他']
        )

        # 按发行人拆分
        bond_holdings_by_issuer = self.analyze_holdings_by_issuer(bond_holdings)

        return bond_holdings, bond_holdings_by_type_df, bond_holdings_by_issuer

    def analyze_income_data(self) -> pd.DataFrame:
        """分析收益数据

        Returns:
            按类型拆分的收益数据，DataFrame格式，包含投资收益、公允价值变动损益和合计三列
        """

        # 安全获取各类收益数据
        def safe_get_data(sheet_name: str) -> pd.DataFrame:
            try:
                return self.excel_data.get(sheet_name, pd.DataFrame()).dropna(axis=0, how='any')
            except Exception as e:
                print(f"获取{sheet_name}数据失败: {e}")
                return pd.DataFrame()

        bond_investment_income = safe_get_data('投资收益-债券')
        public_fund_investment_income = safe_get_data('投资收益-基金（公募）')
        private_fund_investment_income = safe_get_data('投资收益-资管产品（私募）')
        bond_fair_value_income = safe_get_data('公允价值变动损益-债券')
        public_fund_fair_value_income = safe_get_data('公允价值变动损益-基金')
        private_fund_fair_value_income = safe_get_data('公允价值变动损益-资管产品')

        # 处理债券收益数据
        processed_bond_investment_income = self.process_holding_bond(bond_investment_income)
        processed_bond_fair_value_income = self.process_holding_bond(bond_fair_value_income)

        # 按债券类型拆分收益
        bond_investment_by_type = self.split_income_by_type(processed_bond_investment_income, '贷方累计')
        bond_fair_value_by_type = self.split_income_by_type(processed_bond_fair_value_income, '贷方累计')

        # 确保两个列表长度相同
        max_len = max(len(bond_investment_by_type), len(bond_fair_value_by_type))
        bond_investment_by_type.extend([0] * (max_len - len(bond_investment_by_type)))
        bond_fair_value_by_type.extend([0] * (max_len - len(bond_fair_value_by_type)))

        # 安全计算基金收益
        def safe_sum_with_filter(df: pd.DataFrame, filter_str: str, column: str) -> float:
            if df.empty or '证券品种名称' not in df.columns or column not in df.columns:
                return 0.0
            return df.loc[df['证券品种名称'].str.contains(filter_str, na=False)][column].sum()

        # 计算投资收益
        private_fund_investment = safe_sum_with_filter(private_fund_investment_income, '私募', '贷方累计')
        asset_management_investment = private_fund_investment_income.loc[:, '贷方累计'].sum() - private_fund_investment
        public_fund_investment = public_fund_investment_income[
            '贷方累计'].sum() if not public_fund_investment_income.empty and '贷方累计' in public_fund_investment_income.columns else 0.0

        # 计算公允价值变动损益
        private_fund_fair_value = safe_sum_with_filter(private_fund_fair_value_income, '私募', '贷方累计')
        asset_management_fair_value = safe_sum_with_filter(private_fund_fair_value_income, '资管|单一|资产管理',
                                                           '贷方累计')
        public_fund_fair_value = public_fund_fair_value_income[
            '贷方累计'].sum() if not public_fund_fair_value_income.empty and '贷方累计' in public_fund_fair_value_income.columns else 0.0

        # 创建投资收益列表
        investment_income_list = bond_investment_by_type.copy()
        # 将基金收益插入到投资收益数据中
        if len(investment_income_list) >= 2:
            investment_income_list.insert(-2, public_fund_investment)
            investment_income_list.insert(-2, private_fund_investment)
            investment_income_list.insert(-2, asset_management_investment)
        else:
            investment_income_list.extend(
                [public_fund_investment, private_fund_investment, asset_management_investment, 0, 0])

        # 创建公允价值变动损益列表
        fair_value_income_list = bond_fair_value_by_type.copy()
        # 将基金公允价值变动插入到公允价值变动数据中
        if len(fair_value_income_list) >= 2:
            fair_value_income_list.insert(-2, public_fund_fair_value)
            fair_value_income_list.insert(-2, private_fund_fair_value)
            fair_value_income_list.insert(-2, asset_management_fair_value)
        else:
            fair_value_income_list.extend(
                [public_fund_fair_value, private_fund_fair_value, asset_management_fair_value, 0, 0])

        # 计算合计列
        total_income_list = [investment_income_list[i] + fair_value_income_list[i] for i in
                             range(len(investment_income_list))]

        # 创建DataFrame
        income_df = pd.DataFrame({
            '投资收益': investment_income_list,
            '公允价值变动损益': fair_value_income_list,
            '合计': total_income_list
        }, index=['金融债', '同业存单', '公募', '私募', '资管', 'ABS', '其他'])

        return income_df

    def analyze_credit_impairment(self) -> pd.Series:
        """分析信用减值数据

        Returns:
            按类型拆分的信用减值数据
        """
        credit_impairment_data = self.excel_data['信用减值损失（累计数）'].dropna(axis=0, how='any')
        processed_impairment_data = self.process_holding_bond(credit_impairment_data)
        impairment_by_bond_type = self.split_income_by_type(processed_impairment_data, '信用减值损失')
        return pd.Series(impairment_by_bond_type, index=['金融债', '同业存单', 'ABS', '其他'])

    def run_analysis(self) -> None:
        """运行完整分析流程并保存结果"""
        # 分析基金数据
        repo_amount, public_fund_amount, private_fund_amount, asset_management_amount = self.analyze_fund_data()

        # 输出基金数据
        print("买方返售-非银行金融机构", repo_amount,
              "\n公募基金期末余额", public_fund_amount,
              "\n私募基金期末余额", private_fund_amount,
              "\n资管产品期末余额", asset_management_amount)

        # 创建基金数据汇总表
        fund_summary_table = pd.DataFrame({
            "买方返售-非银行金融机构": repo_amount,
            "公募基金期末余额": public_fund_amount,
            "私募基金期末余额": private_fund_amount,
            "资管产品期末余额": asset_management_amount
        }
            , index=[0])

        # 分析债券数据
        bond_holdings, bond_holdings_by_type_df, bond_holdings_by_issuer = self.analyze_bond_data()

        # 输出债券数据
        print("\n\n债券同业投资期末余额及剩余期限拆分\n\n", bond_holdings_by_type_df,
              "\n\n发行人规模排序\n\n", bond_holdings_by_issuer.head(10))

        # 分析收益数据
        income_summary = self.analyze_income_data()
        print("\n\n投资及公允变动收益汇总\n", income_summary)

        # 分析信用减值数据
        credit_impairment_summary = self.analyze_credit_impairment()

        # 保存数据
        with pd.ExcelWriter('风险同业-财务相关数据(校验).xlsx') as writer:
            bond_holdings.to_excel(writer, sheet_name='交易性其他债券期末余额', index=False)
            self.process_holding_bond(self.excel_data['投资收益-债券'].dropna(axis=0, how='any')).to_excel(
                writer, sheet_name='投资收益-债券', index=False)
            self.process_holding_bond(self.excel_data['公允价值变动损益-债券'].dropna(axis=0, how='any')).to_excel(
                writer, sheet_name='公允价值变动损益-债券', index=False)
            self.process_holding_bond(self.excel_data['信用减值损失（累计数）'].dropna(axis=0, how='any')).to_excel(
                writer, sheet_name='信用减值损失（累计数）', index=False)
            bond_holdings_by_type_df.to_excel(writer, sheet_name='债券同业投资期末余额及剩余期限拆分')
            bond_holdings_by_issuer.to_excel(writer, sheet_name='发行人规模排序')
            fund_summary_table.to_excel(writer, sheet_name='同业投资期末余额汇总表', index=False)
            income_summary.to_excel(writer, sheet_name='投资及公允变动收益汇总')
            credit_impairment_summary.to_excel(writer, sheet_name='信用减值损失汇总表')


def main():
    """主函数"""
    try:
        bank_analyzer = BankDataAnalyzer('风险同业-财务相关数据-2506.xls')
        bank_analyzer.run_analysis()
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
